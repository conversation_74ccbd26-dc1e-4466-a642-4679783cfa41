{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-escape-keydown/index.ts"], "sourcesContent": ["import { onBeforeUnmount, onMounted } from 'vue'\nimport { isClient } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\n\nlet registeredEscapeHandlers: ((e: KeyboardEvent) => void)[] = []\n\nconst cachedHandler = (event: KeyboardEvent) => {\n  if (event.code === EVENT_CODE.esc) {\n    registeredEscapeHandlers.forEach((registeredHandler) =>\n      registeredHandler(event)\n    )\n  }\n}\n\nexport const useEscapeKeydown = (handler: (e: KeyboardEvent) => void) => {\n  onMounted(() => {\n    if (registeredEscapeHandlers.length === 0) {\n      document.addEventListener('keydown', cachedHandler)\n    }\n    if (isClient) registeredEscapeHandlers.push(handler)\n  })\n\n  onBeforeUnmount(() => {\n    registeredEscapeHandlers = registeredEscapeHandlers.filter(\n      (registeredHandler) => registeredHandler !== handler\n    )\n    if (registeredEscapeHandlers.length === 0) {\n      if (isClient) document.removeEventListener('keydown', cachedHandler)\n    }\n  })\n}\n"], "names": ["EVENT_CODE", "onMounted", "isClient", "onBeforeUnmount"], "mappings": ";;;;;;;;AAGA,IAAI,wBAAwB,GAAG,EAAE,CAAC;AAClC,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACjC,EAAE,IAAI,KAAK,CAAC,IAAI,KAAKA,eAAU,CAAC,GAAG,EAAE;AACrC,IAAI,wBAAwB,CAAC,OAAO,CAAC,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;AACtF,GAAG;AACH,CAAC,CAAC;AACU,MAAC,gBAAgB,GAAG,CAAC,OAAO,KAAK;AAC7C,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAI,IAAI,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/C,MAAM,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAIC,aAAQ;AAChB,MAAM,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAEC,mBAAe,CAAC,MAAM;AACxB,IAAI,wBAAwB,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC,iBAAiB,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC;AACrH,IAAI,IAAI,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/C,MAAM,IAAID,aAAQ;AAClB,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC/D,KAAK;AACL,GAAG,CAAC,CAAC;AACL;;;;"}