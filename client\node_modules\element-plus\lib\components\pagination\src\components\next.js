'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../../utils/vue/props/runtime.js');
var icon = require('../../../../utils/vue/icon.js');

const paginationNextProps = runtime.buildProps({
  disabled: Boolean,
  currentPage: {
    type: Number,
    default: 1
  },
  pageCount: {
    type: Number,
    default: 50
  },
  nextText: {
    type: String
  },
  nextIcon: {
    type: icon.iconPropType
  }
});

exports.paginationNextProps = paginationNextProps;
//# sourceMappingURL=next.js.map
