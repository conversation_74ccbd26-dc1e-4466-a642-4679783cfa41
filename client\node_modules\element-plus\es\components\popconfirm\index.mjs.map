{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/popconfirm/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Popconfirm from './src/popconfirm.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPopconfirm: SFCWithInstall<typeof Popconfirm> =\n  withInstall(Popconfirm)\nexport default ElPopconfirm\n\nexport * from './src/popconfirm'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}