{"version": 3, "file": "alpha-slider.js", "sources": ["../../../../../../../packages/components/color-picker/src/components/alpha-slider.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <div ref=\"bar\" :class=\"barKls\" :style=\"barStyle\" @click=\"handleClick\" />\n    <div\n      ref=\"thumb\"\n      :class=\"thumbKls\"\n      :style=\"thumbStyle\"\n      :aria-label=\"alphaLabel\"\n      :aria-valuenow=\"alpha\"\n      :aria-orientation=\"vertical ? 'vertical' : 'horizontal'\"\n      aria-valuemin=\"0\"\n      aria-valuemax=\"100\"\n      role=\"slider\"\n      tabindex=\"0\"\n      @keydown=\"handleKeydown\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { alphaSliderProps } from '../props/alpha-slider'\nimport {\n  useAlphaSlider,\n  useAlphaSliderDOM,\n} from '../composables/use-alpha-slider'\n\nconst COMPONENT_NAME = 'ElColorAlphaSlider'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(alphaSliderProps)\n\nconst {\n  alpha,\n  alphaLabel,\n  bar,\n  thumb,\n  handleDrag,\n  handleClick,\n  handleKeydown,\n} = useAlphaSlider(props)\n\nconst { rootKls, barKls, barStyle, thumbKls, thumbStyle, update } =\n  useAlphaSliderDOM(props, {\n    bar,\n    thumb,\n    handleDrag,\n  })\n\ndefineExpose({\n  /**\n   * @description update alpha slider manually\n   * @type {Function}\n   */\n  update,\n  /**\n   * @description bar element ref\n   * @type {HTMLElement}\n   */\n  bar,\n  /**\n   * @description thumb element ref\n   * @type {HTMLElement}\n   */\n  thumb,\n})\n</script>\n"], "names": ["useAlphaSlider", "useAlphaSliderDOM", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_normalizeStyle"], "mappings": ";;;;;;;;;;uCA4Bc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAIA,IAAM,MAAA;AAAA,MACJ,KAAA;AAAA,MACA,UAAA;AAAA,MACA,GAAA;AAAA,MACA,KAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,KACF,GAAIA,8BAAe,KAAK,CAAA,CAAA;AAExB,IAAM,MAAA,EAAE,SAAS,MAAQ,EAAA,QAAA,EAAU,UAAU,UAAY,EAAA,MAAA,EACvD,GAAAC,gCAAA,CAAkB,KAAO,EAAA;AAAA,MACvB,GAAA;AAAA,MACA,KAAA;AAAA,MACA,UAAA;AAAA,KACD,CAAA,CAAA;AAEH,IAAa,MAAA,CAAA;AAAA,MAAA,MAAA;AAAA,MAAA,GAAA;AAAA,MAAA,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAKX,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,OAAA,CAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,UAKA,OAAA,EAAA,KAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,KAAA,EAAAF,kBAAA,CAAAC,SAAA,CAAA,MAAA,CAAA,CAAA;AAAA,UAAA,KAAA,EAAAE,kBAAA,CAAAF,SAAA,CAAA,QAAA,CAAA,CAAA;AAAA,UAAA,OAAA,EAAAA,SAAA,CAAA,WAAA,CAAA;AAAA,SAKA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QACDC,sBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;"}