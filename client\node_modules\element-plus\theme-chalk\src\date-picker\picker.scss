@use 'sass:map';

@use '../mixins/mixins' as *;
@use '../mixins/var' as *;
@use '../common/var' as *;
@use '../common/transition' as *;

@include b(picker) {
  @include e(popper) {
    @include set-css-var-type('datepicker', 'border-color', $datepicker);
    @include picker-popper(
      getCssVar('bg-color', 'overlay'),
      1px solid getCssVar('datepicker', 'border-color'),
      getCssVar('box-shadow', 'light')
    );
  }
}

@include b(date-editor) {
  @include set-component-css-var('date-editor', $date-editor);
  @include set-component-css-var('input', $input);
  position: relative;
  text-align: left;
  vertical-align: middle;

  &.#{$namespace}-input__wrapper {
    @include inset-input-border(
      var(
        #{getCssVarName('input-border-color')},
        map.get($input, 'border-color')
      )
    );

    &:hover {
      @include inset-input-border(#{getCssVar('input', 'hover-border-color')});
    }
  }

  &.#{$namespace}-input,
  &.#{$namespace}-input__wrapper {
    width: getCssVar('date-editor-width');
    height: var(#{getCssVarName('input-height')}, getCssVar('component-size'));
  }

  @include m((monthrange)) {
    @include css-var-from-global(
      'date-editor-width',
      'date-editor-monthrange-width'
    );
  }

  @include m((daterange, timerange)) {
    @include css-var-from-global(
      'date-editor-width',
      'date-editor-daterange-width'
    );
  }

  @include m(datetimerange) {
    @include css-var-from-global(
      'date-editor-width',
      'date-editor-datetimerange-width'
    );
  }

  @include m(dates) {
    .#{$namespace}-input__wrapper {
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .close-icon {
    cursor: pointer;
  }

  .clear-icon {
    cursor: pointer;

    &:hover {
      color: getCssVar('input-clear-hover-color');
    }
  }

  .#{$namespace}-range__icon {
    height: inherit;
    font-size: 14px;
    color: getCssVar('text-color', 'placeholder');
    float: left;

    svg {
      vertical-align: middle;
    }
  }

  .#{$namespace}-range-input {
    appearance: none;
    border: none;
    outline: none;
    display: inline-block;
    height: #{map.get($input-height, 'default') - 2};
    line-height: #{map.get($input-height, 'default') - 2};
    margin: 0;
    padding: 0;
    width: 39%;
    text-align: center;
    font-size: getCssVar('font-size', 'base');
    color: getCssVar('text-color', 'regular');
    background-color: transparent;

    &::placeholder {
      color: getCssVar('text-color', 'placeholder');
    }
  }

  .#{$namespace}-range-separator {
    flex: 1;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 0 5px;
    margin: 0;
    font-size: 14px;
    overflow-wrap: break-word;
    color: getCssVar('text-color', 'primary');
  }

  .#{$namespace}-range__close-icon {
    font-size: 14px;
    color: getCssVar('text-color', 'placeholder');
    height: inherit;
    width: unset;
    cursor: pointer;

    &:hover {
      color: getCssVar('input-clear-hover-color');
    }

    svg {
      vertical-align: middle;
    }

    &--hidden {
      opacity: 0;
      visibility: hidden;
    }
  }
}

@include b(range-editor) {
  &.#{$namespace}-input__wrapper {
    display: inline-flex;
    align-items: center;
    padding: 0 10px;
    vertical-align: middle;
  }

  @include when(active) {
    @include inset-input-border(#{getCssVar('input', 'focus-border-color')});
    &:hover {
      @include inset-input-border(#{getCssVar('input', 'focus-border-color')});
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      line-height: getCssVar('component-size', $size);

      &.#{$namespace}-input__wrapper {
        height: getCssVar('component-size', $size);
      }

      .#{$namespace}-range-separator {
        line-height: map.get($input-line-height, $size);
        font-size: map.get($input-font-size, $size);
      }

      .#{$namespace}-range-input {
        height: #{map.get($input-height, $size) - 2};
        line-height: #{map.get($input-height, $size) - 2};
        font-size: map.get($input-font-size, $size);
      }
    }
  }

  @include when(disabled) {
    background-color: map.get($input-disabled, 'fill');
    border-color: map.get($input-disabled, 'border');
    color: map.get($input-disabled, 'text-color');
    cursor: not-allowed;

    &:hover,
    &:focus {
      border-color: map.get($input-disabled, 'border');
    }

    input {
      background-color: map.get($input-disabled, 'fill');
      color: map.get($input-disabled, 'text-color');
      cursor: not-allowed;
      &::placeholder {
        color: map.get($input-disabled, 'placeholder-color');
      }
    }

    .#{$namespace}-range-separator {
      color: map.get($input-disabled, 'text-color');
    }
  }
}
