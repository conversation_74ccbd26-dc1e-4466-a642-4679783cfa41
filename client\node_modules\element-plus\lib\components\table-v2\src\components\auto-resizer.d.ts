declare const AutoResizer: import("vue").DefineComponent<{
    readonly disableWidth: BooleanConstructor;
    readonly disableHeight: BooleanConstructor;
    readonly onResize: {
        readonly type: import("vue").PropType<(event: {
            height: number;
            width: number;
        }) => void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly disableWidth: BooleanConstructor;
    readonly disableHeight: BooleanConstructor;
    readonly onResize: {
        readonly type: import("vue").PropType<(event: {
            height: number;
            width: number;
        }) => void>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {
    readonly disableWidth: boolean;
    readonly disableHeight: boolean;
}>;
export default AutoResizer;
