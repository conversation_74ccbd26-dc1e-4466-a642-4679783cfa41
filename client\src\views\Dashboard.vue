<template>
  <div class="dashboard-container">
    <el-container class="full-height">
      <!-- 侧边栏（高度撑满） -->
      <el-aside width="250px" class="sidebar full-height">
        <div class="sidebar-header">
          <h2>驴肉美食管理</h2>
        </div>
        
        <el-menu
          :default-active="activeTab === 'dashboard' ? '/dashboard' : '/dashboard/' + activeTab"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          @select="handleMenuSelect"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>首页概览</span>
          </el-menu-item>
          
          <el-sub-menu index="1">
            <template #title>
              <el-icon><Food /></el-icon>
              <span>菜品管理</span>
            </template>
            <el-menu-item index="/dashboard/dishes">菜品列表</el-menu-item>
            <el-menu-item index="/dashboard/categories">分类管理</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="2">
            <template #title>
              <el-icon><ShoppingCart /></el-icon>
              <span>订单管理</span>
            </template>
            <el-menu-item index="/dashboard/orders">订单列表</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="4">
            <template #title>
              <el-icon><ShoppingCart /></el-icon>
              <span>产品管理</span>
            </template>
            <el-menu-item index="/dashboard/product">产品管理</el-menu-item>
            <el-menu-item index="/dashboard/stock">库存管理</el-menu-item>
            <el-menu-item index="/dashboard/customer">客户管理</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/dashboard/settings">
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container class="full-height main-bg">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>首页</el-breadcrumb-item>
              <el-breadcrumb-item>管理后台</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :icon="UserFilled" />
                <span class="username">{{ user?.username || '管理员' }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main-content">
          <keep-alive>
            <component :is="activeComponent" v-if="activeTab === 'dashboard'" />
          </keep-alive>
          <component :is="activeComponent" v-if="activeTab !== 'dashboard'" />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  House, 
  Food, 
  ShoppingCart, 
  User, 
  Setting, 
  UserFilled, 
  ArrowDown,
  Document
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import ProductManage from './ProductManage.vue'
import StockManage from './StockManage.vue'
import CustomerManage from './CustomerManage.vue'
import DashboardHome from './DashboardHome.vue'
import OrderManage from './OrderManage.vue'
import DishesManage from './DishesManage.vue'
import CategoriesManage from './CategoriesManage.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const user = computed(() => authStore.user)
const activeTab = ref('dashboard')

// 统计卡片数据
const statCards = [
  { value: '￥546,000', label: '今日支付订单' },
  { value: '￥156,092', label: '今日未支付订单' },
  { value: '￥702,092', label: '今日订单' },
  { value: '￥6,123,098', label: '本月已收账款' },
  { value: '￥1,345,602', label: '本月未收账款' },
  { value: '￥7,468,700', label: '本月应收账款' }
]

// 品牌购买统计表格数据
const brandTableData = [
  { brand: '五香驴肉', today: 120, month: 3600, total: 43200 },
  { brand: '酱驴肉', today: 90, month: 2700, total: 32400 },
  { brand: '卤驴肉', today: 150, month: 4500, total: 54000 },
  { brand: '驴肉火烧', today: 200, month: 6000, total: 72000 },
  { brand: '驴肉香肠', today: 80, month: 2400, total: 28800 },
  { brand: '驴肉干', today: 350, month: 3500, total: 22000 },
  { brand: '卤驴杂', today: 3050, month: 3600, total: 6650 },
  { brand: '驴腱筋', today: 1350, month: 3060, total: 4410 },
  { brand: '驴板肠', today: 3050, month: 3600, total: 6650 },
  { brand: '驴下水', today: 1350, month: 3060, total: 4410 },
  { brand: '酱驴舌', today: 1350, month: 3060, total: 4410 },
  { brand: '驴肉礼盒', today: 1350, month: 3060, total: 4410 }
]

// 图表ref
const lineChartRef = ref(null)
const barChartRef = ref(null)
const pieChartRef = ref(null)

// 图表初始化
onMounted(() => {
  nextTick(() => {
    // 折线图
    const lineChart = echarts.init(lineChartRef.value)
    lineChart.setOption({
      tooltip: { trigger: 'axis' },
      legend: { data: ['五香驴肉', '酱驴肉', '卤驴肉', '驴肉火烧', '驴肉香肠'] },
      xAxis: { type: 'category', data: ['五香驴肉', '酱驴肉', '卤驴肉', '驴肉火烧', '驴肉香肠'] },
      yAxis: { type: 'value' },
      series: [
        { name: '五香驴肉', type: 'line', data: [88, 88, 88, 88, 88] },
        { name: '酱驴肉', type: 'line', data: [98, 98, 98, 98, 98] },
        { name: '卤驴肉', type: 'line', data: [78, 78, 78, 78, 78] },
        { name: '驴肉火烧', type: 'line', data: [68, 68, 68, 68, 68] },
        { name: '驴肉香肠', type: 'line', data: [58, 58, 58, 58, 58] }
      ]
    })
    // 柱状图
    const barChart = echarts.init(barChartRef.value)
    barChart.setOption({
      tooltip: {},
      legend: { data: ['新增用户', '活跃用户'] },
      xAxis: { data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
      yAxis: {},
      series: [
        { name: '新增用户', type: 'bar', data: [50, 200, 360, 800, 650, 400, 100] },
        { name: '活跃用户', type: 'bar', data: [300, 400, 500, 800, 900, 700, 200] }
      ]
    })
    // 饼图
    const pieChart = echarts.init(pieChartRef.value)
    pieChart.setOption({
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left' },
      series: [
        {
          name: '品牌占比',
          type: 'pie',
          radius: '60%',
          data: [
            { value: 43200, name: '五香驴肉' },
            { value: 32400, name: '酱驴肉' },
            { value: 54000, name: '卤驴肉' },
            { value: 72000, name: '驴肉火烧' },
            { value: 28800, name: '驴肉香肠' },
            { value: 22000, name: '驴肉干' }
          ]
        }
      ]
    })
  })
})

watch(activeTab, (val) => {
  if (val === 'dashboard') {
    nextTick(() => {
      if (lineChartRef.value) echarts.getInstanceByDom(lineChartRef.value)?.resize()
      if (barChartRef.value) echarts.getInstanceByDom(barChartRef.value)?.resize()
      if (pieChartRef.value) echarts.getInstanceByDom(pieChartRef.value)?.resize()
    })
  }
})

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        authStore.logout()
        ElMessage.success('已退出登录')
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

// 监听侧边栏菜单点击，切换标签
function handleMenuSelect(index) {
  if (index === '/dashboard/product') activeTab.value = 'product'
  else if (index === '/dashboard/stock') activeTab.value = 'stock'
  else if (index === '/dashboard/customer') activeTab.value = 'customer'
  else if (index === '/dashboard/orders') activeTab.value = 'orders'
  else if (index === '/dashboard/dishes') activeTab.value = 'dishes'
  else if (index === '/dashboard/categories') activeTab.value = 'categories'
  else activeTab.value = 'dashboard'
}

const componentMap = {
  dashboard: DashboardHome,
  product: ProductManage,
  stock: StockManage,
  customer: CustomerManage,
  orders: OrderManage,
  dishes: DishesManage,
  categories: CategoriesManage
}
const activeComponent = computed(() => componentMap[activeTab.value] || DashboardHome)
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
  background: #ede2d1; /* 更深的暖色背景 */
}
.full-height {
  height: 100vh;
  min-height: 100vh;
}
.main-bg {
  background: #ede2d1;
}
.sidebar {
  background-color: #222; /* 黑色 */
  color: #fff;
  border-right: 1px solid #444;
  min-height: 100vh;
  box-shadow: 2px 0 8px 0 #444;
}
.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #cbb292;
}
.sidebar-header h2 {
  color: #fff;
  font-size: 20px;
  margin: 0;
  font-weight: bold;
}
.sidebar-menu {
  border-right: none;
  background: transparent;
}
.header {
  background-color: #f3e3c3;
  border-bottom: 1px solid #cbb292;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
}
.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}
.user-info:hover {
  background-color: #e6d3b3;
}
.username {
  margin: 0 8px;
  color: #333;
}
.main-content {
  background-color: #ede2d1;
  padding: 24px 18px 18px 18px;
  min-height: calc(100vh - 60px);
}
.top-row {
  margin-bottom: 18px;
}
.profile-card {
  height: 170px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
}
.profile-header {
  display: flex;
  align-items: center;
}
.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 16px;
  object-fit: cover;
  border: 2px solid #e6d3b3;
}
.profile-info {
  display: flex;
  flex-direction: column;
}
.profile-name {
  font-size: 22px;
  font-weight: bold;
}
.profile-title {
  color: #a88b6a;
  font-size: 14px;
}
.profile-detail {
  margin-top: 10px;
  color: #7c5c3b;
  font-size: 15px;
}
.stat-row {
  margin-left: 0;
  margin-right: 0;
}
.stat-card2 {
  height: 54px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  background: #f3e3c3;
  border: 1px solid #e6d3b3;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px #cbb292;
}
.stat-num {
  font-size: 20px;
  font-weight: bold;
  color: #b97a3c;
}
.stat-label {
  color: #a88b6a;
  font-size: 13px;
}
.middle-row {
  margin-bottom: 18px;
}
.table-card {
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
  height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.chart-card {
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
  height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.chart-box {
  width: 100%;
  height: 300px;
}
.bottom-row {
  margin-bottom: 0;
}
.empty-card {
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
  height: 340px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 