{"version": 3, "file": "input-tag2.js", "sources": ["../../../../../../packages/components/input-tag/src/input-tag.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"wrapperRef\"\n    :class=\"containerKls\"\n    :style=\"containerStyle\"\n    @mouseenter=\"handleMouseEnter\"\n    @mouseleave=\"handleMouseLeave\"\n  >\n    <div v-if=\"slots.prefix\" :class=\"ns.e('prefix')\">\n      <slot name=\"prefix\" />\n    </div>\n    <div :class=\"innerKls\">\n      <el-tag\n        v-for=\"(item, index) in modelValue\"\n        :key=\"index\"\n        :size=\"tagSize\"\n        :closable=\"closable\"\n        :type=\"tagType\"\n        :effect=\"tagEffect\"\n        :draggable=\"closable && draggable\"\n        disable-transitions\n        @close=\"handleRemoveTag(index)\"\n        @dragstart=\"(event: DragEvent) => handleDragStart(event, index)\"\n        @dragover=\"(event: DragEvent) => handleDragOver(event, index)\"\n        @dragend=\"handleDragEnd\"\n        @drop.stop\n      >\n        <slot name=\"tag\" :value=\"item\" :index=\"index\">\n          {{ item }}\n        </slot>\n      </el-tag>\n      <div :class=\"ns.e('input-wrapper')\">\n        <input\n          :id=\"inputId\"\n          ref=\"inputRef\"\n          v-model=\"inputValue\"\n          v-bind=\"attrs\"\n          type=\"text\"\n          :minlength=\"minlength\"\n          :maxlength=\"maxlength\"\n          :disabled=\"disabled\"\n          :readonly=\"readonly\"\n          :autocomplete=\"autocomplete\"\n          :tabindex=\"tabindex\"\n          :placeholder=\"placeholder\"\n          :autofocus=\"autofocus\"\n          :ariaLabel=\"ariaLabel\"\n          :class=\"ns.e('input')\"\n          :style=\"inputStyle\"\n          @compositionstart=\"handleCompositionStart\"\n          @compositionupdate=\"handleCompositionUpdate\"\n          @compositionend=\"handleCompositionEnd\"\n          @input=\"handleInput\"\n          @keydown=\"handleKeydown\"\n        />\n        <span\n          ref=\"calculatorRef\"\n          aria-hidden=\"true\"\n          :class=\"ns.e('input-calculator')\"\n          v-text=\"inputValue\"\n        />\n      </div>\n      <div\n        v-show=\"showDropIndicator\"\n        ref=\"dropIndicatorRef\"\n        :class=\"ns.e('drop-indicator')\"\n      />\n    </div>\n    <div v-if=\"showSuffix\" :class=\"ns.e('suffix')\">\n      <slot name=\"suffix\" />\n      <el-icon\n        v-if=\"showClear\"\n        :class=\"[ns.e('icon'), ns.e('clear')]\"\n        @mousedown.prevent=\"NOOP\"\n        @click=\"handleClear\"\n      >\n        <circle-close />\n      </el-icon>\n      <el-icon\n        v-if=\"validateState && validateIcon && needStatusIcon\"\n        :class=\"[\n          nsInput.e('icon'),\n          nsInput.e('validateIcon'),\n          nsInput.is('loading', validateState === 'validating'),\n        ]\"\n      >\n        <component :is=\"validateIcon\" />\n      </el-icon>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, useSlots } from 'vue'\nimport { CircleClose } from '@element-plus/icons-vue'\nimport { useAttrs, useCalcInputWidth } from '@element-plus/hooks'\nimport { NOOP, ValidateComponentsMap } from '@element-plus/utils'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTag from '@element-plus/components/tag'\nimport { useFormItem, useFormItemInputId } from '@element-plus/components/form'\nimport { inputTagEmits, inputTagProps } from './input-tag'\nimport {\n  useDragTag,\n  useHovering,\n  useInputTag,\n  useInputTagDom,\n} from './composables'\n\ndefineOptions({\n  name: 'ElInputTag',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(inputTagProps)\nconst emit = defineEmits(inputTagEmits)\n\nconst attrs = useAttrs()\nconst slots = useSlots()\nconst { form, formItem } = useFormItem()\nconst { inputId } = useFormItemInputId(props, { formItemContext: formItem })\n\nconst needStatusIcon = computed(() => form?.statusIcon ?? false)\nconst validateState = computed(() => formItem?.validateState || '')\nconst validateIcon = computed(() => {\n  return validateState.value && ValidateComponentsMap[validateState.value]\n})\n\nconst {\n  inputRef,\n  wrapperRef,\n  isFocused,\n  inputValue,\n  size,\n  tagSize,\n  placeholder,\n  closable,\n  disabled,\n  handleDragged,\n  handleInput,\n  handleKeydown,\n  handleRemoveTag,\n  handleClear,\n  handleCompositionStart,\n  handleCompositionUpdate,\n  handleCompositionEnd,\n  focus,\n  blur,\n} = useInputTag({ props, emit, formItem })\nconst { hovering, handleMouseEnter, handleMouseLeave } = useHovering()\nconst { calculatorRef, inputStyle } = useCalcInputWidth()\nconst {\n  dropIndicatorRef,\n  showDropIndicator,\n  handleDragStart,\n  handleDragOver,\n  handleDragEnd,\n} = useDragTag({ wrapperRef, handleDragged, afterDragged: focus })\nconst {\n  ns,\n  nsInput,\n  containerKls,\n  containerStyle,\n  innerKls,\n  showClear,\n  showSuffix,\n} = useInputTagDom({\n  props,\n  hovering,\n  isFocused,\n  inputValue,\n  disabled,\n  size,\n  validateState,\n  validateIcon,\n  needStatusIcon,\n})\n\ndefineExpose({\n  focus,\n  blur,\n})\n</script>\n"], "names": ["useAttrs", "useSlots", "useFormItem", "useFormItemInputId", "computed", "ValidateComponentsMap", "useInputTag", "useHovering", "useCalcInputWidth", "useDragTag", "useInputTagDom"], "mappings": ";;;;;;;;;;;;;;;;;;;;uCA4Gc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAA,MAAM,QAAQA,cAAS,EAAA,CAAA;AACvB,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AACvB,IAAA,MAAM,EAAE,IAAA,EAAM,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AACvC,IAAM,MAAA,EAAE,SAAY,GAAAC,8BAAA,CAAmB,OAAO,EAAE,eAAA,EAAiB,UAAU,CAAA,CAAA;AAE3E,IAAA,MAAM,cAAiB,GAAAC,YAAA,CAAS,MAAM;AACtC,MAAA,IAAM,EAAgB,CAAA;AACtB,MAAM,OAAA,CAAA,EAAA,GAAA,IAAA,WAAwB,KAAM,CAAA,GAAA,IAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA;AAClC,KAAA,CAAA,CAAA;AAAuE,IACzE,MAAC,aAAA,GAAAA,YAAA,CAAA,MAAA,CAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,aAAA,KAAA,EAAA,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACJ,OAAA,aAAA,CAAA,KAAA,IAAAC,0BAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,aAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,WAAA;AAAA,MACA,sBAAA;AAAA,MACA,uBAAA;AAAA,0BACuB;AACzB,MAAA,KAAQ;AACR,MAAA,IAAM;AACN,KAAM,GAAAC,uBAAA,CAAA,EAAA,KAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,CAAA;AAAA,IACJ,MAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,GAAAC,uBAAA,EAAA,CAAA;AAAA,IACA,MAAA,EAAA,aAAA,EAAA,UAAA,EAAA,GAAAC,yBAAA,EAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACA,gBAAA;AAAA,MACA,iBAAA;AAAA,qBACe;AACjB,MAAM,cAAA;AAAA,MACJ,aAAA;AAAA,KACA,GAAAC,qBAAA,CAAA,EAAA,UAAA,EAAA,aAAA,EAAA,YAAA,EAAA,KAAA,EAAA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACA,EAAA;AAAA,MACA,OAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,cACiB;AAAA,MACjB,SAAA;AAAA,MACA,UAAA;AAAA,KACA,GAAAC,6BAAA,CAAA;AAAA,MACA,KAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,IAAA;AAAA,MACD,aAAA;AAED,MAAa,YAAA;AAAA,MACX,cAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}