{"version": 3, "file": "use-dialog.mjs", "sources": ["../../../../../../packages/components/dialog/src/use-dialog.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  nextTick,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\n\nimport {\n  defaultNamespace,\n  useId,\n  useLockscreen,\n  useZIndex,\n} from '@element-plus/hooks'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { addUnit, isClient } from '@element-plus/utils'\nimport { useGlobalConfig } from '@element-plus/components/config-provider'\n\nimport type { CSSProperties, Ref, SetupContext } from 'vue'\nimport type { DialogEmits, DialogProps } from './dialog'\n\nexport const useDialog = (\n  props: DialogProps,\n  targetRef: Ref<HTMLElement | undefined>\n) => {\n  const instance = getCurrentInstance()!\n  const emit = instance.emit as SetupContext<DialogEmits>['emit']\n  const { nextZIndex } = useZIndex()\n\n  let lastPosition = ''\n  const titleId = useId()\n  const bodyId = useId()\n  const visible = ref(false)\n  const closed = ref(false)\n  const rendered = ref(false) // when desctroyOnClose is true, we initialize it as false vise versa\n  const zIndex = ref(props.zIndex ?? nextZIndex())\n\n  let openTimer: (() => void) | undefined = undefined\n  let closeTimer: (() => void) | undefined = undefined\n\n  const namespace = useGlobalConfig('namespace', defaultNamespace)\n\n  const style = computed<CSSProperties>(() => {\n    const style: CSSProperties = {}\n    const varPrefix = `--${namespace.value}-dialog` as const\n    if (!props.fullscreen) {\n      if (props.top) {\n        style[`${varPrefix}-margin-top`] = props.top\n      }\n      if (props.width) {\n        style[`${varPrefix}-width`] = addUnit(props.width)\n      }\n    }\n    return style\n  })\n\n  const overlayDialogStyle = computed<CSSProperties>(() => {\n    if (props.alignCenter) {\n      return { display: 'flex' }\n    }\n    return {}\n  })\n\n  function afterEnter() {\n    emit('opened')\n  }\n\n  function afterLeave() {\n    emit('closed')\n    emit(UPDATE_MODEL_EVENT, false)\n    if (props.destroyOnClose) {\n      rendered.value = false\n    }\n  }\n\n  function beforeLeave() {\n    emit('close')\n  }\n\n  function open() {\n    closeTimer?.()\n    openTimer?.()\n\n    if (props.openDelay && props.openDelay > 0) {\n      ;({ stop: openTimer } = useTimeoutFn(() => doOpen(), props.openDelay))\n    } else {\n      doOpen()\n    }\n  }\n\n  function close() {\n    openTimer?.()\n    closeTimer?.()\n\n    if (props.closeDelay && props.closeDelay > 0) {\n      ;({ stop: closeTimer } = useTimeoutFn(() => doClose(), props.closeDelay))\n    } else {\n      doClose()\n    }\n  }\n\n  function handleClose() {\n    function hide(shouldCancel?: boolean) {\n      if (shouldCancel) return\n      closed.value = true\n      visible.value = false\n    }\n\n    if (props.beforeClose) {\n      props.beforeClose(hide)\n    } else {\n      close()\n    }\n  }\n\n  function onModalClick() {\n    if (props.closeOnClickModal) {\n      handleClose()\n    }\n  }\n\n  function doOpen() {\n    if (!isClient) return\n    visible.value = true\n  }\n\n  function doClose() {\n    visible.value = false\n  }\n\n  function onOpenAutoFocus() {\n    emit('openAutoFocus')\n  }\n\n  function onCloseAutoFocus() {\n    emit('closeAutoFocus')\n  }\n\n  function onFocusoutPrevented(event: CustomEvent) {\n    if (event.detail?.focusReason === 'pointer') {\n      event.preventDefault()\n    }\n  }\n\n  if (props.lockScroll) {\n    useLockscreen(visible)\n  }\n\n  function onCloseRequested() {\n    if (props.closeOnPressEscape) {\n      handleClose()\n    }\n  }\n\n  watch(\n    () => props.zIndex,\n    () => {\n      zIndex.value = props.zIndex ?? nextZIndex()\n    }\n  )\n\n  watch(\n    () => props.modelValue,\n    (val) => {\n      if (val) {\n        closed.value = false\n        open()\n        rendered.value = true // enables lazy rendering\n        zIndex.value = props.zIndex ?? nextZIndex()\n        // this.$el.addEventListener('scroll', this.updatePopper)\n        nextTick(() => {\n          emit('open')\n          if (targetRef.value) {\n            targetRef.value.parentElement!.scrollTop = 0\n            targetRef.value.parentElement!.scrollLeft = 0\n            targetRef.value.scrollTop = 0\n          }\n        })\n      } else {\n        // this.$el.removeEventListener('scroll', this.updatePopper\n        if (visible.value) {\n          close()\n        }\n      }\n    }\n  )\n\n  watch(\n    () => props.fullscreen,\n    (val) => {\n      if (!targetRef.value) return\n      if (val) {\n        lastPosition = targetRef.value.style.transform\n        targetRef.value.style.transform = ''\n      } else {\n        targetRef.value.style.transform = lastPosition\n      }\n    }\n  )\n\n  onMounted(() => {\n    if (props.modelValue) {\n      visible.value = true\n      rendered.value = true // enables lazy rendering\n      open()\n    }\n  })\n\n  return {\n    afterEnter,\n    afterLeave,\n    beforeLeave,\n    handleClose,\n    onModalClick,\n    close,\n    doClose,\n    onOpenAutoFocus,\n    onCloseAutoFocus,\n    onCloseRequested,\n    onFocusoutPrevented,\n    titleId,\n    bodyId,\n    closed,\n    style,\n    overlayDialogStyle,\n    rendered,\n    visible,\n    zIndex,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAkBY,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE,SAAS,KAAK;AAC/C,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC7B,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,SAAS,EAAE,CAAC;AACrC,EAAE,IAAI,YAAY,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,OAAO,GAAG,KAAK,EAAE,CAAC;AAC1B,EAAE,MAAM,MAAM,GAAG,KAAK,EAAE,CAAC;AACzB,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;AACtE,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC;AACzB,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC;AAC1B,EAAE,MAAM,SAAS,GAAG,eAAe,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AACnE,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AAC/B,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,MAAM,SAAS,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AAC3B,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE;AACrB,QAAQ,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;AACtD,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE;AACvB,QAAQ,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5D,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM;AAC5C,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;AAC3B,MAAM,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,KAAK,CAAC,cAAc,EAAE;AAC9B,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,KAAK;AACL,GAAG;AACH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,SAAS,IAAI,GAAG;AAClB,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;AAC/C,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC;AAC7C,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE;AAEhD,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,YAAY,CAAC,MAAM,MAAM,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE;AAC5E,KAAK,MAAM;AACX,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,GAAG;AACH,EAAE,SAAS,KAAK,GAAG;AACnB,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC;AAC7C,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;AAC/C,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE;AAElD,MAAM,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC,MAAM,OAAO,EAAE,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE;AAC/E,KAAK,MAAM;AACX,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;AAChC,MAAM,IAAI,YAAY;AACtB,QAAQ,OAAO;AACf,MAAM,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,MAAM,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE;AAC3B,MAAM,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9B,KAAK,MAAM;AACX,MAAM,KAAK,EAAE,CAAC;AACd,KAAK;AACL,GAAG;AACH,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACjC,MAAM,WAAW,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,MAAM,GAAG;AACpB,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACzB,GAAG;AACH,EAAE,SAAS,OAAO,GAAG;AACrB,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,GAAG;AACH,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACtC,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,MAAM,SAAS,EAAE;AACjF,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,UAAU,EAAE;AACxB,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,KAAK,CAAC,kBAAkB,EAAE;AAClC,MAAM,WAAW,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,EAAE,MAAM;AAClC,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,UAAU,EAAE,CAAC;AACrE,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK;AACzC,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,MAAM,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,UAAU,EAAE,CAAC;AACvE,MAAM,QAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;AACrB,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;AAC7B,UAAU,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC;AACtD,UAAU,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,GAAG,CAAC,CAAC;AACvD,UAAU,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;AACxC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;AACzB,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK;AACzC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;AACxB,MAAM,OAAO;AACb,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;AACrD,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC;AACrD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,IAAI,KAAK,CAAC,UAAU,EAAE;AAC1B,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,MAAM,IAAI,EAAE,CAAC;AACb,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAI,kBAAkB;AACtB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,MAAM;AACV,GAAG,CAAC;AACJ;;;;"}