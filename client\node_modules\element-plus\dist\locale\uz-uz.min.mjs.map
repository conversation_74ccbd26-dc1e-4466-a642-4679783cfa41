{"version": 3, "file": "uz-uz.min.mjs", "sources": ["../../../../packages/locale/lang/uz-uz.ts"], "sourcesContent": ["export default {\n  name: 'uz-uz',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Qabul qilish',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: 'Bugun',\n      cancel: '<PERSON><PERSON> qilish',\n      clear: 'Tozalash',\n      confirm: 'Qabul qilish',\n      selectDate: '<PERSON>nni tanlash',\n      selectTime: 'Soatni tanlash',\n      startDate: 'Boshlanish sanasi',\n      startTime: '<PERSON><PERSON><PERSON><PERSON> vaqti',\n      endDate: 'Tugash sanasi',\n      endTime: '<PERSON>gash vaqti',\n      prevYear: 'Oʻtgan yil',\n      nextYear: 'Kelgusi yil',\n      prevMonth: 'Oʻtgan oy',\n      nextMonth: 'Kelgusi oy',\n      year: 'Yil',\n      month1: 'Yanvar',\n      month2: 'Fevral',\n      month3: 'Mart',\n      month4: 'Aprel',\n      month5: 'May',\n      month6: 'Iyun',\n      month7: 'Iyul',\n      month8: 'Avgust',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: 'Oktabr',\n      month11: 'Noyabr',\n      month12: 'Dekabr',\n      week: 'Hafta',\n      weeks: {\n        sun: 'Yak',\n        mon: 'Dush',\n        tue: 'Sesh',\n        wed: 'Chor',\n        thu: 'Pay',\n        fri: 'Jum',\n        sat: 'Shan',\n      },\n      months: {\n        jan: 'Yan',\n        feb: 'Fev',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Iyun',\n        jul: 'Iyul',\n        aug: 'Avg',\n        sep: 'Sen',\n        oct: 'Okt',\n        nov: 'Noy',\n        dec: 'Dek',\n      },\n    },\n    select: {\n      loading: 'Yuklanmoqda',\n      noMatch: 'Mos maʼlumot yoʻq',\n      noData: 'Maʼlumot yoʻq',\n      placeholder: 'Tanladizngiz',\n    },\n    mention: {\n      loading: 'Yuklanmoqda',\n    },\n    cascader: {\n      noMatch: 'Mos maʼlumot topilmadi',\n      loading: 'Yuklanmoqda',\n      placeholder: 'Tanlash',\n      noData: 'Maʼlumot yoʻq',\n    },\n    pagination: {\n      goto: 'Oʻtish',\n      pagesize: '/sahifa',\n      total: 'Barchasi {total} ta',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Xabar',\n      confirm: 'Qabul qilish',\n      cancel: 'Bekor qilish',\n      error: 'Xatolik',\n    },\n    upload: {\n      deleteTip: 'Oʻchirish tugmasini bosib oʻchiring',\n      delete: 'Oʻchirish',\n      preview: 'Oldin koʻrish',\n      continue: 'Davom qilish',\n    },\n    table: {\n      emptyText: 'Boʻsh',\n      confirmFilter: 'Qabul qilish',\n      resetFilter: 'Oldingi holatga qaytarish',\n      clearFilter: 'Jami',\n      sumText: 'Summasi',\n    },\n    tree: {\n      emptyText: 'Maʼlumot yoʻq',\n    },\n    transfer: {\n      noMatch: 'Mos maʼlumot topilmadi',\n      noData: 'Maʼlumot yoʻq',\n      titles: ['1-jadval', '2-jadval'],\n      filterPlaceholder: 'Kalit soʻzni kiriting',\n      noCheckedFormat: '{total} ta element',\n      hasCheckedFormat: '{checked}/{total} ta belgilandi',\n    },\n    image: {\n      error: 'Xatolik',\n    },\n    pageHeader: {\n      title: 'Orqaga',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,WAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,2BAA2B,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,eAAe,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}