{"version": 3, "file": "fa.mjs", "sources": ["../../../../../packages/locale/lang/fa.ts"], "sourcesContent": ["export default {\n  name: 'fa',\n  el: {\n    breadcrumb: {\n      label: 'مسیر راهنما',\n    },\n    colorpicker: {\n      confirm: 'تأیید',\n      clear: 'پاک کردن',\n      defaultLabel: 'انتخاب‌گر رنگ',\n      description:\n        'رنگ فعلی {color} است. برای انتخاب رنگ جدید، اینتر را فشار دهید.',\n      alphaLabel: 'مقدار آلفا را انتخاب کنید',\n    },\n    datepicker: {\n      now: 'اکنون',\n      today: 'امروز',\n      cancel: 'لغو',\n      clear: 'پاک کردن',\n      confirm: 'تأیید',\n      dateTablePrompt:\n        'از کلیدهای جهت‌دار و اینتر برای انتخاب روز ماه استفاده کنید',\n      monthTablePrompt:\n        'از کلیدهای جهت‌دار و اینتر برای انتخاب ماه استفاده کنید',\n      yearTablePrompt:\n        'از کلیدهای جهت‌دار و اینتر برای انتخاب سال استفاده کنید',\n      selectedDate: 'تاریخ انتخاب‌شده',\n      selectDate: 'انتخاب تاریخ',\n      selectTime: 'انتخاب زمان',\n      startDate: 'تاریخ شروع',\n      startTime: 'زمان شروع',\n      endDate: 'تاریخ پایان',\n      endTime: 'زمان پایان',\n      prevYear: 'سال قبل',\n      nextYear: 'سال بعد',\n      prevMonth: 'ماه قبل',\n      nextMonth: 'ماه بعد',\n      year: '',\n      month1: 'ژانویه',\n      month2: 'فوریه',\n      month3: 'مارس',\n      month4: 'آوریل',\n      month5: 'مه',\n      month6: 'ژوئن',\n      month7: 'ژوئیه',\n      month8: 'اوت',\n      month9: 'سپتامبر',\n      month10: 'اکتبر',\n      month11: 'نوامبر',\n      month12: 'دسامبر',\n      week: 'هفته',\n      weeks: {\n        sun: 'یک‌شنبه',\n        mon: 'دوشنبه',\n        tue: 'سه‌شنبه',\n        wed: 'چهارشنبه',\n        thu: 'پنج‌شنبه',\n        fri: 'جمعه',\n        sat: 'شنبه',\n      },\n      weeksFull: {\n        sun: 'یک‌شنبه',\n        mon: 'دوشنبه',\n        tue: 'سه‌شنبه',\n        wed: 'چهارشنبه',\n        thu: 'پنج‌شنبه',\n        fri: 'جمعه',\n        sat: 'شنبه',\n      },\n      months: {\n        jan: 'ژانویه',\n        feb: 'فوریه',\n        mar: 'مارچ',\n        apr: 'آوریل',\n        may: 'مه',\n        jun: 'ژوئن',\n        jul: 'ژوئیه',\n        aug: 'اوت',\n        sep: 'سپتامبر',\n        oct: 'اکتبر',\n        nov: 'نوامبر',\n        dec: 'دسامبر',\n      },\n    },\n    inputNumber: {\n      decrease: 'کاهش عدد',\n      increase: 'افزایش عدد',\n    },\n    select: {\n      loading: 'در حال بارگذاری',\n      noMatch: 'هیچ داده منطبقی وجود ندارد',\n      noData: 'داده‌ای موجود نیست',\n      placeholder: 'انتخاب کنید',\n    },\n    mention: {\n      loading: 'در حال بارگذاری',\n    },\n    dropdown: {\n      toggleDropdown: 'باز و بسته کردن منوی کشویی',\n    },\n    cascader: {\n      noMatch: 'هیچ داده منطبقی وجود ندارد',\n      loading: 'در حال بارگذاری',\n      placeholder: 'انتخاب کنید',\n      noData: 'داده‌ای موجود نیست',\n    },\n    pagination: {\n      goto: 'برو به',\n      pagesize: '/صفحه',\n      total: 'مجموع {total}',\n      pageClassifier: '',\n      page: 'صفحه',\n      prev: 'برو به صفحه قبلی',\n      next: 'برو به صفحه بعدی',\n      currentPage: 'صفحه {pager}',\n      prevPages: '{pager} صفحات قبلی',\n      nextPages: '{pager} صفحات بعدی',\n      deprecationWarning:\n        'استفاده‌های منسوخ شناسایی شد، لطفاً به مستندات el-pagination مراجعه کنید',\n    },\n    dialog: {\n      close: 'بستن این دیالوگ',\n    },\n    drawer: {\n      close: 'بستن این دیالوگ',\n    },\n    messagebox: {\n      title: 'پیام',\n      confirm: 'تأیید',\n      cancel: 'لغو',\n      error: 'ورودی نامعتبر',\n      close: 'بستن این دیالوگ',\n    },\n    upload: {\n      deleteTip: 'برای حذف، کلید delete را فشار دهید',\n      delete: 'حذف',\n      preview: 'پیش‌نمایش',\n      continue: 'ادامه',\n    },\n    slider: {\n      defaultLabel: 'لغزنده بین {min} و {max}',\n      defaultRangeStartLabel: 'انتخاب مقدار شروع',\n      defaultRangeEndLabel: 'انتخاب مقدار پایان',\n    },\n    table: {\n      emptyText: 'داده‌ای موجود نیست',\n      confirmFilter: 'تأیید',\n      resetFilter: 'بازنشانی',\n      clearFilter: 'همه',\n      sumText: 'مجموع',\n    },\n    tour: {\n      next: 'بعدی',\n      previous: 'قبلی',\n      finish: 'پایان',\n    },\n    tree: {\n      emptyText: 'داده‌ای موجود نیست',\n    },\n    transfer: {\n      noMatch: 'داده‌ای مطابقت ندارد',\n      noData: 'داده‌ای موجود نیست',\n      titles: ['فهرست ۱', 'فهرست ۲'],\n      filterPlaceholder: 'کلمه کلیدی را وارد کنید',\n      noCheckedFormat: '{total} آیتم',\n      hasCheckedFormat: '{checked}/{total} انتخاب‌شده',\n    },\n    image: {\n      error: 'ناموفق',\n    },\n    pageHeader: {\n      title: 'بازگشت',\n    },\n    popconfirm: {\n      confirmButtonText: 'بله',\n      cancelButtonText: 'خیر',\n    },\n    carousel: {\n      leftArrow: 'پیکان به جهت چپ',\n      rightArrow: 'پیکان چرخان به جهت راست',\n      indicator: 'سوئیچ چرخان به شاخص {index}',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,+DAA+D;AAC5E,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE,6CAA6C;AAC1D,MAAM,YAAY,EAAE,2EAA2E;AAC/F,MAAM,WAAW,EAAE,wRAAwR;AAC3S,MAAM,UAAU,EAAE,oIAAoI;AACtJ,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,gCAAgC;AAC3C,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,KAAK,EAAE,6CAA6C;AAC1D,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,eAAe,EAAE,kTAAkT;AACzU,MAAM,gBAAgB,EAAE,+RAA+R;AACvT,MAAM,eAAe,EAAE,+RAA+R;AACtT,MAAM,YAAY,EAAE,6FAA6F;AACjH,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,UAAU,EAAE,+DAA+D;AACjF,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,OAAO,EAAE,yDAAyD;AACxE,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,SAAS,EAAE,uCAAuC;AACxD,MAAM,SAAS,EAAE,uCAAuC;AACxD,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,6CAA6C;AAC7D,MAAM,QAAQ,EAAE,yDAAyD;AACzE,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kFAAkF;AACjG,MAAM,OAAO,EAAE,0IAA0I;AACzJ,MAAM,MAAM,EAAE,oGAAoG;AAClH,MAAM,WAAW,EAAE,+DAA+D;AAClF,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kFAAkF;AACjG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,qIAAqI;AAC3J,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0IAA0I;AACzJ,MAAM,OAAO,EAAE,kFAAkF;AACjG,MAAM,WAAW,EAAE,+DAA+D;AAClF,MAAM,MAAM,EAAE,oGAAoG;AAClH,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,iCAAiC;AAC7C,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,KAAK,EAAE,wCAAwC;AACrD,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,IAAI,EAAE,mFAAmF;AAC/F,MAAM,IAAI,EAAE,mFAAmF;AAC/F,MAAM,WAAW,EAAE,kCAAkC;AACrD,MAAM,SAAS,EAAE,iEAAiE;AAClF,MAAM,SAAS,EAAE,iEAAiE;AAClF,MAAM,kBAAkB,EAAE,oUAAoU;AAC9V,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,kFAAkF;AAC/F,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,kFAAkF;AAC/F,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,KAAK,EAAE,2EAA2E;AACxF,MAAM,KAAK,EAAE,kFAAkF;AAC/F,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,kJAAkJ;AACnK,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,QAAQ,EAAE,gCAAgC;AAChD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,4EAA4E;AAChG,MAAM,sBAAsB,EAAE,8FAA8F;AAC5H,MAAM,oBAAoB,EAAE,oGAAoG;AAChI,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,oGAAoG;AACrH,MAAM,aAAa,EAAE,gCAAgC;AACrD,MAAM,WAAW,EAAE,kDAAkD;AACrE,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,oGAAoG;AACrH,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gHAAgH;AAC/H,MAAM,MAAM,EAAE,oGAAoG;AAClH,MAAM,MAAM,EAAE,CAAC,uCAAuC,EAAE,uCAAuC,CAAC;AAChG,MAAM,iBAAiB,EAAE,wHAAwH;AACjJ,MAAM,eAAe,EAAE,kCAAkC;AACzD,MAAM,gBAAgB,EAAE,gFAAgF;AACxG,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,gBAAgB,EAAE,oBAAoB;AAC5C,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,6EAA6E;AAC9F,MAAM,UAAU,EAAE,wHAAwH;AAC1I,MAAM,SAAS,EAAE,6GAA6G;AAC9H,KAAK;AACL,GAAG;AACH,CAAC;;;;"}