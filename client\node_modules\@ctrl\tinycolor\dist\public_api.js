"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
var index_js_1 = require("./index.js");
__exportStar(require("./index.js"), exports);
__exportStar(require("./css-color-names.js"), exports);
__exportStar(require("./readability.js"), exports);
__exportStar(require("./to-ms-filter.js"), exports);
__exportStar(require("./from-ratio.js"), exports);
__exportStar(require("./format-input.js"), exports);
__exportStar(require("./random.js"), exports);
__exportStar(require("./interfaces.js"), exports);
__exportStar(require("./conversion.js"), exports);
// kept for backwards compatability with v1
exports.default = index_js_1.tinycolor;
