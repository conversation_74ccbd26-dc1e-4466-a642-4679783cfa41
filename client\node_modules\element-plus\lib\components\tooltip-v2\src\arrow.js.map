{"version": 3, "file": "arrow.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/arrow.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { tooltipV2Sides } from './common'\n\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type { TooltipV2Sides } from './common'\n\nexport const tooltipV2ArrowProps = buildProps({\n  width: {\n    type: Number,\n    default: 10,\n  },\n  height: {\n    type: Number,\n    default: 10,\n  },\n  style: {\n    type: definePropType<CSSProperties | null>(Object),\n    default: null,\n  },\n} as const)\n\nexport const tooltipV2ArrowSpecialProps = buildProps({\n  side: {\n    type: definePropType<TooltipV2Sides>(String),\n    values: tooltipV2Sides,\n    required: true,\n  },\n} as const)\n\nexport type TooltipV2ArrowProps = ExtractPropTypes<typeof tooltipV2ArrowProps>\n"], "names": ["buildProps", "definePropType", "tooltipV2Sides"], "mappings": ";;;;;;;AAEY,MAAC,mBAAmB,GAAGA,kBAAU,CAAC;AAC9C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,0BAA0B,GAAGD,kBAAU,CAAC;AACrD,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAEC,qBAAc;AAC1B,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,CAAC;;;;;"}