<template>
  <div class="page-container">
    <el-card>
      <div class="toolbar">
        <el-input v-model="search" placeholder="搜索员工姓名" style="width: 200px; margin-right: 10px;" clearable />
        <el-button type="primary" @click="openAddDialog">添加员工</el-button>
      </div>
      <el-table :data="filteredStaff" style="width: 100%; margin-top: 16px;" border>
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="position" label="职位" />
        <el-table-column prop="phone" label="电话" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="removeStaff(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="editMode ? '编辑员工' : '添加员工'" v-model="dialogVisible">
      <el-form :model="form" label-width="80px">
        <el-form-item label="姓名">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="职位">
          <el-input v-model="form.position" />
        </el-form-item>
        <el-form-item label="电话">
          <el-input v-model="form.phone" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveStaff">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const search = ref('')
const dialogVisible = ref(false)
const editMode = ref(false)
const form = ref({ id: null, name: '', position: '', phone: '' })
const staffList = ref([
  { id: 1, name: '张三', position: '经理', phone: '13800000001' },
  { id: 2, name: '李四', position: '销售', phone: '13800000002' },
  { id: 3, name: '王五', position: '后勤', phone: '13800000003' }
])
const filteredStaff = computed(() => {
  if (!search.value) return staffList.value
  return staffList.value.filter(s => s.name.includes(search.value))
})
function openAddDialog() {
  editMode.value = false
  form.value = { id: null, name: '', position: '', phone: '' }
  dialogVisible.value = true
}
function openEditDialog(row) {
  editMode.value = true
  form.value = { ...row }
  dialogVisible.value = true
}
function saveStaff() {
  if (!form.value.name) return ElMessage.error('请输入姓名')
  if (!form.value.position) return ElMessage.error('请输入职位')
  if (!form.value.phone) return ElMessage.error('请输入电话')
  if (editMode.value) {
    const idx = staffList.value.findIndex(s => s.id === form.value.id)
    if (idx !== -1) staffList.value[idx] = { ...form.value }
    ElMessage.success('编辑成功')
  } else {
    form.value.id = Date.now()
    staffList.value.push({ ...form.value })
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
}
function removeStaff(id) {
  staffList.value = staffList.value.filter(s => s.id !== id)
  ElMessage.success('删除成功')
}
</script>
<style scoped>
.page-container { padding: 18px; }
.toolbar { margin-bottom: 10px; display: flex; align-items: center; }
</style> 