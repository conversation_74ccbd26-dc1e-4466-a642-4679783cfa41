{"version": 3, "file": "skeleton-item2.mjs", "sources": ["../../../../../../packages/components/skeleton/src/skeleton-item.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type SkeletonItem from './skeleton-item.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const skeletonItemProps = buildProps({\n  /**\n   * @description the current rendering skeleton type\n   */\n  variant: {\n    type: String,\n    values: [\n      'circle',\n      'rect',\n      'h1',\n      'h3',\n      'text',\n      'caption',\n      'p',\n      'image',\n      'button',\n    ],\n    default: 'text',\n  },\n} as const)\nexport type SkeletonItemProps = ExtractPropTypes<typeof skeletonItemProps>\n\nexport type SkeletonItemInstance = InstanceType<typeof SkeletonItem> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE;AACZ,MAAM,QAAQ;AACd,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,GAAG;AACT,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,KAAK;AACL,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}