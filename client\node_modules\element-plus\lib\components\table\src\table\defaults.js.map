{"version": 3, "file": "defaults.js", "sources": ["../../../../../../../packages/components/table/src/table/defaults.ts"], "sourcesContent": ["// @ts-nocheck\nimport { useSizeProp } from '@element-plus/hooks'\nimport type {\n  CSSProperties,\n  ComponentInternalInstance,\n  PropType,\n  Ref,\n  VNode,\n} from 'vue'\nimport type { ComponentSize } from '@element-plus/constants'\nimport type { Nullable } from '@element-plus/utils'\nimport type { Store } from '../store'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type TableLayout from '../table-layout'\nimport type {\n  TableOverflowTooltipFormatter,\n  TableOverflowTooltipOptions,\n} from '../util'\n\nexport type DefaultRow = any\n\ninterface TableRefs {\n  tableWrapper: HTMLElement\n  headerWrapper: HTMLElement\n  footerWrapper: HTMLElement\n  fixedBodyWrapper: HTMLElement\n  rightFixedBodyWrapper: HTMLElement\n  bodyWrapper: HTMLElement\n  appendWrapper: HTMLElement\n  [key: string]: any\n}\n\ninterface TableState {\n  isGroup: Ref<boolean>\n  resizeState: Ref<{\n    width: any\n    height: any\n  }>\n  doLayout: () => void\n  debouncedUpdateLayout: () => void\n}\n\ninterface TreeProps {\n  hasChildren?: string\n  children?: string\n  checkStrictly?: boolean\n}\n\ntype HoverState<T> = Nullable<{\n  cell: HTMLElement\n  column: TableColumnCtx<T>\n  row: T\n}>\n\ntype RIS<T> = { row: T; $index: number; store: Store<T>; expanded: boolean }\n\ntype RenderExpanded<T> = ({\n  row,\n  $index,\n  store,\n  expanded: boolean,\n}: RIS<T>) => VNode\n\ntype SummaryMethod<T> = (data: {\n  columns: TableColumnCtx<T>[]\n  data: T[]\n}) => (string | VNode)[]\n\ninterface Table<T> extends ComponentInternalInstance {\n  $ready: boolean\n  hoverState?: HoverState<T>\n  renderExpanded: RenderExpanded<T>\n  store: Store<T>\n  layout: TableLayout<T>\n  refs: TableRefs\n  tableId: string\n  state: TableState\n}\n\ntype ColumnCls<T> = string | ((data: { row: T; rowIndex: number }) => string)\ntype ColumnStyle<T> =\n  | CSSProperties\n  | ((data: { row: T; rowIndex: number }) => CSSProperties)\ntype CellCls<T> =\n  | string\n  | ((data: {\n      row: T\n      rowIndex: number\n      column: TableColumnCtx<T>\n      columnIndex: number\n    }) => string)\ntype CellStyle<T> =\n  | CSSProperties\n  | ((data: {\n      row: T\n      rowIndex: number\n      column: TableColumnCtx<T>\n      columnIndex: number\n    }) => CSSProperties)\ntype Layout = 'fixed' | 'auto'\ninterface TableProps<T> {\n  data: T[]\n  size?: ComponentSize\n  width?: string | number\n  height?: string | number\n  maxHeight?: string | number\n  fit?: boolean\n  stripe?: boolean\n  border?: boolean\n  rowKey?: string | ((row: T) => string)\n  context?: Table<T>\n  showHeader?: boolean\n  showSummary?: boolean\n  sumText?: string\n  summaryMethod?: SummaryMethod<T>\n  rowClassName?: ColumnCls<T>\n  rowStyle?: ColumnStyle<T>\n  cellClassName?: CellCls<T>\n  cellStyle?: CellStyle<T>\n  headerRowClassName?: ColumnCls<T>\n  headerRowStyle?: ColumnStyle<T>\n  headerCellClassName?: CellCls<T>\n  headerCellStyle?: CellStyle<T>\n  highlightCurrentRow?: boolean\n  currentRowKey?: string | number\n  emptyText?: string\n  expandRowKeys?: string[]\n  defaultExpandAll?: boolean\n  defaultSort?: Sort\n  tooltipEffect?: string\n  tooltipOptions?: TableOverflowTooltipOptions\n  spanMethod?: (data: {\n    row: T\n    rowIndex: number\n    column: TableColumnCtx<T>\n    columnIndex: number\n  }) =>\n    | number[]\n    | {\n        rowspan: number\n        colspan: number\n      }\n    | undefined\n  selectOnIndeterminate?: boolean\n  indent?: number\n  treeProps?: TreeProps\n  lazy?: boolean\n  load?: (row: T, treeNode: TreeNode, resolve: (data: T[]) => void) => void\n  className?: string\n  style?: CSSProperties\n  tableLayout?: Layout\n  scrollbarAlwaysOn?: boolean\n  flexible?: boolean\n  showOverflowTooltip?: boolean | TableOverflowTooltipOptions\n  tooltipFormatter?: TableOverflowTooltipFormatter<T>\n  appendFilterPanelTo?: string\n  scrollbarTabindex?: number | string\n}\n\ntype TableTooltipData<T = any> = Parameters<TableOverflowTooltipFormatter<T>>[0]\n\ninterface Sort {\n  prop: string\n  order: 'ascending' | 'descending'\n  init?: any\n  silent?: any\n}\n\ninterface Filter<T> {\n  column: TableColumnCtx<T>\n  values: string[]\n  silent: any\n}\n\ninterface TreeNode {\n  expanded?: boolean\n  loading?: boolean\n  noLazyChildren?: boolean\n  indent?: number\n  level?: number\n  display?: boolean\n}\n\ninterface RenderRowData<T> {\n  store: Store<T>\n  _self: Table<T>\n  column: TableColumnCtx<T>\n  row: T\n  $index: number\n  treeNode?: TreeNode\n  expanded: boolean\n}\n\nexport default {\n  /**\n   * @description table data\n   */\n  data: {\n    type: Array as PropType<DefaultRow[]>,\n    default: () => [],\n  },\n  /**\n   * @description size of Table\n   */\n  size: useSizeProp,\n  width: [String, Number],\n  /**\n   * @description table's height. By default it has an `auto` height. If its value is a number, the height is measured in pixels; if its value is a string, the value will be assigned to element's style.height, the height is affected by external styles\n   */\n  height: [String, Number],\n  /**\n   * @description table's max-height. The legal value is a number or the height in px\n   */\n  maxHeight: [String, Number],\n  /**\n   * @description whether width of column automatically fits its container\n   */\n  fit: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether Table is striped\n   */\n  stripe: Boolean,\n  /**\n   * @description whether Table has vertical border\n   */\n  border: Boolean,\n  /**\n   * @description key of row data, used for optimizing rendering. Required if `reserve-selection` is on or display tree data. When its type is String, multi-level access is supported, e.g. `user.info.id`, but `user.info[0].id` is not supported, in which case `Function` should be used\n   */\n  rowKey: [String, Function] as PropType<TableProps<DefaultRow>['rowKey']>,\n  /**\n   * @description whether Table header is visible\n   */\n  showHeader: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to display a summary row\n   */\n  showSummary: Boolean,\n  /**\n   * @description displayed text for the first column of summary row\n   */\n  sumText: String,\n  /**\n   * @description custom summary method\n   */\n  summaryMethod: Function as PropType<TableProps<DefaultRow>['summaryMethod']>,\n  /**\n   * @description function that returns custom class names for a row, or a string assigning class names for every row\n   */\n  rowClassName: [String, Function] as PropType<\n    TableProps<DefaultRow>['rowClassName']\n  >,\n  /**\n   * @description function that returns custom style for a row, or an object assigning custom style for every row\n   */\n  rowStyle: [Object, Function] as PropType<TableProps<DefaultRow>['rowStyle']>,\n  /**\n   * @description function that returns custom class names for a cell, or a string assigning class names for every cell\n   */\n  cellClassName: [String, Function] as PropType<\n    TableProps<DefaultRow>['cellClassName']\n  >,\n  /**\n   * @description function that returns custom style for a cell, or an object assigning custom style for every cell\n   */\n  cellStyle: [Object, Function] as PropType<\n    TableProps<DefaultRow>['cellStyle']\n  >,\n  /**\n   * @description function that returns custom class names for a row in table header, or a string assigning class names for every row in table header\n   */\n  headerRowClassName: [String, Function] as PropType<\n    TableProps<DefaultRow>['headerRowClassName']\n  >,\n  /**\n   * @description function that returns custom style for a row in table header, or an object assigning custom style for every row in table header\n   */\n  headerRowStyle: [Object, Function] as PropType<\n    TableProps<DefaultRow>['headerRowStyle']\n  >,\n  /**\n   * @description function that returns custom class names for a cell in table header, or a string assigning class names for every cell in table header\n   */\n  headerCellClassName: [String, Function] as PropType<\n    TableProps<DefaultRow>['headerCellClassName']\n  >,\n  /**\n   * @description function that returns custom style for a cell in table header, or an object assigning custom style for every cell in table header\n   */\n  headerCellStyle: [Object, Function] as PropType<\n    TableProps<DefaultRow>['headerCellStyle']\n  >,\n  /**\n   * @description whether current row is highlighted\n   */\n  highlightCurrentRow: Boolean,\n  /**\n   * @description key of current row, a set only prop\n   */\n  currentRowKey: [String, Number],\n  /**\n   * @description displayed text when data is empty. You can customize this area with `#empty`\n   */\n  emptyText: String,\n  /**\n   * @description set expanded rows by this prop, prop's value is the keys of expand rows, you should set row-key before using this prop\n   */\n  expandRowKeys: Array as PropType<TableProps<DefaultRow>['expandRowKeys']>,\n  /**\n   * @description whether expand all rows by default, works when the table has a column type=\"expand\" or contains tree structure data\n   */\n  defaultExpandAll: Boolean,\n  /**\n   * @description set the default sort column and order. property `prop` is used to set default sort column, property `order` is used to set default sort order\n   */\n  defaultSort: Object as PropType<TableProps<DefaultRow>['defaultSort']>,\n  /**\n   * @description the `effect` of the overflow tooltip\n   */\n  tooltipEffect: String,\n  /**\n   * @description the options for the overflow tooltip, [see the following tooltip component](tooltip.html#attributes)\n   */\n  tooltipOptions: Object as PropType<TableProps<DefaultRow>['tooltipOptions']>,\n  /**\n   * @description method that returns rowspan and colspan\n   */\n  spanMethod: Function as PropType<TableProps<DefaultRow>['spanMethod']>,\n  /**\n   * @description controls the behavior of master checkbox in multi-select tables when only some rows are selected (but not all). If true, all rows will be selected, else deselected\n   */\n  selectOnIndeterminate: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description horizontal indentation of tree data\n   */\n  indent: {\n    type: Number,\n    default: 16,\n  },\n  /**\n   * @description configuration for rendering nested data\n   */\n  treeProps: {\n    type: Object as PropType<TableProps<DefaultRow>['treeProps']>,\n    default: () => {\n      return {\n        hasChildren: 'hasChildren',\n        children: 'children',\n        checkStrictly: false,\n      }\n    },\n  },\n  /**\n   * @description whether to lazy loading data\n   */\n  lazy: Boolean,\n  /**\n   * @description method for loading child row data, only works when `lazy` is true\n   */\n  load: Function as PropType<TableProps<DefaultRow>['load']>,\n  style: {\n    type: Object as PropType<CSSProperties>,\n    default: () => ({}),\n  },\n  className: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description sets the algorithm used to lay out table cells, rows, and columns\n   */\n  tableLayout: {\n    type: String as PropType<Layout>,\n    default: 'fixed',\n  },\n  /**\n   * @description always show scrollbar\n   */\n  scrollbarAlwaysOn: Boolean,\n  /**\n   * @description ensure main axis minimum-size doesn't follow the content\n   */\n  flexible: Boolean,\n  /**\n   * @description whether to hide extra content and show them in a tooltip when hovering on the cell.It will affect all the table columns\n   */\n  showOverflowTooltip: [Boolean, Object] as PropType<\n    TableProps<DefaultRow>['showOverflowTooltip']\n  >,\n  /**\n   * @description function that formats cell tooltip content, works when `show-overflow-tooltip` is `true`\n   */\n  tooltipFormatter: Function as PropType<\n    TableProps<DefaultRow>['tooltipFormatter']\n  >,\n  appendFilterPanelTo: String,\n  scrollbarTabindex: {\n    type: [Number, String],\n    default: undefined,\n  },\n  /**\n   * @description whether to allow drag the last column\n   */\n  allowDragLastColumn: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to preserve expanded row content in DOM when collapsed\n   */\n  preserveExpandedContent: {\n    type: Boolean,\n    default: false,\n  },\n}\nexport type {\n  SummaryMethod,\n  Table,\n  TableProps,\n  TableRefs,\n  ColumnCls,\n  ColumnStyle,\n  CellCls,\n  CellStyle,\n  TreeNode,\n  RenderRowData,\n  Sort,\n  Filter,\n  TableColumnCtx,\n  TreeProps,\n  TableTooltipData,\n}\n"], "names": ["useSizeProp"], "mappings": ";;;;;;AACA,mBAAe;AACf,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,IAAI,EAAEA,iBAAW;AACnB,EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AACzB,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,EAAE,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC5B,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,OAAO,EAAE,MAAM;AACjB,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAClC,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC9B,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AACnC,EAAE,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC/B,EAAE,kBAAkB,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AACxC,EAAE,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AACpC,EAAE,mBAAmB,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AACzC,EAAE,eAAe,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AACrC,EAAE,mBAAmB,EAAE,OAAO;AAC9B,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AACjC,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,aAAa,EAAE,KAAK;AACtB,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,aAAa,EAAE,MAAM;AACvB,EAAE,cAAc,EAAE,MAAM;AACxB,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,qBAAqB,EAAE;AACzB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO;AACb,QAAQ,WAAW,EAAE,aAAa;AAClC,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,aAAa,EAAE,KAAK;AAC5B,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,mBAAmB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AACxC,EAAE,gBAAgB,EAAE,QAAQ;AAC5B,EAAE,mBAAmB,EAAE,MAAM;AAC7B,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,uBAAuB,EAAE;AAC3B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC;;;;"}