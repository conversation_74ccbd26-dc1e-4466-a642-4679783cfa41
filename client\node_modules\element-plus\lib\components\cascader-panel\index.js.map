{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/cascader-panel/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CascaderPanel from './src/index.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCascaderPanel: SFCWithInstall<typeof CascaderPanel> =\n  withInstall(CascaderPanel)\n\nexport default ElCascaderPanel\nexport * from './src/types'\nexport * from './src/config'\nexport * from './src/instance'\n"], "names": ["withInstall", "CascaderPanel"], "mappings": ";;;;;;;;;AAEY,MAAC,eAAe,GAAGA,mBAAW,CAACC,gBAAa;;;;;;;;;"}