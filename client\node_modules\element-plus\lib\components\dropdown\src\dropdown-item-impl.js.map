{"version": 3, "file": "dropdown-item-impl.js", "sources": ["../../../../../../packages/components/dropdown/src/dropdown-item-impl.vue"], "sourcesContent": ["<template>\n  <li\n    v-if=\"divided\"\n    role=\"separator\"\n    :class=\"ns.bem('menu', 'item', 'divided')\"\n  />\n  <li\n    :ref=\"itemRef\"\n    v-bind=\"{ ...dataset, ...$attrs }\"\n    :aria-disabled=\"disabled\"\n    :class=\"[ns.be('menu', 'item'), ns.is('disabled', disabled)]\"\n    :tabindex=\"tabIndex\"\n    :role=\"role\"\n    @click=\"(e) => $emit('clickimpl', e)\"\n    @focus=\"handleFocus\"\n    @keydown.self=\"handleKeydown\"\n    @mousedown=\"handleMousedown\"\n    @pointermove=\"(e) => $emit('pointermove', e)\"\n    @pointerleave=\"(e) => $emit('pointerleave', e)\"\n  >\n    <el-icon v-if=\"icon\">\n      <component :is=\"icon\" />\n    </el-icon>\n    <slot />\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, inject } from 'vue'\nimport {\n  ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY,\n  ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY,\n} from '@element-plus/components/roving-focus-group'\nimport { COLLECTION_ITEM_SIGN } from '@element-plus/components/collection'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { composeEventHandlers, composeRefs } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport {\n  DROPDOWN_COLLECTION_ITEM_INJECTION_KEY,\n  dropdownItemProps,\n} from './dropdown'\nimport { DROPDOWN_INJECTION_KEY } from './tokens'\n\nexport default defineComponent({\n  name: 'DropdownItemImpl',\n  components: {\n    ElIcon,\n  },\n  props: dropdownItemProps,\n  emits: ['pointermove', 'pointerleave', 'click', 'clickimpl'],\n  setup(_, { emit }) {\n    const ns = useNamespace('dropdown')\n\n    const { role: menuRole } = inject(DROPDOWN_INJECTION_KEY, undefined)!\n\n    const { collectionItemRef: dropdownCollectionItemRef } = inject(\n      DROPDOWN_COLLECTION_ITEM_INJECTION_KEY,\n      undefined\n    )!\n\n    const { collectionItemRef: rovingFocusCollectionItemRef } = inject(\n      ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n\n    const {\n      rovingFocusGroupItemRef,\n      tabIndex,\n      handleFocus,\n      handleKeydown: handleItemKeydown,\n      handleMousedown,\n    } = inject(ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY, undefined)!\n\n    const itemRef = composeRefs(\n      dropdownCollectionItemRef,\n      rovingFocusCollectionItemRef,\n      rovingFocusGroupItemRef\n    )\n\n    const role = computed<string>(() => {\n      if (menuRole.value === 'menu') {\n        return 'menuitem'\n      } else if (menuRole.value === 'navigation') {\n        return 'link'\n      }\n      return 'button'\n    })\n\n    const handleKeydown = composeEventHandlers((e: KeyboardEvent) => {\n      if (\n        [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space].includes(\n          e.code\n        )\n      ) {\n        e.preventDefault()\n        e.stopImmediatePropagation()\n        emit('clickimpl', e)\n        return true\n      }\n    }, handleItemKeydown)\n\n    return {\n      ns,\n      itemRef,\n      dataset: {\n        [COLLECTION_ITEM_SIGN]: '',\n      },\n      role,\n      tabIndex,\n      handleFocus,\n      handleKeydown,\n      handleMousedown,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElIcon", "dropdownItemProps", "useNamespace", "inject", "DROPDOWN_INJECTION_KEY", "DROPDOWN_COLLECTION_ITEM_INJECTION_KEY", "ROVING_FOCUS_ITEM_COLLECTION_INJECTION_KEY", "ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY", "composeRefs", "computed", "composeEventHandlers", "EVENT_CODE", "COLLECTION_ITEM_SIGN", "_resolveComponent", "_openBlock", "_createElementBlock", "_Fragment", "_normalizeClass", "_createCommentVNode", "_createElementVNode", "_mergeProps", "_createBlock", "_withCtx", "_resolveDynamicComponent"], "mappings": ";;;;;;;;;;;;;;;;;AA4CA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,kBAAA;AAAA,EACN,UAAY,EAAA;AAAA,YACVC,YAAA;AAAA,GACF;AAAA,EACA,KAAO,EAAAC,0BAAA;AAAA,EACP,KAAO,EAAA,CAAC,aAAe,EAAA,cAAA,EAAgB,SAAS,WAAW,CAAA;AAAA,EAC3D,KAAM,CAAA,CAAA,EAAG,EAAE,IAAA,EAAQ,EAAA;AACjB,IAAM,MAAA,EAAA,GAAKC,qBAAa,UAAU,CAAA,CAAA;AAElC,IAAA,MAAM,EAAE,IAAM,EAAA,QAAA,EAAa,GAAAC,UAAA,CAAOC,+BAAwB,KAAS,CAAA,CAAA,CAAA;AAEnE,IAAM,MAAA,EAAE,iBAAmB,EAAA,yBAAA,EAA8B,GAAAD,UAAA,CAAAE,+CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACvD,MAAA,EAAA,iBAAA,EAAA,4BAAA,EAAA,GAAAF,UAAA,CAAAG,2DAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACF,uBAAA;AAEA,MAAM,QAAqB;AAAiC,MAC1D,WAAA;AAAA,MACA,aAAA,EAAA,iBAAA;AAAA,MACF,eAAA;AAEA,KAAM,GAAAH,UAAA,CAAAI,8CAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACJ,MAAA,OAAA,GAAAC,gBAAA,CAAA,yBAAA,EAAA,4BAAA,EAAA,uBAAA,CAAA,CAAA;AAAA,IACA,MAAA,IAAA,GAAAC,YAAA,CAAA,MAAA;AAAA,MACA,IAAA,QAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AAAA,QACe,OAAA,UAAA,CAAA;AAAA,OACf,MAAA,IAAA,QAAA,CAAA,KAAA,KAAA,YAAA,EAAA;AAAA,QACE,OAAO,MAAA,CAAA;AAEX,OAAA;AAAgB,MACd,OAAA,QAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,aAAA,GAAAC,0BAAA,CAAA,CAAA,CAAA,KAAA;AAAA,MACF,IAAA,CAAAC,eAAA,CAAA,KAAA,EAAAA,eAAA,CAAA,WAAA,EAAAA,eAAA,CAAA,KAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAAA,EAAA;AAEA,QAAM,CAAA,CAAA,gBAAwB,CAAM;AAClC,QAAI,CAAA,CAAA,wBAA2B,EAAA,CAAA;AAC7B,QAAO,IAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA;AAAA,QACT,OAAA,IAAoB,CAAA;AAClB,OAAO;AAAA,KACT,EAAA,iBAAA,CAAA,CAAA;AACA,IAAO,OAAA;AAAA,MACR,EAAA;AAED,MAAM,OAAA;AACJ,MAAA,OACc,EAAA;AAAiD,QAC3D,CAAEC,+BAAA,GAAA,EAAA;AAAA,OAEJ;AACA,MAAA,IAAE;AACF,MAAA,QAA2B;AAC3B,MAAA;AACA,MAAO,aAAA;AAAA,MACT,eAAA;AAAA;AAGF,GAAO;AAAA,CACL,CAAA,CAAA;AACA,SACS,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACP,MAAA,qBAAqBC,oBAAG,CAAA,SAAA,CAAA,CAAA;AAAA,EAC1B,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAAC,YAAA,EAAA,IAAA,EAAA;AAAA,IACA,IAAA,CAAA,OAAA,IAAAF,aAAA,EAAA,EAAAC,sBAAA,CAAA,IAAA,EAAA;AAAA,MACA,GAAA,EAAA,CAAA;AAAA,MACA,IAAA,EAAA,WAAA;AAAA,MACA,KAAA,EAAAE,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,EAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,KACA,EAAA,IAAA,EAAA,CAAA,CAAA,IAAAC,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,IACFC,sBAAA,CAAA,IAAA,EAAAC,cAAA,CAAA,EAAA,GAAA,EAAA,IAAA,CAAA,OAAA,EAAA,EAAA,EAAA,GAAA,IAAA,CAAA,OAAA,EAAA,GAAA,IAAA,CAAA,MAAA,EAAA,EAAA;AAAA,MACF,eAAA,EAAA,IAAA,CAAA,QAAA;AACF,MAAC,KAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;MAjHS,cADR,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,cAAA,EAAA,CAAA,CAAA;AAAA,KAIE,CAAA,EAAA;AAAA,MAAA,IAAA,CAAA,IAAA,IAAAN,aAAA,EAAA,EAAAO,eAAA,CAAA,kBAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAAC,WAAA,CAAA,MAAA;WAFKR,aAAA,EAAA,EAAAO,eAAA,CAAAE,2BAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAAA,SACJ,CAAA;AAAa,QAAA,CAAA,EAAA,CAAA;;;;;;AAKE,yBACV,2DAA6B,EAAA,CAAE,sBAAqB,CAAA,EAAA,CAAA,QAAA,EAAA,wBAAA,CAAA,CAAA,CAAA;;;;"}