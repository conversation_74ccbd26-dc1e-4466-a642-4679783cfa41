{"version": 3, "file": "select-dropdown.mjs", "sources": ["../../../../../../packages/components/select/src/select-dropdown.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('dropdown'), ns.is('multiple', isMultiple), popperClass]\"\n    :style=\"{ [isFitInputWidth ? 'width' : 'minWidth']: minWidth }\"\n  >\n    <div v-if=\"$slots.header\" :class=\"ns.be('dropdown', 'header')\">\n      <slot name=\"header\" />\n    </div>\n    <slot />\n    <div v-if=\"$slots.footer\" :class=\"ns.be('dropdown', 'footer')\">\n      <slot name=\"footer\" />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, inject, onMounted, ref } from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { useNamespace } from '@element-plus/hooks'\nimport { selectKey } from './token'\n\nexport default defineComponent({\n  name: 'ElSelectDropdown',\n\n  componentName: 'ElSelectDropdown',\n\n  setup() {\n    const select = inject(selectKey)!\n    const ns = useNamespace('select')\n\n    // computed\n    const popperClass = computed(() => select.props.popperClass)\n    const isMultiple = computed(() => select.props.multiple)\n    const isFitInputWidth = computed(() => select.props.fitInputWidth)\n    const minWidth = ref('')\n\n    function updateMinWidth() {\n      minWidth.value = `${select.selectRef?.offsetWidth}px`\n    }\n\n    onMounted(() => {\n      // TODO: updatePopper\n      // popper.value.update()\n      updateMinWidth()\n      useResizeObserver(select.selectRef, updateMinWidth)\n    })\n\n    return {\n      ns,\n      minWidth,\n      popperClass,\n      isMultiple,\n      isFitInputWidth,\n    }\n  },\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_normalizeStyle", "_renderSlot", "_createCommentVNode"], "mappings": ";;;;;;AAqBA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,kBAAA;AAAA,EAEN,aAAe,EAAA,kBAAA;AAAA,EAEf,KAAQ,GAAA;AACN,IAAM,MAAA,MAAA,GAAS,OAAO,SAAS,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAGhC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAS,MAAM,MAAA,CAAO,MAAM,WAAW,CAAA,CAAA;AAC3D,IAAA,MAAM,UAAa,GAAA,QAAA,CAAS,MAAM,MAAA,CAAO,MAAM,QAAQ,CAAA,CAAA;AACvD,IAAA,MAAM,eAAkB,GAAA,QAAA,CAAS,MAAM,MAAA,CAAO,MAAM,aAAa,CAAA,CAAA;AACjE,IAAM,MAAA,QAAA,GAAW,IAAI,EAAE,CAAA,CAAA;AAEvB,IAAA,SAAS,cAAiB,GAAA;AACxB,MAAA,IAAA,EAAA,CAAA;AAAiD,MACnD,QAAA,CAAA,KAAA,GAAA,CAAA,EAAA,CAAA,EAAA,GAAA,MAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAEA,KAAA;AAGE,IAAe,SAAA,CAAA,MAAA;AACf,MAAkB,cAAA,EAAA,CAAA;AAAgC,MACnD,iBAAA,CAAA,MAAA,CAAA,SAAA,EAAA,cAAA,CAAA,CAAA;AAED,KAAO,CAAA,CAAA;AAAA,IACL,OAAA;AAAA,MACA,EAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,UAAA;AAAA,MACF,eAAA;AAAA,KACF,CAAA;AACF,GAAC;;AAtDC,SAAA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EAWM,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,IAAA,KAAA,EAAAC,cAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA,CAAA;AAAA,IAVH,KAAA,EAAAC,cAAQ,CAAA,EAAA,CAAA,IAAA,CAAA,eAAI,GAAc,UAAK,UAAA,GAAa,IAAU,CAAA,QAAA,EAAA,CAAA;AAAc,GAAA,EAAA;AACT,IAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAH,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;AAEjD,MAAA,KAAA,EAAAC,cAAX,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,KAEM,EAAA;AAAA,MAAAE,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,KAAA,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;cAF0B,CAAA,IAAA,CAAA,MAAA,EAAA;AAAO,IAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAL,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;WACf,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,KAAA,EAAA;;;;;AAGxB,mBAEM,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,qBAAA,CAAA,CAAA,CAAA;;;;"}