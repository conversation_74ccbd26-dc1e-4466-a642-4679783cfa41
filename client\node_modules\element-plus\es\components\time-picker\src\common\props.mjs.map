{"version": 3, "file": "props.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/common/props.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport { buildProps, definePropType } from '@element-plus/utils'\nimport {\n  useAriaProps,\n  useEmptyValuesProps,\n  useSizeProp,\n} from '@element-plus/hooks'\nimport { CircleClose } from '@element-plus/icons-vue'\nimport { disabledTimeListsProps } from '../props/shared'\n\nimport type { Component, ExtractPropTypes } from 'vue'\nimport type { Options } from '@popperjs/core'\nimport type { Dayjs } from 'dayjs'\nimport type { Placement } from '@element-plus/components/popper'\n\nexport type SingleOrRange<T> = T | [T, T]\nexport type DateModelType = number | string | Date\nexport type ModelValueType = SingleOrRange<DateModelType> | string[]\nexport type DayOrDays = SingleOrRange<Dayjs>\nexport type DateOrDates = SingleOrRange<Date>\nexport type UserInput = SingleOrRange<string | null>\nexport type GetDisabledHours = (role: string, comparingDate?: Dayjs) => number[]\nexport type GetDisabledMinutes = (\n  hour: number,\n  role: string,\n  comparingDate?: Dayjs\n) => number[]\nexport type GetDisabledSeconds = (\n  hour: number,\n  minute: number,\n  role: string,\n  comparingDate?: Dayjs\n) => number[]\n\nexport const timePickerDefaultProps = buildProps({\n  /**\n   * @description same as `id` in native input\n   */\n  id: {\n    type: definePropType<SingleOrRange<string>>([Array, String]),\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: {\n    type: definePropType<SingleOrRange<string>>([Array, String]),\n  },\n  /**\n   * @description custom class name for TimePicker's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description format of the displayed value in the input box\n   */\n  format: String,\n  /**\n   * @description optional, format of binding value. If not specified, the binding value will be a Date object\n   */\n  valueFormat: String,\n  /**\n   * @description optional, format of the date displayed value in TimePicker's dropdown\n   */\n  dateFormat: String,\n  /**\n   * @description optional, format of the time displayed value in TimePicker's dropdown\n   */\n  timeFormat: String,\n  /**\n   * @description type of the picker\n   */\n  type: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether to show clear button\n   */\n  clearable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Custom clear icon component\n   */\n  clearIcon: {\n    type: definePropType<string | Component>([String, Object]),\n    default: CircleClose,\n  },\n  /**\n   * @description whether the input is editable\n   */\n  editable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Custom prefix icon component\n   */\n  prefixIcon: {\n    type: definePropType<string | Component>([String, Object]),\n    default: '',\n  },\n  /**\n   * @description size of Input\n   */\n  size: useSizeProp,\n  /**\n   * @description whether TimePicker is read only\n   */\n  readonly: Boolean,\n  /**\n   * @description whether TimePicker is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description placeholder in non-range mode\n   */\n  placeholder: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description [popper.js](https://popper.js.org/docs/v2/) parameters\n   */\n  popperOptions: {\n    type: definePropType<Partial<Options>>(Object),\n    default: () => ({}),\n  },\n  /**\n   * @description binding value, if it is an array, the length should be 2\n   */\n  modelValue: {\n    type: definePropType<ModelValueType>([Date, Array, String, Number]),\n    default: '',\n  },\n  /**\n   * @description range separator\n   */\n  rangeSeparator: {\n    type: String,\n    default: '-',\n  },\n  /**\n   * @description placeholder for the start date in range mode\n   */\n  startPlaceholder: String,\n  /**\n   * @description placeholder for the end date in range mode\n   */\n  endPlaceholder: String,\n  /**\n   * @description optional, default date of the calendar\n   */\n  defaultValue: {\n    type: definePropType<SingleOrRange<Date>>([Date, Array]),\n  },\n  /**\n   * @description optional, the time value to use when selecting date range\n   */\n  defaultTime: {\n    type: definePropType<SingleOrRange<Date>>([Date, Array]),\n  },\n  /**\n   * @description whether to pick a time range\n   */\n  isRange: Boolean,\n  ...disabledTimeListsProps,\n  /**\n   * @description a function determining if a date is disabled with that date as its parameter. Should return a Boolean\n   */\n  disabledDate: {\n    type: Function,\n  },\n  /**\n   * @description set custom className\n   */\n  cellClassName: {\n    type: Function,\n  },\n  /**\n   * @description an object array to set shortcut options\n   */\n  shortcuts: {\n    type: Array,\n    default: () => [],\n  },\n  /**\n   * @description whether to pick time using arrow buttons\n   */\n  arrowControl: Boolean,\n  /**\n   * @description input tabindex\n   */\n  tabindex: {\n    type: definePropType<string | number>([String, Number]),\n    default: 0,\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description unlink two date-panels in range-picker\n   */\n  unlinkPanels: Boolean,\n  /**\n   * @description position of dropdown\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: placements,\n    default: 'bottom',\n  },\n  /**\n   * @description list of possible positions for dropdown\n   */\n  fallbackPlacements: {\n    type: definePropType<Placement[]>(Array),\n    default: ['bottom', 'top', 'right', 'left'],\n  },\n  ...useEmptyValuesProps,\n  ...useAriaProps(['ariaLabel']),\n  /**\n   * @description whether to show the now button\n   */\n  showNow: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\n\nexport type TimePickerDefaultProps = ExtractPropTypes<\n  typeof timePickerDefaultProps\n>\n\nexport interface PickerOptions {\n  isValidValue: (date: DayOrDays) => boolean\n  handleKeydownInput: (event: KeyboardEvent) => void\n  parseUserInput: (value: UserInput) => DayOrDays\n  formatToString: (value: DayOrDays) => UserInput\n  getRangeAvailableTime: (date: DayOrDays) => DayOrDays\n  getDefaultValue: () => DayOrDays\n  panelReady: boolean\n  handleClear: () => void\n  handleFocusPicker?: () => void\n}\n\nexport const timePickerRangeTriggerProps = buildProps({\n  id: {\n    type: definePropType<string[]>(Array),\n  },\n  name: {\n    type: definePropType<string[]>(Array),\n  },\n  modelValue: {\n    type: definePropType<UserInput>([Array, String]),\n  },\n  startPlaceholder: String,\n  endPlaceholder: String,\n  disabled: Boolean,\n} as const)\n\n/**\n * @deprecated Use `timePickerRangeTriggerProps` instead. This will be removed in future versions.\n */\nexport const timePickerRngeTriggerProps = timePickerRangeTriggerProps\n"], "names": [], "mappings": ";;;;;;;;AASY,MAAC,sBAAsB,GAAG,UAAU,CAAC;AACjD,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,WAAW;AACxB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACvD,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,gBAAgB,EAAE,MAAM;AAC1B,EAAE,cAAc,EAAE,MAAM;AACxB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,GAAG,sBAAsB;AAC3B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAC/C,GAAG;AACH,EAAE,GAAG,mBAAmB;AACxB,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,2BAA2B,GAAG,UAAU,CAAC;AACtD,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,gBAAgB,EAAE,MAAM;AAC1B,EAAE,cAAc,EAAE,MAAM;AACxB,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC,EAAE;AACS,MAAC,0BAA0B,GAAG;;;;"}