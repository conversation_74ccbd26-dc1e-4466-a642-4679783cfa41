{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-model-toggle/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance, onMounted, watch } from 'vue'\nimport {\n  buildProp,\n  definePropType,\n  isBoolean,\n  isClient,\n  isFunction,\n} from '@element-plus/utils'\nimport type { ExtractPropType } from '@element-plus/utils'\nimport type { RouteLocationNormalizedLoaded } from 'vue-router'\n\nimport type { ComponentPublicInstance, ExtractPropTypes, Ref } from 'vue'\n\nconst _prop = buildProp({\n  type: definePropType<boolean | null>(Boolean),\n  default: null,\n} as const)\nconst _event = buildProp({\n  type: definePropType<(val: boolean) => void>(Function),\n} as const)\n\nexport type UseModelTogglePropsRaw<T extends string> = {\n  [K in T]: typeof _prop\n} & {\n  [K in `onUpdate:${T}`]: typeof _event\n}\n\nexport type UseModelTogglePropsGeneric<T extends string> = {\n  [K in T]: ExtractPropType<typeof _prop>\n} & {\n  [K in `onUpdate:${T}`]: ExtractPropType<typeof _event>\n}\n\nexport const createModelToggleComposable = <T extends string>(name: T) => {\n  const updateEventKey = `update:${name}` as const\n  const updateEventKeyRaw = `onUpdate:${name}` as const\n  const useModelToggleEmits = [updateEventKey]\n\n  const useModelToggleProps = {\n    [name]: _prop,\n    [updateEventKeyRaw]: _event,\n  } as UseModelTogglePropsRaw<T>\n\n  const useModelToggle = ({\n    indicator,\n    toggleReason,\n    shouldHideWhenRouteChanges,\n    shouldProceed,\n    onShow,\n    onHide,\n  }: ModelToggleParams) => {\n    const instance = getCurrentInstance()!\n    const { emit } = instance\n    const props = instance.props as UseModelTogglePropsGeneric<T> & {\n      disabled: boolean\n    }\n    const hasUpdateHandler = computed(() =>\n      isFunction(props[updateEventKeyRaw])\n    )\n    // when it matches the default value we say this is absent\n    // though this could be mistakenly passed from the user but we need to rule out that\n    // condition\n    const isModelBindingAbsent = computed(() => props[name] === null)\n\n    const doShow = (event?: Event) => {\n      if (indicator.value === true) {\n        return\n      }\n\n      indicator.value = true\n      if (toggleReason) {\n        toggleReason.value = event\n      }\n      if (isFunction(onShow)) {\n        onShow(event)\n      }\n    }\n\n    const doHide = (event?: Event) => {\n      if (indicator.value === false) {\n        return\n      }\n\n      indicator.value = false\n      if (toggleReason) {\n        toggleReason.value = event\n      }\n      if (isFunction(onHide)) {\n        onHide(event)\n      }\n    }\n\n    const show = (event?: Event) => {\n      if (\n        props.disabled === true ||\n        (isFunction(shouldProceed) && !shouldProceed())\n      )\n        return\n\n      const shouldEmit = hasUpdateHandler.value && isClient\n\n      if (shouldEmit) {\n        emit(updateEventKey, true)\n      }\n\n      if (isModelBindingAbsent.value || !shouldEmit) {\n        doShow(event)\n      }\n    }\n\n    const hide = (event?: Event) => {\n      if (props.disabled === true || !isClient) return\n\n      const shouldEmit = hasUpdateHandler.value && isClient\n\n      if (shouldEmit) {\n        emit(updateEventKey, false)\n      }\n\n      if (isModelBindingAbsent.value || !shouldEmit) {\n        doHide(event)\n      }\n    }\n\n    const onChange = (val: boolean) => {\n      if (!isBoolean(val)) return\n      if (props.disabled && val) {\n        if (hasUpdateHandler.value) {\n          emit(updateEventKey, false)\n        }\n      } else if (indicator.value !== val) {\n        if (val) {\n          doShow()\n        } else {\n          doHide()\n        }\n      }\n    }\n\n    const toggle = () => {\n      if (indicator.value) {\n        hide()\n      } else {\n        show()\n      }\n    }\n\n    watch(() => props[name], onChange)\n\n    if (\n      shouldHideWhenRouteChanges &&\n      instance.appContext.config.globalProperties.$route !== undefined\n    ) {\n      watch(\n        () => ({\n          ...(\n            instance.proxy as ComponentPublicInstance<{\n              $route: RouteLocationNormalizedLoaded\n            }>\n          ).$route,\n        }),\n        () => {\n          if (shouldHideWhenRouteChanges.value && indicator.value) {\n            hide()\n          }\n        }\n      )\n    }\n\n    onMounted(() => {\n      onChange(props[name])\n    })\n\n    return {\n      hide,\n      show,\n      toggle,\n      hasUpdateHandler,\n    }\n  }\n\n  return {\n    useModelToggle,\n    useModelToggleProps,\n    useModelToggleEmits,\n  }\n}\n\nconst { useModelToggle, useModelToggleProps, useModelToggleEmits } =\n  createModelToggleComposable('modelValue')\n\nexport { useModelToggle, useModelToggleEmits, useModelToggleProps }\n\nexport type UseModelToggleProps = ExtractPropTypes<typeof useModelToggleProps>\n\nexport type ModelToggleParams = {\n  indicator: Ref<boolean>\n  toggleReason?: Ref<Event | undefined>\n  shouldHideWhenRouteChanges?: Ref<boolean>\n  shouldProceed?: () => boolean\n  onShow?: (event?: Event) => void\n  onHide?: (event?: Event) => void\n}\n"], "names": ["buildProp", "definePropType", "getCurrentInstance", "computed", "isFunction", "isClient", "isBoolean", "watch", "onMounted"], "mappings": ";;;;;;;;;;AAQA,MAAM,KAAK,GAAGA,iBAAS,CAAC;AACxB,EAAE,IAAI,EAAEC,sBAAc,CAAC,OAAO,CAAC;AAC/B,EAAE,OAAO,EAAE,IAAI;AACf,CAAC,CAAC,CAAC;AACH,MAAM,MAAM,GAAGD,iBAAS,CAAC;AACzB,EAAE,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAChC,CAAC,CAAC,CAAC;AACS,MAAC,2BAA2B,GAAG,CAAC,IAAI,KAAK;AACrD,EAAE,MAAM,cAAc,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1C,EAAE,MAAM,iBAAiB,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C,EAAE,MAAM,oBAAoB,GAAG,CAAC,cAAc,CAAC,CAAC;AAChD,EAAE,MAAM,oBAAoB,GAAG;AAC/B,IAAI,CAAC,IAAI,GAAG,KAAK;AACjB,IAAI,CAAC,iBAAiB,GAAG,MAAM;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC;AAC3B,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,0BAA0B;AAC9B,IAAI,aAAa;AACjB,IAAI,MAAM;AACV,IAAI,MAAM;AACV,GAAG,KAAK;AACR,IAAI,MAAM,QAAQ,GAAGC,sBAAkB,EAAE,CAAC;AAC1C,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;AAC9B,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjC,IAAI,MAAM,gBAAgB,GAAGC,YAAQ,CAAC,MAAMC,iBAAU,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAClF,IAAI,MAAM,oBAAoB,GAAGD,YAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AACtE,IAAI,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;AAC9B,MAAM,IAAI,SAAS,CAAC,KAAK,KAAK,IAAI,EAAE;AACpC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7B,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,OAAO;AACP,MAAM,IAAIC,iBAAU,CAAC,MAAM,CAAC,EAAE;AAC9B,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;AAC9B,MAAM,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,EAAE;AACrC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,OAAO;AACP,MAAM,IAAIA,iBAAU,CAAC,MAAM,CAAC,EAAE;AAC9B,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;AAC5B,MAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAIA,iBAAU,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE;AAClF,QAAQ,OAAO;AACf,MAAM,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,IAAIC,aAAQ,CAAC;AAC5D,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,oBAAoB,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE;AACrD,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;AAC5B,MAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAI,CAACA,aAAQ;AAC9C,QAAQ,OAAO;AACf,MAAM,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,IAAIA,aAAQ,CAAC;AAC5D,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AACpC,OAAO;AACP,MAAM,IAAI,oBAAoB,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE;AACrD,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;AAC9B,MAAM,IAAI,CAACC,eAAS,CAAC,GAAG,CAAC;AACzB,QAAQ,OAAO;AACf,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,GAAG,EAAE;AACjC,QAAQ,IAAI,gBAAgB,CAAC,KAAK,EAAE;AACpC,UAAU,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AACtC,SAAS;AACT,OAAO,MAAM,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,EAAE;AAC1C,QAAQ,IAAI,GAAG,EAAE;AACjB,UAAU,MAAM,EAAE,CAAC;AACnB,SAAS,MAAM;AACf,UAAU,MAAM,EAAE,CAAC;AACnB,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,MAAM;AACzB,MAAM,IAAI,SAAS,CAAC,KAAK,EAAE;AAC3B,QAAQ,IAAI,EAAE,CAAC;AACf,OAAO,MAAM;AACb,QAAQ,IAAI,EAAE,CAAC;AACf,OAAO;AACP,KAAK,CAAC;AACN,IAAIC,SAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;AACvC,IAAI,IAAI,0BAA0B,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,KAAK,KAAK,CAAC,EAAE;AACrG,MAAMA,SAAK,CAAC,OAAO;AACnB,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM;AAChC,OAAO,CAAC,EAAE,MAAM;AAChB,QAAQ,IAAI,0BAA0B,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE;AACjE,UAAU,IAAI,EAAE,CAAC;AACjB,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAIC,aAAS,CAAC,MAAM;AACpB,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,gBAAgB;AACtB,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,cAAc,EAAE,eAAe;AACnC,IAAI,mBAAmB,EAAE,oBAAoB;AAC7C,IAAI,mBAAmB,EAAE,oBAAoB;AAC7C,GAAG,CAAC;AACJ,EAAE;AACG,MAAC,EAAE,cAAc,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,GAAG,2BAA2B,CAAC,YAAY;;;;;;;"}