{"version": 3, "file": "token.js", "sources": ["../../../../../../packages/components/select/src/token.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\nimport type { SelectContext, SelectGroupContext } from './type'\n\n// For individual build sharing injection key, we had to make `Symbol` to string\nexport const selectGroupKey: InjectionKey<SelectGroupContext> =\n  Symbol('ElSelectGroup')\n\nexport const selectKey: InjectionKey<SelectContext> = Symbol('ElSelect')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,cAAc,GAAG,MAAM,CAAC,eAAe,EAAE;AAC1C,MAAC,SAAS,GAAG,MAAM,CAAC,UAAU;;;;;"}