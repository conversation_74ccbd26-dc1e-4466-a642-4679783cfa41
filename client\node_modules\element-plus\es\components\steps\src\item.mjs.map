{"version": 3, "file": "item.mjs", "sources": ["../../../../../../packages/components/steps/src/item.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type Step from './item.vue'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const stepProps = buildProps({\n  /**\n   * @description step title\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description step custom icon. Icons can be passed via named slot as well\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description step description\n   */\n  description: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description current status. It will be automatically set by Steps if not configured.\n   */\n  status: {\n    type: String,\n    values: ['', 'wait', 'process', 'finish', 'error', 'success'],\n    default: '',\n  },\n} as const)\n\nexport type StepProps = ExtractPropTypes<typeof stepProps>\n\nexport type StepInstance = InstanceType<typeof Step> & unknown\n"], "names": [], "mappings": ";;;AACY,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AACjE,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}