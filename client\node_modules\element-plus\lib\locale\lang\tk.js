'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tk = {
  name: "tk",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Arassala"
    },
    datepicker: {
      now: "\u015Euwagt",
      today: "\u015E\xFCg\xFCn",
      cancel: "Bes et",
      clear: "Arassala",
      confirm: "OK",
      selectDate: "G\xFCni sa\xFDla\u0148",
      selectTime: "Wagty sa\xFDla\u0148",
      startDate: "Ba\u015Fla\xFDan g\xFCni",
      startTime: "Ba\u015Fla\xFDan wagty",
      endDate: "Gutar\xFDan g\xFCni",
      endTime: "Gutar\xFDan wagty",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "",
      month1: "\xDDan",
      month2: "Few",
      month3: "Mar",
      month4: "Apr",
      month5: "Ma\xFD",
      month6: "I\xFDn",
      month7: "I\xFDl",
      month8: "Awg",
      month9: "Sen",
      month10: "Okt",
      month11: "No\xFD",
      month12: "Dek",
      weeks: {
        sun: "\xDDek",
        mon: "Du\u015F",
        tue: "Si\u015F",
        wed: "\xC7ar",
        thu: "Pen",
        fri: "Ann",
        sat: "\u015Een"
      },
      months: {
        jan: "\xDDan",
        feb: "Few",
        mar: "Mar",
        apr: "Apr",
        may: "Ma\xFD",
        jun: "I\xFDn",
        jul: "I\xFDl",
        aug: "Awg",
        sep: "Sep",
        oct: "Okt",
        nov: "No\xFD",
        dec: "Dek"
      }
    },
    select: {
      loading: "Indiril\xFD\xE4r",
      noMatch: "Hi\xE7zat tapylmady",
      noData: "Hi\xE7zat \xFDok",
      placeholder: "Sa\xFDla"
    },
    mention: {
      loading: "Indiril\xFD\xE4r"
    },
    cascader: {
      noMatch: "Hi\xE7zat tapylmady",
      loading: "Indiril\xFD\xE4r",
      placeholder: "Sa\xFDla\u0148",
      noData: "Hi\xE7zat \xFDok"
    },
    pagination: {
      goto: "Git",
      pagesize: "/sahypa",
      total: "Umumy {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Hat",
      confirm: "OK",
      cancel: "Bes et",
      error: "\xDDal\u0148y\u015F girizme"
    },
    upload: {
      deleteTip: 'Pozmak \xFC\xE7in "poz" d\xFCwm\xE4 basy\u0148',
      delete: "Poz",
      preview: "G\xF6r",
      continue: "Dowam et"
    },
    table: {
      emptyText: "Maglumat \xFDok",
      confirmFilter: "Tassykla",
      resetFilter: "Arassala",
      clearFilter: "Hemmesi",
      sumText: "Jemi"
    },
    tree: {
      emptyText: "Maglumat \xFDok"
    },
    transfer: {
      noMatch: "Hi\xE7zat tapylmady",
      noData: "Hi\xE7zat \xFDok",
      titles: ["Sanaw 1", "Sanaw 2"],
      filterPlaceholder: "G\xF6zleg s\xF6zlerini girizi\u0148",
      noCheckedFormat: "{total} sany",
      hasCheckedFormat: "{checked}/{total} sa\xFDlanan"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

exports["default"] = tk;
//# sourceMappingURL=tk.js.map
