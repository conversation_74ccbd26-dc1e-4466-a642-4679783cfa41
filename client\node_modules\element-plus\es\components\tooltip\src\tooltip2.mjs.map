{"version": 3, "file": "tooltip2.mjs", "sources": ["../../../../../../packages/components/tooltip/src/tooltip.vue"], "sourcesContent": ["<template>\n  <el-popper ref=\"popperRef\" :role=\"role\">\n    <el-tooltip-trigger\n      :disabled=\"disabled\"\n      :trigger=\"trigger\"\n      :trigger-keys=\"triggerKeys\"\n      :virtual-ref=\"virtualRef\"\n      :virtual-triggering=\"virtualTriggering\"\n    >\n      <slot v-if=\"$slots.default\" />\n    </el-tooltip-trigger>\n    <el-tooltip-content\n      ref=\"contentRef\"\n      :aria-label=\"ariaLabel\"\n      :boundaries-padding=\"boundariesPadding\"\n      :content=\"content\"\n      :disabled=\"disabled\"\n      :effect=\"effect\"\n      :enterable=\"enterable\"\n      :fallback-placements=\"fallbackPlacements\"\n      :hide-after=\"hideAfter\"\n      :gpu-acceleration=\"gpuAcceleration\"\n      :offset=\"offset\"\n      :persistent=\"persistent\"\n      :popper-class=\"kls\"\n      :popper-style=\"popperStyle\"\n      :placement=\"placement\"\n      :popper-options=\"popperOptions\"\n      :arrow-offset=\"arrowOffset\"\n      :pure=\"pure\"\n      :raw-content=\"rawContent\"\n      :reference-el=\"referenceEl\"\n      :trigger-target-el=\"triggerTargetEl\"\n      :show-after=\"showAfter\"\n      :strategy=\"strategy\"\n      :teleported=\"teleported\"\n      :transition=\"transition\"\n      :virtual-triggering=\"virtualTriggering\"\n      :z-index=\"zIndex\"\n      :append-to=\"appendTo\"\n    >\n      <slot name=\"content\">\n        <span v-if=\"rawContent\" v-html=\"content\" />\n        <span v-else>{{ content }}</span>\n      </slot>\n      <el-popper-arrow v-if=\"showArrow\" />\n    </el-tooltip-content>\n  </el-popper>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  onDeactivated,\n  provide,\n  readonly,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { ElPopper, ElPopperArrow } from '@element-plus/components/popper'\n\nimport { isBoolean } from '@element-plus/utils'\nimport {\n  useDelayedToggle,\n  useId,\n  useNamespace,\n  usePopperContainer,\n} from '@element-plus/hooks'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { tooltipEmits, useTooltipModelToggle, useTooltipProps } from './tooltip'\nimport ElTooltipTrigger from './trigger.vue'\nimport ElTooltipContent from './content.vue'\nimport type { TooltipContentInstance } from './content'\nimport type { PopperInstance } from '@element-plus/components/popper'\n\ndefineOptions({\n  name: 'ElTooltip',\n})\n\nconst props = defineProps(useTooltipProps)\nconst emit = defineEmits(tooltipEmits)\n\nusePopperContainer()\n\nconst ns = useNamespace('tooltip')\nconst id = useId()\nconst popperRef = ref<PopperInstance>()\nconst contentRef = ref<TooltipContentInstance>()\n\nconst updatePopper = () => {\n  const popperComponent = unref(popperRef)\n  if (popperComponent) {\n    popperComponent.popperInstanceRef?.update()\n  }\n}\nconst open = ref(false)\nconst toggleReason = ref<Event>()\n\nconst { show, hide, hasUpdateHandler } = useTooltipModelToggle({\n  indicator: open,\n  toggleReason,\n})\n\nconst { onOpen, onClose } = useDelayedToggle({\n  showAfter: toRef(props, 'showAfter'),\n  hideAfter: toRef(props, 'hideAfter'),\n  autoClose: toRef(props, 'autoClose'),\n  open: show,\n  close: hide,\n})\n\nconst controlled = computed(\n  () => isBoolean(props.visible) && !hasUpdateHandler.value\n)\n\nconst kls = computed(() => {\n  return [ns.b(), props.popperClass!]\n})\n\nprovide(TOOLTIP_INJECTION_KEY, {\n  controlled,\n  id,\n  open: readonly(open),\n  trigger: toRef(props, 'trigger'),\n  onOpen: (event?: Event) => {\n    onOpen(event)\n  },\n  onClose: (event?: Event) => {\n    onClose(event)\n  },\n  onToggle: (event?: Event) => {\n    if (unref(open)) {\n      onClose(event)\n    } else {\n      onOpen(event)\n    }\n  },\n  onShow: () => {\n    emit('show', toggleReason.value)\n  },\n  onHide: () => {\n    emit('hide', toggleReason.value)\n  },\n  onBeforeShow: () => {\n    emit('before-show', toggleReason.value)\n  },\n  onBeforeHide: () => {\n    emit('before-hide', toggleReason.value)\n  },\n  updatePopper,\n})\n\nwatch(\n  () => props.disabled,\n  (disabled) => {\n    if (disabled && open.value) {\n      open.value = false\n    }\n  }\n)\n\nconst isFocusInsideContent = (event?: FocusEvent) => {\n  return contentRef.value?.isFocusInsideContent(event)\n}\n\nonDeactivated(() => open.value && hide())\n\ndefineExpose({\n  /**\n   * @description el-popper component instance\n   */\n  popperRef,\n  /**\n   * @description el-tooltip-content component instance\n   */\n  contentRef,\n  /**\n   * @description validate current focus event is trigger inside el-tooltip-content\n   */\n  isFocusInsideContent,\n  /**\n   * @description update el-popper component instance\n   */\n  updatePopper,\n  /**\n   * @description expose onOpen function to mange el-tooltip open state\n   */\n  onOpen,\n  /**\n   * @description expose onOpen function to mange el-tooltip open state\n   */\n  onClose,\n  /**\n   * @description expose hide function\n   */\n  hide,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref", "_withCtx", "_createVNode", "_renderSlot", "_createCommentVNode"], "mappings": ";;;;;;;;;;;;;;mCA6Ec,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAmB,kBAAA,EAAA,CAAA;AAEnB,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AACjC,IAAA,MAAM,KAAK,KAAM,EAAA,CAAA;AACjB,IAAA,MAAM,YAAY,GAAoB,EAAA,CAAA;AACtC,IAAA,MAAM,aAAa,GAA4B,EAAA,CAAA;AAE/C,IAAA,MAAM,eAAe,MAAM;AACzB,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAqB,eAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACnB,MAAA,IAAA;AAA0C,QAC5C,CAAA,EAAA,GAAA,eAAA,CAAA,iBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,OACF;AACA,KAAM,CAAA;AACN,IAAA,MAAM,gBAA0B,CAAA,CAAA;AAEhC,IAAA,MAAM,YAAc,GAAA,GAAA,EAAA,CAAA;AAA2C,IAAA,MAClD,EAAA,IAAA,EAAA,IAAA,EAAA,gBAAA,EAAA,GAAA,qBAAA,CAAA;AAAA,MACX,SAAA,EAAA,IAAA;AAAA,MACD,YAAA;AAED,KAAA,CAAA,CAAA;AAA6C,IAC3C,MAAA,EAAA,MAAiB,EAAA,OAAA,EAAA,GAAkB,gBAAA,CAAA;AAAA,MACnC,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,SAAA,EAAW,KAAM,CAAA,KAAA,EAAO,WAAW,CAAA;AAAA,MACnC,SAAM,EAAA,KAAA,CAAA,KAAA,EAAA,WAAA,CAAA;AAAA,MACN,IAAO,EAAA,IAAA;AAAA,MACR,KAAA,EAAA,IAAA;AAED,KAAA,CAAA,CAAA;AAAmB,IAAA,gBACD,GAAA,QAAa,CAAA,MAAA,SAAuB,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACtD,MAAA,GAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAM,OAAA,CAAA,EAAM,WAAe,CAAA,WAAA,CAAA,CAAA;AACzB,KAAA,CAAA,CAAA;AAAkC,IACpC,OAAC,CAAA,qBAAA,EAAA;AAED,MAAA,UAA+B;AAAA,MAC7B,EAAA;AAAA,MACA,IAAA,EAAA,QAAA,CAAA,IAAA,CAAA;AAAA,MACA,eAAe,KAAI,EAAA,SAAA,CAAA;AAAA,MACnB,MAAA,EAAA,CAAS,KAAM,KAAA;AAAgB,QAC/B,MAAQ,CAAC,KAAkB,CAAA,CAAA;AACzB,OAAA;AAAY,MACd,OAAA,EAAA,CAAA,KAAA,KAAA;AAAA,QACA,OAAS,CAAC,KAAkB,CAAA,CAAA;AAC1B,OAAA;AAAa,MACf,QAAA,EAAA,CAAA,KAAA,KAAA;AAAA,QACA,IAAA,KAAW,CAAkB,IAAA,CAAA,EAAA;AAC3B,UAAI,OAAA,CAAM,KAAO,CAAA,CAAA;AACf,SAAA,MAAA;AAAa,UACR,MAAA,CAAA,KAAA,CAAA,CAAA;AACL,SAAA;AAAY,OACd;AAAA,MACF,MAAA,EAAA,MAAA;AAAA,mBACc,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACZ,OAAK;AAA0B,MACjC,MAAA,EAAA,MAAA;AAAA,mBACc,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACZ,OAAK;AAA0B,MACjC,YAAA,EAAA,MAAA;AAAA,0BACoB,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAClB,OAAK;AAAiC,MACxC,YAAA,EAAA,MAAA;AAAA,0BACoB,EAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAClB,OAAK;AAAiC,MACxC,YAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,KAAC,CAAA,MAAA,KAAA,CAAA,QAAA,EAAA,CAAA,QAAA,KAAA;AAED,MAAA,IAAA,QAAA,IAAA,IAAA,CAAA,KAAA,EAAA;AAAA,YACQ,CAAM,KAAA,GAAA,KAAA,CAAA;AAAA,OACX;AACC,KAAI,CAAA,CAAA;AACF,IAAA,MAAA,oBAAa,GAAA,CAAA,KAAA,KAAA;AAAA,MACf,IAAA,EAAA,CAAA;AAAA,MACF,OAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,aAAA,CAAA,MAAA,IAAA,CAAA,KAAwB,IAAuB,IAAA,EAAA,CAAA,CAAA;AACnD,IAAO,MAAA,CAAA;AAA4C,MACrD,SAAA;AAEA,MAAA,UAAA;AAEA,MAAa,oBAAA;AAAA,MAAA,YAAA;AAAA,MAAA,MAAA;AAAA,MAAA,OAAA;AAAA,MAIX,IAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,QAAA,CAAA,EAAA;AAAA,QAIA,OAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA,SAAA;AAAA,QAAA,IAAA,EAAA,IAAA,CAAA,IAAA;AAAA,OAAA,EAAA;AAAA,QAIA,OAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,UAAAC,WAAA,CAAA,gBAAA,EAAA;AAAA,YAAA,QAAA,EAAA,IAAA,CAAA,QAAA;AAAA,YAAA,OAAA,EAAA,IAAA,CAAA,OAAA;AAAA,YAIA,cAAA,EAAA,IAAA,CAAA,WAAA;AAAA,YAAA,aAAA,EAAA,IAAA,CAAA,UAAA;AAAA,YAAA,oBAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,WAAA,EAAA;AAAA,YAIA,OAAA,EAAAD,OAAA,CAAA,MAAA;AAAA,cAAA,IAAA,CAAA,MAAA,CAAA,OAAA,GAAAE,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,CAAA,GAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,CAAA;AAAA,YAAA,CAAA,EAAA,CAAA;AAAA,WAIA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,SAAA,EAAA,cAAA,EAAA,aAAA,EAAA,oBAAA,CAAA,CAAA;AAAA,UAAAF,WAAA,CAAA,gBAAA,EAAA;AAAA,YAAA,OAAA,EAAA,YAAA;AAAA,YAAA,GAAA,EAAA,UAAA;AAAA,YAIA,YAAA,EAAA,IAAA,CAAA,SAAA;AAAA,YACD,oBAAA,EAAA,IAAA,CAAA,iBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}