{"version": 3, "file": "tree-node-content.mjs", "sources": ["../../../../../../packages/components/tree/src/tree-node-content.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, h, inject, renderSlot } from 'vue'\nimport ElText from '@element-plus/components/text'\n\nimport { useNamespace } from '@element-plus/hooks'\nimport { NODE_INSTANCE_INJECTION_KEY, ROOT_TREE_INJECTION_KEY } from './tokens'\nimport type { ComponentInternalInstance } from 'vue'\nimport type { RootTreeType } from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: {\n    node: {\n      type: Object,\n      required: true,\n    },\n    renderContent: Function,\n  },\n  setup(props) {\n    const ns = useNamespace('tree')\n    const nodeInstance = inject<ComponentInternalInstance>(\n      NODE_INSTANCE_INJECTION_KEY\n    )\n    const tree = inject<RootTreeType>(ROOT_TREE_INJECTION_KEY)!\n    return () => {\n      const node = props.node\n      const { data, store } = node\n      return props.renderContent\n        ? props.renderContent(h, { _self: nodeInstance, node, data, store })\n        : renderSlot(tree.ctx.slots, 'default', { node, data }, () => [\n            h(\n              ElText,\n              { tag: 'span', truncated: true, class: ns.be('node', 'label') },\n              () => [node.label]\n            ),\n          ])\n    }\n  },\n})\n</script>\n"], "names": [], "mappings": ";;;;;;AASA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,mBAAA;AAAA,EACN,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,aAAe,EAAA,QAAA;AAAA,GACjB;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,YAAe,GAAA,MAAA,CAAA,2BAAA,CAAA,CAAA;AAAA,IACnB,MAAA,IAAA,GAAA,MAAA,CAAA,uBAAA,CAAA,CAAA;AAAA,IACF,OAAA,MAAA;AACA,MAAM,MAAA,IAAA,QAA4B,CAAuB,IAAA,CAAA;AACzD,MAAA,MAAa,EAAA,IAAA,EAAA,KAAA,EAAA,GAAA,IAAA,CAAA;AACX,MAAA,aAAa,aAAM,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA,EAAA,EAAA,KAAA,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,CAAA,GAAA,UAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,MAAA;AACnB,QAAM,CAAA,CAAA,MAAQ,EAAA,EAAA,GAAA,EAAA,MAAU,EAAA,SAAA,EAAA,IAAA,EAAA,KAAA,EAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACxB,OAAO,CAAA,CAAA;AAEyD,KAC1D,CAAA;AAAA,GACE;AAAA,CACA,CAAA,CAAA;AACiB,kBACnB,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,uBAAA,CAAA,CAAA,CAAA;;;;"}