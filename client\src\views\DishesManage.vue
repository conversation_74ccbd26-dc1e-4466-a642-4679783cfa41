<template>
  <div class="dishes-manage-container">
    <!-- 搜索和筛选工具栏 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索菜品名称"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="categoryFilter" placeholder="菜品分类" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="熟食" value="熟食" />
            <el-option label="主食" value="主食" />
            <el-option label="小吃" value="小吃" />
            <el-option label="礼盒" value="礼盒" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="上架" value="上架" />
            <el-option label="下架" value="下架" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch" style="margin-left: 10px;">重置</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="openAddDialog">添加菜品</el-button>
          <el-button type="warning" @click="batchOperation" style="margin-left: 10px;">批量操作</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 菜品统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ dishesStats.total }}</div>
            <div class="stat-label">总菜品数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ dishesStats.onSale }}</div>
            <div class="stat-label">已上架</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ dishesStats.offSale }}</div>
            <div class="stat-label">已下架</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ dishesStats.categories }}</div>
            <div class="stat-label">分类数量</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 菜品列表 -->
    <el-card class="dishes-list-card">
      <div class="view-toggle">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="card">卡片视图</el-radio-button>
          <el-radio-button label="table">表格视图</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="dishes-grid">
        <el-card 
          v-for="dish in filteredDishes" 
          :key="dish.id" 
          class="dish-card"
          :class="{ 'off-sale': dish.status === '下架' }"
        >
          <div class="dish-image">
            <img :src="dish.image || '/donkey-avatar.png'" :alt="dish.name" />
            <div class="dish-status" :class="dish.status === '上架' ? 'on-sale' : 'off-sale'">
              {{ dish.status }}
            </div>
          </div>
          <div class="dish-info">
            <h3 class="dish-name">{{ dish.name }}</h3>
            <p class="dish-category">{{ dish.category }}</p>
            <div class="dish-price">¥{{ dish.price }}</div>
            <div class="dish-stock">库存: {{ dish.stock }}</div>
            <div class="dish-description">{{ dish.description }}</div>
          </div>
          <div class="dish-actions">
            <el-button size="small" @click="openEditDialog(dish)">编辑</el-button>
            <el-button 
              size="small" 
              :type="dish.status === '上架' ? 'warning' : 'success'"
              @click="toggleStatus(dish)"
            >
              {{ dish.status === '上架' ? '下架' : '上架' }}
            </el-button>
            <el-button size="small" type="danger" @click="removeDish(dish.id)">删除</el-button>
          </div>
        </el-card>
      </div>

      <!-- 表格视图 -->
      <el-table 
        v-else 
        :data="filteredDishes" 
        style="width: 100%" 
        border 
        stripe
        class="dishes-table"
      >
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="name" label="菜品名称" />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="scope">
            <span class="price">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '上架' ? 'success' : 'info'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              :type="scope.row.status === '上架' ? 'warning' : 'success'"
              @click="toggleStatus(scope.row)"
            >
              {{ scope.row.status === '上架' ? '下架' : '上架' }}
            </el-button>
            <el-button size="small" type="danger" @click="removeDish(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="totalDishes"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑菜品弹窗 -->
    <el-dialog
      :title="editMode ? '编辑菜品' : '添加菜品'"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="菜品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入菜品名称" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
            <el-option label="熟食" value="熟食" />
            <el-option label="主食" value="主食" />
            <el-option label="小吃" value="小吃" />
            <el-option label="礼盒" value="礼盒" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number 
            v-model="form.price" 
            :precision="2" 
            :step="0.1" 
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number 
            v-model="form.stock" 
            :min="0" 
            :precision="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="上架">上架</el-radio>
            <el-radio label="下架">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="图片">
          <el-upload
            class="dish-upload"
            action="#"
            :auto-upload="false"
            :on-change="handleImageChange"
            :show-file-list="false"
          >
            <img v-if="form.image" :src="form.image" class="upload-image" />
            <el-icon v-else class="upload-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入菜品描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDish">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const categoryFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const dialogVisible = ref(false)
const editMode = ref(false)
const viewMode = ref('card')
const formRef = ref(null)

// 表单数据
const form = ref({
  id: null,
  name: '',
  category: '',
  price: 0,
  stock: 0,
  status: '上架',
  image: '',
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入菜品名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存', trigger: 'blur' }
  ]
}

// 模拟菜品数据
const dishes = ref([
  {
    id: 1,
    name: '五香驴肉',
    category: '熟食',
    price: 68.00,
    stock: 100,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '精选新鲜驴肉，采用传统五香工艺制作，口感鲜美，营养丰富',
    createTime: '2023-12-01 10:30:00'
  },
  {
    id: 2,
    name: '酱驴肉',
    category: '熟食',
    price: 72.00,
    stock: 80,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '秘制酱料腌制，肉质鲜嫩，香味浓郁',
    createTime: '2023-12-01 11:15:00'
  },
  {
    id: 3,
    name: '驴肉火烧',
    category: '主食',
    price: 15.00,
    stock: 200,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '传统驴肉火烧，外酥内软，肉香四溢',
    createTime: '2023-12-01 14:20:00'
  },
  {
    id: 4,
    name: '卤驴肉',
    category: '熟食',
    price: 75.00,
    stock: 60,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '老汤卤制，入味十足，回味无穷',
    createTime: '2023-12-01 16:45:00'
  },
  {
    id: 5,
    name: '驴肉香肠',
    category: '熟食',
    price: 44.00,
    stock: 150,
    status: '下架',
    image: '/donkey-avatar.png',
    description: '纯手工制作，无添加防腐剂，健康美味',
    createTime: '2023-12-01 18:30:00'
  },
  {
    id: 6,
    name: '驴肉礼盒',
    category: '礼盒',
    price: 288.00,
    stock: 30,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '精美礼盒包装，内含多种驴肉制品，送礼佳品',
    createTime: '2023-12-02 09:15:00'
  },
  {
    id: 7,
    name: '驴肉干',
    category: '小吃',
    price: 35.00,
    stock: 120,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '传统工艺制作，口感韧性十足，越嚼越香',
    createTime: '2023-12-02 10:45:00'
  },
  {
    id: 8,
    name: '卤驴杂',
    category: '熟食',
    price: 28.00,
    stock: 80,
    status: '上架',
    image: '/donkey-avatar.png',
    description: '精选驴杂，卤制入味，下酒佳品',
    createTime: '2023-12-02 11:30:00'
  }
])

// 计算属性
const filteredDishes = computed(() => {
  let result = dishes.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(dish => 
      dish.name.includes(searchQuery.value) ||
      dish.description.includes(searchQuery.value)
    )
  }

  // 分类过滤
  if (categoryFilter.value) {
    result = result.filter(dish => dish.category === categoryFilter.value)
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(dish => dish.status === statusFilter.value)
  }

  return result
})

const totalDishes = computed(() => filteredDishes.value.length)

const dishesStats = computed(() => {
  const total = dishes.value.length
  const onSale = dishes.value.filter(d => d.status === '上架').length
  const offSale = dishes.value.filter(d => d.status === '下架').length
  const categories = new Set(dishes.value.map(d => d.category)).size
  
  return {
    total,
    onSale,
    offSale,
    categories
  }
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const resetSearch = () => {
  searchQuery.value = ''
  categoryFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
}

const openAddDialog = () => {
  editMode.value = false
  form.value = {
    id: null,
    name: '',
    category: '',
    price: 0,
    stock: 0,
    status: '上架',
    image: '',
    description: ''
  }
  dialogVisible.value = true
}

const openEditDialog = (dish) => {
  editMode.value = true
  form.value = { ...dish }
  dialogVisible.value = true
}

const saveDish = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (editMode.value) {
      const idx = dishes.value.findIndex(d => d.id === form.value.id)
      if (idx !== -1) {
        dishes.value[idx] = { ...form.value }
        ElMessage.success('编辑成功')
      }
    } else {
      form.value.id = Date.now()
      form.value.createTime = new Date().toLocaleString()
      dishes.value.push({ ...form.value })
      ElMessage.success('添加成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    // 表单验证失败
  }
}

const toggleStatus = async (dish) => {
  const newStatus = dish.status === '上架' ? '下架' : '上架'
  const action = dish.status === '上架' ? '下架' : '上架'
  
  try {
    await ElMessageBox.confirm(
      `确认要${action}该菜品吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    dish.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

const removeDish = async (id) => {
  try {
    await ElMessageBox.confirm(
      '确认要删除该菜品吗？删除后无法恢复！',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const idx = dishes.value.findIndex(d => d.id === id)
    if (idx !== -1) {
      dishes.value.splice(idx, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const batchOperation = () => {
  ElMessage.info('批量操作功能开发中...')
}

const handleImageChange = (file) => {
  // 这里可以处理图片上传逻辑
  const reader = new FileReader()
  reader.onload = (e) => {
    form.value.image = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

onMounted(() => {
  // 组件挂载后的初始化操作
})
</script>

<style scoped>
.dishes-manage-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.dishes-list-card {
  margin-bottom: 20px;
}

.view-toggle {
  margin-bottom: 20px;
  text-align: right;
}

.dishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.dish-card {
  transition: all 0.3s;
  cursor: pointer;
}

.dish-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dish-card.off-sale {
  opacity: 0.6;
}

.dish-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
}

.dish-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dish-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.dish-status.on-sale {
  background-color: #67c23a;
}

.dish-status.off-sale {
  background-color: #909399;
}

.dish-info {
  padding: 15px 0;
}

.dish-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.dish-category {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.dish-price {
  font-size: 20px;
  font-weight: bold;
  color: #e6a23c;
  margin-bottom: 8px;
}

.dish-stock {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.dish-description {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dish-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.dishes-table {
  margin-bottom: 20px;
}

.price {
  font-weight: bold;
  color: #e6a23c;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dish-upload {
  text-align: center;
}

.upload-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
}

.upload-icon:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.dialog-footer {
  text-align: right;
}
</style> 