{"name": "@vitejs/plugin-vue", "version": "4.6.2", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-vue.git", "directory": "packages/plugin-vue"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-vue/issues"}, "homepage": "https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#readme", "peerDependencies": {"vite": "^4.0.0 || ^5.0.0", "vue": "^3.2.25"}, "devDependencies": {"@jridgewell/gen-mapping": "^0.3.3", "@jridgewell/trace-mapping": "^0.3.20", "debug": "^4.3.4", "rollup": "^3.29.4", "slash": "^5.1.0", "source-map-js": "^1.0.2", "vite": "^4.5.0", "vue": "^3.3.8"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts"}}