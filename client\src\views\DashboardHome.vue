<template>
  <div class="dashboard-home-root">
    <div class="dashboard-banner-img">
      <img src="/banner-team.jpg" alt="banner" style="width:100%;height:100%;object-fit:cover;border-radius:12px;" />
    </div>
    <el-row :gutter="20" class="top-row">
      <!-- 左侧：个人信息卡片 -->
      <el-col :span="6" class="left-col">
        <el-card class="profile-card">
          <div class="profile-header">
            <img class="profile-avatar" src="/donkey-avatar.png" alt="avatar" />
            <div class="profile-info">
              <div class="profile-name">驴驴的缅北</div>
              <div class="profile-title">VP：驴肉事业部</div>
            </div>
          </div>
          <div class="profile-detail">
            <div>本月还剩(天)：<b>15</b></div>
            <div>Office BASE：保定</div>
          </div>
        </el-card>
        <el-card class="table-card" style="margin-top: 18px;">
          <el-table :data="brandTableData" border size="small" height="320">
            <el-table-column prop="brand" label="品牌" width="110" />
            <el-table-column prop="today" label="今日购买" width="90" />
            <el-table-column prop="month" label="本月购买" width="100" />
            <el-table-column prop="total" label="总计" width="100" />
          </el-table>
        </el-card>
      </el-col>
      <!-- 右侧：统计卡片和图表 -->
      <el-col :span="18" class="right-col">
        <el-row :gutter="16" class="stat-row">
          <el-col :span="8" v-for="(item, idx) in statCards" :key="idx">
            <el-card class="stat-card2">
              <div class="stat-num">{{ item.value }}</div>
              <div class="stat-label">{{ item.label }}</div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="chart-row">
          <el-col :span="24">
            <el-card class="chart-card">
              <div ref="lineChartRef" class="chart-box"></div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="chart-row">
          <el-col :span="12">
            <el-card class="chart-card">
              <div ref="barChartRef" class="chart-box"></div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <div ref="pieChartRef" class="chart-box"></div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'

const statCards = [
  { value: '￥546,000', label: '今日支付订单' },
  { value: '￥156,092', label: '今日未支付订单' },
  { value: '￥702,092', label: '今日订单' },
  { value: '￥6,123,098', label: '本月已收账款' },
  { value: '￥1,345,602', label: '本月未收账款' },
  { value: '￥7,468,700', label: '本月应收账款' }
]
const brandTableData = [
  { brand: '五香驴肉', today: 120, month: 3600, total: 43200 },
  { brand: '酱驴肉', today: 90, month: 2700, total: 32400 },
  { brand: '卤驴肉', today: 150, month: 4500, total: 54000 },
  { brand: '驴肉火烧', today: 200, month: 6000, total: 72000 },
  { brand: '驴肉香肠', today: 80, month: 2400, total: 28800 },
  { brand: '驴肉干', today: 350, month: 3500, total: 22000 },
  { brand: '卤驴杂', today: 3050, month: 3600, total: 6650 },
  { brand: '驴腱筋', today: 1350, month: 3060, total: 4410 },
  { brand: '驴板肠', today: 3050, month: 3600, total: 6650 },
  { brand: '驴下水', today: 1350, month: 3060, total: 4410 },
  { brand: '酱驴舌', today: 1350, month: 3060, total: 4410 },
  { brand: '驴肉礼盒', today: 1350, month: 3060, total: 4410 }
]
const lineChartRef = ref(null)
const barChartRef = ref(null)
const pieChartRef = ref(null)

function initCharts() {
  // 折线图
  const lineChart = echarts.init(lineChartRef.value)
  lineChart.setOption({
    tooltip: { trigger: 'axis' },
    legend: { data: ['五香驴肉', '酱驴肉', '卤驴肉', '驴肉火烧', '驴肉香肠'] },
    xAxis: { type: 'category', data: ['五香驴肉', '酱驴肉', '卤驴肉', '驴肉火烧', '驴肉香肠'] },
    yAxis: { type: 'value' },
    series: [
      { name: '五香驴肉', type: 'line', data: [88, 88, 88, 88, 88] },
      { name: '酱驴肉', type: 'line', data: [98, 98, 98, 98, 98] },
      { name: '卤驴肉', type: 'line', data: [78, 78, 78, 78, 78] },
      { name: '驴肉火烧', type: 'line', data: [68, 68, 68, 68, 68] },
      { name: '驴肉香肠', type: 'line', data: [58, 58, 58, 58, 58] }
    ]
  })
  // 柱状图
  const barChart = echarts.init(barChartRef.value)
  barChart.setOption({
    tooltip: {},
    legend: { data: ['新增用户', '活跃用户'] },
    xAxis: { data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
    yAxis: {},
    series: [
      { name: '新增用户', type: 'bar', data: [50, 200, 360, 800, 650, 400, 100] },
      { name: '活跃用户', type: 'bar', data: [300, 400, 500, 800, 900, 700, 200] }
    ]
  })
  // 饼图
  const pieChart = echarts.init(pieChartRef.value)
  pieChart.setOption({
    tooltip: { trigger: 'item' },
    legend: { orient: 'vertical', left: 'left' },
    series: [
      {
        name: '品牌占比',
        type: 'pie',
        radius: '60%',
        center: ['60%', '50%'],
        data: [
          { value: 43200, name: '五香驴肉' },
          { value: 32400, name: '酱驴肉' },
          { value: 54000, name: '卤驴肉' },
          { value: 72000, name: '驴肉火烧' },
          { value: 28800, name: '驴肉香肠' },
          { value: 22000, name: '驴肉干' }
        ]
      }
    ]
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})

// 响应式resize
watch(() => [lineChartRef.value, barChartRef.value, pieChartRef.value], () => {
  nextTick(() => {
    if (lineChartRef.value) echarts.getInstanceByDom(lineChartRef.value)?.resize()
    if (barChartRef.value) echarts.getInstanceByDom(barChartRef.value)?.resize()
    if (pieChartRef.value) echarts.getInstanceByDom(pieChartRef.value)?.resize()
  })
})
</script>

<style scoped>
.dashboard-home-root {
  width: 100%;
  min-height: 100%;
}
.dashboard-banner-img {
  width: 100%;
  height: 300px;
  background: linear-gradient(90deg, #f3e3c3 0%, #ede2d1 100%);
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.left-col {
  display: flex;
  flex-direction: column;
}
.right-col {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.top-row {
  margin-bottom: 0;
}
.profile-card {
  height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
}
.profile-header {
  display: flex;
  align-items: center;
}
.profile-avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  margin-right: 16px;
  object-fit: cover;
  border: 2px solid #e6d3b3;
}
.profile-info {
  display: flex;
  flex-direction: column;
}
.profile-name {
  font-size: 22px;
  font-weight: bold;
}
.profile-title {
  color: #a88b6a;
  font-size: 14px;
}
.profile-detail {
  margin-top: 10px;
  color: #7c5c3b;
  font-size: 15px;
}
.table-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
  height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.stat-row {
  margin-bottom: 18px;
}
.stat-card2 {
  height: 54px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  background: #f3e3c3;
  border: 1px solid #e6d3b3;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px #cbb292;
}
.stat-num {
  font-size: 20px;
  font-weight: bold;
  color: #b97a3c;
}
.stat-label {
  color: #a88b6a;
  font-size: 13px;
}
.chart-row {
  margin-bottom: 18px;
}
.chart-card {
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
  height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.chart-box {
  width: 100%;
  height: 300px;
}
.empty-card {
  background: #fff7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px #cbb292;
  border: none;
  height: 340px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 