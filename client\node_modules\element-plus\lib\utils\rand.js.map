{"version": 3, "file": "rand.js", "sources": ["../../../../packages/utils/rand.ts"], "sourcesContent": ["/**\n * @deprecated Use `useId` `useIdInjection` instead\n * Generate random number in range [0, 1000]\n * Maybe replace with [uuid](https://www.npmjs.com/package/uuid)\n */\nexport const generateId = (): number => Math.floor(Math.random() * 10000)\n\n/**\n * @deprecated\n * Generating a random int in range (0, max - 1)\n * @param max {number}\n */\nexport const getRandomInt = (max: number) =>\n  Math.floor(Math.random() * Math.floor(max))\n"], "names": [], "mappings": ";;;;AAAY,MAAC,UAAU,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE;AACpD,MAAC,YAAY,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;;;;;"}