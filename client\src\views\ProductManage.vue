<template>
  <div class="page-container">
    <el-card>
      <div class="toolbar">
        <el-input v-model="search" placeholder="搜索产品名称" style="width: 200px; margin-right: 10px;" clearable />
        <el-button type="primary" @click="openAddDialog">添加产品</el-button>
      </div>
      <el-table :data="filteredProducts" style="width: 100%; margin-top: 16px;" border>
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="name" label="产品名称" />
        <el-table-column prop="category" label="分类" />
        <el-table-column prop="price" label="价格" />
        <el-table-column prop="stock" label="库存" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="removeProduct(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="editMode ? '编辑产品' : '添加产品'" v-model="dialogVisible">
      <el-form :model="form" label-width="80px">
        <el-form-item label="产品名称">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="分类">
          <el-input v-model="form.category" />
        </el-form-item>
        <el-form-item label="价格">
          <el-input v-model="form.price" type="number" />
        </el-form-item>
        <el-form-item label="库存">
          <el-input v-model="form.stock" type="number" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProduct">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const search = ref('')
const dialogVisible = ref(false)
const editMode = ref(false)
const form = ref({ id: null, name: '', category: '', price: '', stock: '' })
const products = ref([
  { id: 1, name: '五香驴肉', category: '熟食', price: 68, stock: 100 },
  { id: 2, name: '酱驴肉', category: '熟食', price: 72, stock: 80 },
  { id: 3, name: '驴肉火烧', category: '主食', price: 15, stock: 200 }
])
const filteredProducts = computed(() => {
  if (!search.value) return products.value
  return products.value.filter(p => p.name.includes(search.value))
})
function openAddDialog() {
  editMode.value = false
  form.value = { id: null, name: '', category: '', price: '', stock: '' }
  dialogVisible.value = true
}
function openEditDialog(row) {
  editMode.value = true
  form.value = { ...row }
  dialogVisible.value = true
}
function saveProduct() {
  if (!form.value.name) return ElMessage.error('请输入产品名称')
  if (editMode.value) {
    const idx = products.value.findIndex(p => p.id === form.value.id)
    if (idx !== -1) products.value[idx] = { ...form.value }
    ElMessage.success('编辑成功')
  } else {
    form.value.id = Date.now()
    products.value.push({ ...form.value })
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
}
function removeProduct(id) {
  products.value = products.value.filter(p => p.id !== id)
  ElMessage.success('删除成功')
}
</script>
<style scoped>
.page-container { padding: 18px; }
.toolbar { margin-bottom: 10px; display: flex; align-items: center; }
</style> 