{"version": 3, "file": "lt.js", "sources": ["../../../../../packages/locale/lang/lt.ts"], "sourcesContent": ["export default {\n  name: 'lt',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: 'Šiandien',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Pasirink datą',\n      selectTime: 'Pasirink laiką',\n      startDate: 'Data nuo',\n      startTime: 'Laikas nuo',\n      endDate: 'Data iki',\n      endTime: 'Laikas iki',\n      prevYear: 'Metai atgal',\n      nextYear: 'Metai į priekį',\n      prevMonth: 'Mėn. atgal',\n      nextMonth: 'Mėn. į priekį',\n      year: '',\n      month1: 'Sausis',\n      month2: 'Vasaris',\n      month3: 'Kovas',\n      month4: 'Balandis',\n      month5: 'Gegu<PERSON><PERSON>',\n      month6: 'Bir<PERSON><PERSON><PERSON>',\n      month7: 'Liepa',\n      month8: 'Rugp<PERSON><PERSON><PERSON>',\n      month9: 'Rugsėjis',\n      month10: '<PERSON><PERSON>',\n      month11: 'Lapkrit<PERSON>',\n      month12: '<PERSON><PERSON>od<PERSON>',\n      // week: 'savait<PERSON>',\n      weeks: {\n        sun: 'S.',\n        mon: 'Pr.',\n        tue: 'A.',\n        wed: 'T.',\n        thu: 'K.',\n        fri: 'Pn.',\n        sat: 'Š.',\n      },\n      months: {\n        jan: 'Sau',\n        feb: 'Vas',\n        mar: 'Kov',\n        apr: 'Bal',\n        may: 'Geg',\n        jun: 'Bir',\n        jul: 'Lie',\n        aug: 'Rugp',\n        sep: 'Rugs',\n        oct: 'Spa',\n        nov: 'Lap',\n        dec: 'Gruo',\n      },\n    },\n    select: {\n      loading: 'Kraunasi',\n      noMatch: 'Duomenų nerasta',\n      noData: 'Nėra duomenų',\n      placeholder: 'Pasirink',\n    },\n    mention: {\n      loading: 'Kraunasi',\n    },\n    cascader: {\n      noMatch: 'Duomenų nerasta',\n      loading: 'Kraunasi',\n      placeholder: 'Pasirink',\n      noData: 'Nėra duomenų',\n    },\n    pagination: {\n      goto: 'Eiti į',\n      pagesize: '/p',\n      total: 'Viso {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Žinutė',\n      confirm: 'OK',\n      cancel: 'Atšaukti',\n      error: 'Klaida įvestuose duomenyse',\n    },\n    upload: {\n      deleteTip: 'spauskite \"Trinti\" norėdami pašalinti',\n      delete: 'Trinti',\n      preview: 'Peržiūrėti',\n      continue: 'Toliau',\n    },\n    table: {\n      emptyText: 'Duomenų nerasta',\n      confirmFilter: 'Patvirtinti',\n      resetFilter: 'Atstatyti',\n      clearFilter: 'Išvalyti',\n      sumText: 'Suma',\n    },\n    tour: {\n      next: 'Kitas',\n      previous: 'Ankstesnis',\n      finish: 'Baigti',\n    },\n    tree: {\n      emptyText: 'Nėra duomenų',\n    },\n    transfer: {\n      noMatch: 'Duomenų nerasta',\n      noData: 'Nėra duomenų',\n      titles: ['Sąrašas 1', 'Sąrašas 2'],\n      filterPlaceholder: 'Įvesk raktažodį',\n      noCheckedFormat: 'Viso: {total}',\n      hasCheckedFormat: 'Pažymėta {checked} iš {total}',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,oBAAoB;AACtC,MAAM,UAAU,EAAE,qBAAqB;AACvC,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,SAAS;AACtB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,wBAAwB;AACtC,MAAM,WAAW,EAAE,UAAU;AAC7B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,UAAU;AACzB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,MAAM,EAAE,wBAAwB;AACtC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,iCAAiC;AAC9C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iDAAiD;AAClE,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,2BAA2B;AAC1C,MAAM,QAAQ,EAAE,QAAQ;AACxB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,aAAa,EAAE,aAAa;AAClC,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,MAAM,EAAE,QAAQ;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,wBAAwB;AACzC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,wBAAwB;AACtC,MAAM,MAAM,EAAE,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;AAC5D,MAAM,iBAAiB,EAAE,gCAAgC;AACzD,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,8CAA8C;AACtE,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}