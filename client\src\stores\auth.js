import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || null)

  const isAuthenticated = computed(() => !!token.value)

  // 设置axios默认headers
  if (token.value) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
  }

  const login = async (username, password) => {
    try {
      const response = await axios.post('/api/login', {
        username,
        password
      })

      const { token: newToken, user: userData } = response.data
      
      token.value = newToken
      user.value = userData
      
      localStorage.setItem('token', newToken)
      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败，请重试' 
      }
    }
  }

  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    delete axios.defaults.headers.common['Authorization']
  }

  const verifyToken = async () => {
    if (!token.value) return false
    
    try {
      const response = await axios.get('/api/verify')
      user.value = response.data.user
      return true
    } catch (error) {
      console.error('Token验证失败:', error)
      logout()
      return false
    }
  }

  const getUserInfo = async () => {
    if (!token.value) return null
    
    try {
      const response = await axios.get('/api/user')
      user.value = response.data
      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
      return null
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    verifyToken,
    getUserInfo
  }
}) 