{"name": "dayjs", "version": "1.11.13", "description": "2KB immutable date time library alternative to Moment.js with the same modern API ", "main": "dayjs.min.js", "types": "index.d.ts", "scripts": {"test": "TZ=Pacific/Auckland npm run test-tz && TZ=Europe/London npm run test-tz && TZ=America/Whitehorse npm run test-tz && npm run test-tz && jest", "test-tz": "date && jest test/timezone.test --coverage=false", "lint": "./node_modules/.bin/eslint src/* test/* build/*", "prettier": "prettier --write \"docs/**/*.md\"", "babel": "cross-env BABEL_ENV=build babel src --out-dir esm --copy-files && node build/esm", "build": "cross-env BABEL_ENV=build node build && npm run size", "sauce": "npx karma start karma.sauce.conf.js", "test:sauce": "npm run sauce -- 0 && npm run sauce -- 1 && npm run sauce -- 2  && npm run sauce -- 3", "size": "size-limit && gzip-size dayjs.min.js"}, "pre-commit": ["lint"], "size-limit": [{"limit": "2.99 KB", "path": "dayjs.min.js"}], "jest": {"roots": ["test"], "testRegex": "test/(.*?/)?.*test.js$", "testURL": "http://localhost", "coverageDirectory": "./coverage/", "collectCoverage": true, "collectCoverageFrom": ["src/**/*"]}, "keywords": ["dayjs", "date", "time", "immutable", "moment"], "author": "i<PERSON><PERSON>n", "license": "MIT", "homepage": "https://day.js.org", "repository": {"type": "git", "url": "https://github.com/iamkun/dayjs.git"}, "devDependencies": {"@babel/cli": "^7.0.0-beta.44", "@babel/core": "^7.0.0-beta.44", "@babel/node": "^7.0.0-beta.44", "@babel/preset-env": "^7.0.0-beta.44", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "babel-plugin-external-helpers": "^6.22.0", "cross-env": "^5.1.6", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-jest": "^21.15.0", "gzip-size-cli": "^2.1.0", "jasmine-core": "^2.99.1", "jest": "^22.4.3", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.1.0", "mockdate": "^2.0.2", "moment": "2.29.2", "moment-timezone": "0.5.31", "ncp": "^2.0.0", "pre-commit": "^1.2.2", "prettier": "^1.16.1", "rollup": "^2.45.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^0.18.0", "typescript": "^2.8.3"}}