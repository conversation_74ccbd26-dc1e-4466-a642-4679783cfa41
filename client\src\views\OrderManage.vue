<template>
  <div class="order-manage-container">
    <!-- 搜索和筛选工具栏 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索订单号或客户名称"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="订单状态" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="待付款" value="pending" />
            <el-option label="已付款" value="paid" />
            <el-option label="已发货" value="shipped" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch" style="margin-left: 20px;">搜索</el-button>
          <el-button @click="resetSearch" style="margin-left: 10px;">重置</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="success" @click="exportOrders">导出订单</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 订单统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ orderStats.total }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ orderStats.pending }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">¥{{ orderStats.totalAmount }}</div>
            <div class="stat-label">总金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ orderStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 订单列表 -->
    <el-card class="order-list-card">
      <el-table
        :data="filteredOrders"
        style="width: 100%"
        border
        stripe
        @row-click="showOrderDetail"
        class="order-table"
      >
        <el-table-column prop="orderNo" label="订单号" />
        <el-table-column prop="customerName" label="客户名称" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="totalAmount" label="订单金额">
          <template #default="scope">
            <span class="amount">¥{{ scope.row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" label="支付方式" />
        <el-table-column prop="createTime" label="下单时间" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <div class="action-buttons" style="flex-direction: column; align-items: flex-start; gap: 0;">
              <el-button size="small" @click.stop="showOrderDetail(scope.row)">查看详情</el-button>
              <div class="status-buttons" style="margin-bottom: 0; display: flex; flex-direction: row; gap: 5px;">
                <el-button 
                  size="small" 
                  type="primary" 
                  :disabled="scope.row.status !== 'pending'"
                  @click.stop="updateOrderStatus(scope.row.id, 'paid')"
                >
                  确认付款
                </el-button>
                <el-button 
                  size="small" 
                  type="warning" 
                  :disabled="scope.row.status !== 'paid'"
                  @click.stop="updateOrderStatus(scope.row.id, 'shipped')"
                >
                  发货
                </el-button>
              </div>
              <el-button 
                size="small" 
                type="danger" 
                @click.stop="removeOrder(scope.row.id)"
                style="margin-top: 4px;"
              >
                删除订单
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalOrders"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedOrder" class="order-detail">
        <!-- 订单基本信息 -->
        <el-descriptions title="订单信息" :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ selectedOrder.createTime }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ selectedOrder.paymentMethod }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ selectedOrder.totalAmount }}</el-descriptions-item>
          <el-descriptions-item label="配送地址">{{ selectedOrder.address }}</el-descriptions-item>
        </el-descriptions>

        <!-- 客户信息 -->
        <el-descriptions title="客户信息" :column="2" border style="margin-top: 20px;">
          <el-descriptions-item label="客户姓名">{{ selectedOrder.customerName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ selectedOrder.phone }}</el-descriptions-item>
          <el-descriptions-item label="收货地址" :span="2">{{ selectedOrder.address }}</el-descriptions-item>
        </el-descriptions>

        <!-- 订单商品 -->
        <div style="margin-top: 20px;">
          <h4>订单商品</h4>
          <el-table :data="selectedOrder.items" border>
            <el-table-column prop="productName" label="商品名称" />
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="unitPrice" label="单价" width="100">
              <template #default="scope">
                ¥{{ scope.row.unitPrice }}
              </template>
            </el-table-column>
            <el-table-column prop="subtotal" label="小计" width="100">
              <template #default="scope">
                ¥{{ scope.row.subtotal }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 订单备注 -->
        <div style="margin-top: 20px;">
          <h4>订单备注</h4>
          <p>{{ selectedOrder.remark || '无' }}</p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="printOrder">打印订单</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const dateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const detailDialogVisible = ref(false)
const selectedOrder = ref(null)

// 模拟订单数据
const orders = ref([
  {
    id: 1,
    orderNo: 'DD20231201001',
    customerName: '张三',
    phone: '13800000001',
    address: '北京市朝阳区建国路88号',
    totalAmount: 156.00,
    status: 'pending',
    paymentMethod: '微信支付',
    createTime: '2023-12-01 10:30:00',
    remark: '请尽快发货',
    items: [
      { productName: '五香驴肉', quantity: 2, unitPrice: 68.00, subtotal: 136.00 },
      { productName: '驴肉火烧', quantity: 4, unitPrice: 5.00, subtotal: 20.00 }
    ]
  },
  {
    id: 2,
    orderNo: 'DD20231201002',
    customerName: '李四',
    phone: '13800000002',
    address: '上海市浦东新区陆家嘴金融中心',
    totalAmount: 288.00,
    status: 'paid',
    paymentMethod: '支付宝',
    createTime: '2023-12-01 11:15:00',
    remark: '',
    items: [
      { productName: '酱驴肉', quantity: 2, unitPrice: 72.00, subtotal: 144.00 },
      { productName: '卤驴肉', quantity: 2, unitPrice: 72.00, subtotal: 144.00 }
    ]
  },
  {
    id: 3,
    orderNo: 'DD20231201003',
    customerName: '王五',
    phone: '13800000003',
    address: '广州市天河区珠江新城',
    totalAmount: 432.00,
    status: 'shipped',
    paymentMethod: '银行卡',
    createTime: '2023-12-01 14:20:00',
    remark: '包装要精美',
    items: [
      { productName: '驴肉礼盒', quantity: 1, unitPrice: 432.00, subtotal: 432.00 }
    ]
  },
  {
    id: 4,
    orderNo: 'DD20231201004',
    customerName: '赵六',
    phone: '13800000004',
    address: '深圳市南山区科技园',
    totalAmount: 156.00,
    status: 'completed',
    paymentMethod: '微信支付',
    createTime: '2023-12-01 16:45:00',
    remark: '',
    items: [
      { productName: '五香驴肉', quantity: 1, unitPrice: 68.00, subtotal: 68.00 },
      { productName: '驴肉香肠', quantity: 2, unitPrice: 44.00, subtotal: 88.00 }
    ]
  },
  {
    id: 5,
    orderNo: 'DD20231201005',
    customerName: '钱七',
    phone: '13800000005',
    address: '杭州市西湖区文三路',
    totalAmount: 200.00,
    status: 'cancelled',
    paymentMethod: '支付宝',
    createTime: '2023-12-01 18:30:00',
    remark: '客户取消',
    items: [
      { productName: '驴肉火烧', quantity: 20, unitPrice: 10.00, subtotal: 200.00 }
    ]
  }
])

// 计算属性
const filteredOrders = computed(() => {
  let result = orders.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(order => 
      order.orderNo.includes(searchQuery.value) ||
      order.customerName.includes(searchQuery.value)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(order => order.status === statusFilter.value)
  }

  // 日期过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const startDate = new Date(dateRange.value[0])
    const endDate = new Date(dateRange.value[1])
    result = result.filter(order => {
      const orderDate = new Date(order.createTime.split(' ')[0])
      return orderDate >= startDate && orderDate <= endDate
    })
  }

  return result
})

const totalOrders = computed(() => filteredOrders.value.length)

const orderStats = computed(() => {
  const total = orders.value.length
  const pending = orders.value.filter(o => o.status === 'pending').length
  const completed = orders.value.filter(o => o.status === 'completed').length
  const totalAmount = orders.value.reduce((sum, order) => sum + order.totalAmount, 0)
  
  return {
    total,
    pending,
    completed,
    totalAmount: totalAmount.toFixed(2)
  }
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const resetSearch = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  dateRange.value = []
  currentPage.value = 1
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    paid: 'primary',
    shipped: 'info',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待付款',
    paid: '已付款',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const showOrderDetail = (order) => {
  selectedOrder.value = order
  detailDialogVisible.value = true
}

const handleCloseDetail = () => {
  detailDialogVisible.value = false
  selectedOrder.value = null
}

const updateOrderStatus = async (orderId, newStatus) => {
  try {
    await ElMessageBox.confirm(
      `确认将订单状态更新为"${getStatusText(newStatus)}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const order = orders.value.find(o => o.id === orderId)
    if (order) {
      order.status = newStatus
      ElMessage.success('订单状态更新成功')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const removeOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm(
      '确认要删除此订单吗？删除后无法恢复！',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const idx = orders.value.findIndex(o => o.id === orderId)
    if (idx !== -1) {
      orders.value.splice(idx, 1)
      ElMessage.success('订单已删除')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const exportOrders = () => {
  ElMessage.success('订单导出功能开发中...')
}

const printOrder = () => {
  ElMessage.success('打印功能开发中...')
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

onMounted(() => {
  // 组件挂载后的初始化操作
})
</script>

<style scoped>
.order-manage-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.order-list-card {
  margin-bottom: 20px;
}

.order-table {
  cursor: pointer;
}

.order-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.amount {
  font-weight: bold;
  color: #e6a23c;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.order-detail h4 {
  margin-bottom: 10px;
  color: #333;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0;
}

.status-buttons {
  display: flex;
  gap: 5px;
  align-items: center;
}
</style> 