{"version": 3, "file": "item.mjs", "sources": ["../../../../../../packages/components/space/src/item.ts"], "sourcesContent": ["import { computed, defineComponent, h, renderSlot } from 'vue'\nimport { buildProps } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const spaceItemProps = buildProps({\n  prefixCls: {\n    type: String,\n  },\n} as const)\nexport type SpaceItemProps = ExtractPropTypes<typeof spaceItemProps>\n\nconst SpaceItem = defineComponent({\n  name: 'ElSpaceItem',\n\n  props: spaceItemProps,\n\n  setup(props, { slots }) {\n    const ns = useNamespace('space')\n\n    const classes = computed(() => `${props.prefixCls || ns.b()}__item`)\n\n    return () =>\n      h('div', { class: classes.value }, renderSlot(slots, 'default'))\n  },\n})\nexport type SpaceItemInstance = InstanceType<typeof SpaceItem> & unknown\n\nexport default SpaceItem\n"], "names": [], "mappings": ";;;;AAGY,MAAC,cAAc,GAAG,UAAU,CAAC;AACzC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,CAAC,EAAE;AACE,MAAC,SAAS,GAAG,eAAe,CAAC;AAClC,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,KAAK,EAAE,cAAc;AACvB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACrC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACzE,IAAI,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;AAClF,GAAG;AACH,CAAC;;;;"}