{"version": 3, "file": "bn.mjs", "sources": ["../../../../../packages/locale/lang/bn.ts"], "sourcesContent": ["export default {\n  name: 'bn',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'ঠিক আছে',\n      clear: 'ক্লিয়ার',\n    },\n    datepicker: {\n      now: 'এখন',\n      today: 'আজ',\n      cancel: 'বাতিল',\n      clear: 'ক্লিয়ার',\n      confirm: 'ঠিক আছে',\n      selectDate: 'তারিখ নির্বাচন করুন',\n      selectTime: 'সময় নির্বাচন করুন',\n      startDate: 'যে তারিখ থেকে',\n      startTime: 'যে সময় থেকে',\n      endDate: 'যে তারিখ পর্যন্ত',\n      endTime: 'যে সময় পর্যন্ত',\n      prevYear: 'পূর্ববর্তী বছর',\n      nextYear: 'পরবর্তী বছর',\n      prevMonth: 'পূর্ববর্তী মাস',\n      nextMonth: 'পরবর্তী মাস',\n      year: 'সাল',\n      month1: 'জানুয়ারি',\n      month2: 'ফেব্রুয়ারী',\n      month3: 'মার্চ',\n      month4: 'এপ্রিল',\n      month5: 'মে',\n      month6: 'জুন',\n      month7: 'জুলাই',\n      month8: 'আগষ্ট',\n      month9: 'সেপ্টেম্বর',\n      month10: 'অক্টোবর',\n      month11: 'নভেম্বর',\n      month12: 'ডিসেম্বর',\n      week: 'সাপ্তাহ',\n      weeks: {\n        sun: 'রবি',\n        mon: 'সোম',\n        tue: 'মঙ্গল',\n        wed: 'বুধ',\n        thu: 'বৃহঃ',\n        fri: 'শুক্র',\n        sat: 'শনি',\n      },\n      months: {\n        jan: 'জানু',\n        feb: 'ফেব্রু',\n        mar: 'মার্চ',\n        apr: 'এপ্রি',\n        may: 'মে',\n        jun: 'জুন',\n        jul: 'জুলা',\n        aug: 'আগ',\n        sep: 'সেপ্টে',\n        oct: 'আক্টো',\n        nov: 'নভে',\n        dec: 'ডিসে',\n      },\n    },\n    select: {\n      loading: 'লোড হচ্ছে',\n      noMatch: 'কোন মিল পওয়া যায়নি',\n      noData: 'কোন ডাটা নেই',\n      placeholder: 'নির্বাচন করুন',\n    },\n    mention: {\n      loading: 'লোড হচ্ছে',\n    },\n    cascader: {\n      noMatch: 'কোন মিল পওয়া যায়নি',\n      loading: 'লোড হচ্ছে',\n      placeholder: 'নির্বাচন করুন',\n      noData: 'কোন ডাটা নেই',\n    },\n    pagination: {\n      goto: 'যান',\n      pagesize: '/পেজ',\n      total: 'মোট {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'অপ্রচলিত (Deprecated) ব্যাবহার পওয়া গেছে, আরও জানতে চাইলে, দয়া করে el-pagination এর ডকুমেন্টেশন দেখুন',\n    },\n    messagebox: {\n      title: 'বার্তা',\n      confirm: 'ঠিক আছে',\n      cancel: 'বাতিল',\n      error: 'ইনপুট ডাটা গ্রহনযোগ্য নয়',\n    },\n    upload: {\n      deleteTip: 'অপসারণ করতে \"ডিলিট\" এ ক্লিক করুন',\n      delete: 'ডিলিট',\n      preview: 'প্রিভিউ',\n      continue: 'চালিয়ে যান',\n    },\n    table: {\n      emptyText: 'কোন ডাটা নেই',\n      confirmFilter: 'নিশ্চিত করুন',\n      resetFilter: 'রিসেট',\n      clearFilter: 'সব',\n      sumText: 'সারাংশ',\n    },\n    tree: {\n      emptyText: 'কোন ডাটা নেই',\n    },\n    transfer: {\n      noMatch: 'কোন মিল পওয়া যায়নি',\n      noData: 'কোন ডাটা নেই',\n      titles: ['লিস্ট ১', 'লিস্ট ২'],\n      filterPlaceholder: 'সার্চ করুন',\n      noCheckedFormat: '{total} আইটেম',\n      hasCheckedFormat: '{checked}/{total} টিক করা হয়েছে',\n    },\n    image: {\n      error: 'ব্যর্থ হয়েছে',\n    },\n    pageHeader: {\n      title: 'পিছনে',\n    },\n    popconfirm: {\n      confirmButtonText: 'হ্যা',\n      cancelButtonText: 'না',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,uCAAuC;AACtD,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,oBAAoB;AAC/B,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,OAAO,EAAE,uCAAuC;AACtD,MAAM,UAAU,EAAE,0GAA0G;AAC5H,MAAM,UAAU,EAAE,8FAA8F;AAChH,MAAM,SAAS,EAAE,sEAAsE;AACvF,MAAM,SAAS,EAAE,0DAA0D;AAC3E,MAAM,OAAO,EAAE,wFAAwF;AACvG,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,QAAQ,EAAE,iFAAiF;AACjG,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,mDAAmD;AAClE,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,MAAM,EAAE,gEAAgE;AAC9E,MAAM,WAAW,EAAE,2EAA2E;AAC9F,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,mDAAmD;AAClE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,OAAO,EAAE,mDAAmD;AAClE,MAAM,WAAW,EAAE,2EAA2E;AAC9F,MAAM,MAAM,EAAE,gEAAgE;AAC9E,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,KAAK,EAAE,4BAA4B;AACzC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,kBAAkB,EAAE,wZAAwZ;AAClb,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,uCAAuC;AACtD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,mIAAmI;AAChJ,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,+JAA+J;AAChL,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,QAAQ,EAAE,yDAAyD;AACzE,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gEAAgE;AACjF,MAAM,aAAa,EAAE,qEAAqE;AAC1F,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gEAAgE;AACjF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,MAAM,EAAE,gEAAgE;AAC9E,MAAM,MAAM,EAAE,CAAC,uCAAuC,EAAE,uCAAuC,CAAC;AAChG,MAAM,iBAAiB,EAAE,yDAAyD;AAClF,MAAM,eAAe,EAAE,wCAAwC;AAC/D,MAAM,gBAAgB,EAAE,wFAAwF;AAChH,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,qEAAqE;AAClF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,gBAAgB,EAAE,cAAc;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}