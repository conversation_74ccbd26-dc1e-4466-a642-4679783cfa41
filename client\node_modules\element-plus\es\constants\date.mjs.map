{"version": 3, "file": "date.mjs", "sources": ["../../../../packages/constants/date.ts"], "sourcesContent": ["export const datePickTypes = [\n  'year',\n  'years',\n  'month',\n  'months',\n  'date',\n  'dates',\n  'week',\n  'datetime',\n  'datetimerange',\n  'daterange',\n  'monthrange',\n  'yearrange',\n] as const\n\nexport const WEEK_DAYS = [\n  'sun',\n  'mon',\n  'tue',\n  'wed',\n  'thu',\n  'fri',\n  'sat',\n] as const\n\nexport type DatePickType = typeof datePickTypes[number]\n"], "names": [], "mappings": "AAAY,MAAC,aAAa,GAAG;AAC7B,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,UAAU;AACZ,EAAE,eAAe;AACjB,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,WAAW;AACb,EAAE;AACU,MAAC,SAAS,GAAG;AACzB,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP;;;;"}