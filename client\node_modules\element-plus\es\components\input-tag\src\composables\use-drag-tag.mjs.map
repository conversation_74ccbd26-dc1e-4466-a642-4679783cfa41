{"version": 3, "file": "use-drag-tag.mjs", "sources": ["../../../../../../../packages/components/input-tag/src/composables/use-drag-tag.ts"], "sourcesContent": ["import { type ShallowRef, ref, shallowRef } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { getStyle, isUndefined, setStyle } from '@element-plus/utils'\n\ntype DropType = 'before' | 'after'\n\ninterface UseDragTagOptions {\n  wrapperRef: ShallowRef<HTMLElement | undefined>\n  handleDragged: (\n    draggingIndex: number,\n    dropIndex: number,\n    type: DropType\n  ) => void\n  afterDragged?: () => void\n}\n\nexport function useDragTag({\n  wrapperRef,\n  handleDragged,\n  afterDragged,\n}: UseDragTagOptions) {\n  const ns = useNamespace('input-tag')\n  const dropIndicatorRef = shallowRef<HTMLElement>()\n  const showDropIndicator = ref(false)\n\n  let draggingIndex: number | undefined\n  let draggingTag: HTMLElement | null\n  let dropIndex: number | undefined\n  let dropType: DropType | undefined\n\n  function getTagClassName(index: number) {\n    return `.${ns.e('inner')} .${ns.namespace.value}-tag:nth-child(${\n      index + 1\n    })`\n  }\n\n  function handleDragStart(event: DragEvent, index: number) {\n    draggingIndex = index\n    draggingTag = wrapperRef.value!.querySelector<HTMLElement>(\n      getTagClassName(index)\n    )\n\n    if (draggingTag) {\n      draggingTag.style.opacity = '0.5'\n    }\n    event.dataTransfer!.effectAllowed = 'move'\n  }\n\n  function handleDragOver(event: DragEvent, index: number) {\n    dropIndex = index\n    event.preventDefault()\n    event.dataTransfer!.dropEffect = 'move'\n\n    if (isUndefined(draggingIndex) || draggingIndex === index) {\n      showDropIndicator.value = false\n      return\n    }\n\n    const dropPosition = wrapperRef\n      .value!.querySelector<HTMLElement>(getTagClassName(index))!\n      .getBoundingClientRect()\n    const dropPrev = !(draggingIndex + 1 === index)\n    const dropNext = !(draggingIndex - 1 === index)\n    const distance = event.clientX - dropPosition.left\n    const prevPercent = dropPrev ? (dropNext ? 0.5 : 1) : -1\n    const nextPercent = dropNext ? (dropPrev ? 0.5 : 0) : 1\n\n    if (distance <= dropPosition.width * prevPercent) {\n      dropType = 'before'\n    } else if (distance > dropPosition.width * nextPercent) {\n      dropType = 'after'\n    } else {\n      dropType = undefined\n    }\n\n    const innerEl = wrapperRef.value!.querySelector<HTMLElement>(\n      `.${ns.e('inner')}`\n    )!\n    const innerPosition = innerEl.getBoundingClientRect()\n    const gap = Number.parseFloat(getStyle(innerEl, 'gap')) / 2\n\n    const indicatorTop = dropPosition.top - innerPosition.top\n    let indicatorLeft = -9999\n\n    if (dropType === 'before') {\n      indicatorLeft = Math.max(\n        dropPosition.left - innerPosition.left - gap,\n        Math.floor(-gap / 2)\n      )\n    } else if (dropType === 'after') {\n      const left = dropPosition.right - innerPosition.left\n      indicatorLeft =\n        left + (innerPosition.width === left ? Math.floor(gap / 2) : gap)\n    }\n\n    setStyle(dropIndicatorRef.value!, {\n      top: `${indicatorTop}px`,\n      left: `${indicatorLeft}px`,\n    })\n    showDropIndicator.value = !!dropType\n  }\n\n  function handleDragEnd(event: DragEvent) {\n    event.preventDefault()\n\n    if (draggingTag) {\n      draggingTag.style.opacity = ''\n    }\n\n    if (\n      dropType &&\n      !isUndefined(draggingIndex) &&\n      !isUndefined(dropIndex) &&\n      draggingIndex !== dropIndex\n    ) {\n      handleDragged(draggingIndex, dropIndex, dropType)\n    }\n\n    showDropIndicator.value = false\n    draggingIndex = undefined\n    draggingTag = null\n    dropIndex = undefined\n    dropType = undefined\n    afterDragged?.()\n  }\n\n  return {\n    dropIndicatorRef,\n    showDropIndicator,\n    handleDragStart,\n    handleDragOver,\n    handleDragEnd,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGO,SAAS,UAAU,CAAC;AAC3B,EAAE,UAAU;AACZ,EAAE,aAAa;AACf,EAAE,YAAY;AACd,CAAC,EAAE;AACH,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;AACvC,EAAE,MAAM,gBAAgB,GAAG,UAAU,EAAE,CAAC;AACxC,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACvC,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE;AAClC,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClF,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE;AACzC,IAAI,aAAa,GAAG,KAAK,CAAC;AAC1B,IAAI,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;AACxC,KAAK;AACL,IAAI,KAAK,CAAC,YAAY,CAAC,aAAa,GAAG,MAAM,CAAC;AAC9C,GAAG;AACH,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE;AACxC,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,IAAI,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;AAC3C,IAAI,IAAI,WAAW,CAAC,aAAa,CAAC,IAAI,aAAa,KAAK,KAAK,EAAE;AAC/D,MAAM,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC;AACtC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;AACxG,IAAI,MAAM,QAAQ,GAAG,EAAE,aAAa,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;AACpD,IAAI,MAAM,QAAQ,GAAG,EAAE,aAAa,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;AACpD,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC;AACvD,IAAI,MAAM,WAAW,GAAG,QAAQ,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3D,IAAI,MAAM,WAAW,GAAG,QAAQ,GAAG,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAI,IAAI,QAAQ,IAAI,YAAY,CAAC,KAAK,GAAG,WAAW,EAAE;AACtD,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAC1B,KAAK,MAAM,IAAI,QAAQ,GAAG,YAAY,CAAC,KAAK,GAAG,WAAW,EAAE;AAC5D,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;AAC1D,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAChE,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;AAC9D,IAAI,IAAI,aAAa,GAAG,CAAC,IAAI,CAAC;AAC9B,IAAI,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnG,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACrC,MAAM,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC;AAC3D,MAAM,aAAa,GAAG,IAAI,IAAI,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACxF,KAAK;AACL,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE;AACrC,MAAM,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAC9B,MAAM,IAAI,EAAE,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC;AACzC,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,aAAa,KAAK,SAAS,EAAE;AAC3G,MAAM,aAAa,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC;AACpC,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC;AAC3B,IAAI,WAAW,GAAG,IAAI,CAAC;AACvB,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC;AACvB,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC;AACtB,IAAI,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,EAAE,CAAC;AACnD,GAAG;AACH,EAAE,OAAO;AACT,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ;;;;"}