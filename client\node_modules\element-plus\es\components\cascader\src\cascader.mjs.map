{"version": 3, "file": "cascader.mjs", "sources": ["../../../../../../packages/components/cascader/src/cascader.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport { CommonProps } from '@element-plus/components/cascader-panel'\nimport { buildProps, definePropType, isBoolean } from '@element-plus/utils'\nimport { useEmptyValuesProps, useSizeProp } from '@element-plus/hooks'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport { tagProps } from '@element-plus/components/tag'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport type {\n  CascaderNode,\n  CascaderValue,\n} from '@element-plus/components/cascader-panel'\nimport type { Placement } from '@element-plus/components/popper'\n\nexport const cascaderProps = buildProps({\n  ...CommonProps,\n  /**\n   * @description size of input\n   */\n  size: useSizeProp,\n  /**\n   * @description placeholder of input\n   */\n  placeholder: String,\n  /**\n   * @description whether Cascader is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether selected value can be cleared\n   */\n  clearable: Boolean,\n  /**\n   * @description whether the options can be searched\n   */\n  filterable: Boolean,\n  /**\n   * @description customize search logic, the first parameter is `node`, the second is `keyword`, and need return a boolean value indicating whether it hits.\n   */\n  filterMethod: {\n    type: definePropType<(node: CascaderNode, keyword: string) => boolean>(\n      Function\n    ),\n    default: (node: CascaderNode, keyword: string) =>\n      node.text.includes(keyword),\n  },\n  /**\n   * @description option label separator\n   */\n  separator: {\n    type: String,\n    default: ' / ',\n  },\n  /**\n   * @description whether to display all levels of the selected value in the input\n   */\n  showAllLevels: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to collapse tags in multiple selection mode\n   */\n  collapseTags: Boolean,\n  /**\n   * @description The max tags number to be shown. To use this, collapse-tags must be true\n   */\n  maxCollapseTags: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description native input id\n   */\n  collapseTagsTooltip: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description The max height of collapse tags tooltip, in pixels. To use this, collapse-tags-tooltip must be true\n   */\n  maxCollapseTagsTooltipHeight: {\n    type: [String, Number],\n  },\n  /**\n   * @description debounce delay when typing filter keyword, in milliseconds\n   */\n  debounce: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description hook function before filtering with the value to be filtered as its parameter. If `false` is returned or a `Promise` is returned and then is rejected, filtering will be aborted\n   */\n  beforeFilter: {\n    type: definePropType<(value: string) => boolean | Promise<any>>(Function),\n    default: () => true,\n  },\n  /**\n   * @description position of dropdown\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: placements,\n    default: 'bottom-start',\n  },\n  /**\n   * @description list of possible positions for dropdown\n   */\n  fallbackPlacements: {\n    type: definePropType<Placement[]>(Array),\n    default: ['bottom-start', 'bottom', 'top-start', 'top', 'right', 'left'],\n  },\n  /**\n   * @description custom class name for Cascader's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether cascader popup is teleported\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description tag type\n   */\n  // eslint-disable-next-line vue/require-prop-types\n  tagType: { ...tagProps.type, default: 'info' },\n  /**\n   * @description tag effect\n   */\n  tagEffect: { ...tagProps.effect, default: 'light' },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description when dropdown is inactive and `persistent` is `false`, dropdown will be destroyed\n   */\n  persistent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useEmptyValuesProps,\n})\n\nexport const cascaderEmits = {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  [UPDATE_MODEL_EVENT]: (_: CascaderValue) => true,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  [CHANGE_EVENT]: (_: CascaderValue) => true,\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n  visibleChange: (val: boolean) => isBoolean(val),\n  expandChange: (val: CascaderValue) => !!val,\n  removeTag: (val: CascaderNode['valueByOption']) => !!val,\n}\n\n// Type name is taken(cascader-panel/src/node), needs discussion\n// export type CascaderProps = ExtractPropTypes<typeof cascaderProps>\n\nexport type CascaderEmits = typeof cascaderEmits\n"], "names": [], "mappings": ";;;;;;;;;;AAOY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,GAAG,WAAW;AAChB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC3D,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,4BAA4B,EAAE;AAChC,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,MAAM,IAAI;AACvB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,OAAO,EAAE,cAAc;AAC3B,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAC5E,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,UAAU,EAAE,sBAAsB,CAAC,UAAU;AAC/C,EAAE,OAAO,EAAE,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE;AAChD,EAAE,SAAS,EAAE,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AACrD,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAG,mBAAmB;AACxB,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAAC,kBAAkB,GAAG,CAAC,CAAC,KAAK,IAAI;AACnC,EAAE,CAAC,YAAY,GAAG,CAAC,CAAC,KAAK,IAAI;AAC7B,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,aAAa,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC;AACxC,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;AAC9B,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;AAC3B;;;;"}