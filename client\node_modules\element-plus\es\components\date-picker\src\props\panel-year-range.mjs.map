{"version": 3, "file": "panel-year-range.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-year-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const panelYearRangeProps = buildProps({\n  ...panelRangeSharedProps,\n} as const)\n\nexport const panelYearRangeEmits = [\n  'pick',\n  'set-picker-option',\n  'calendar-change',\n]\n\nexport type PanelYearRangeProps = ExtractPropTypes<typeof panelYearRangeProps>\n"], "names": [], "mappings": ";;;AAEY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,GAAG,qBAAqB;AAC1B,CAAC,EAAE;AACS,MAAC,mBAAmB,GAAG;AACnC,EAAE,MAAM;AACR,EAAE,mBAAmB;AACrB,EAAE,iBAAiB;AACnB;;;;"}