'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var inputTag$1 = require('./src/input-tag2.js');
var inputTag = require('./src/input-tag.js');
var install = require('../../utils/vue/install.js');

const ElInputTag = install.withInstall(inputTag$1["default"]);

exports.inputTagEmits = inputTag.inputTagEmits;
exports.inputTagProps = inputTag.inputTagProps;
exports.ElInputTag = ElInputTag;
exports["default"] = ElInputTag;
//# sourceMappingURL=index.js.map
