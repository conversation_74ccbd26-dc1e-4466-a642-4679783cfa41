/*! Element Plus v2.10.2 */

var sr = {
  name: "sr",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "\u041F\u043E\u043D\u0438\u0448\u0442\u0438"
    },
    datepicker: {
      now: "\u0421\u0430\u0434\u0430",
      today: "\u0414\u0430\u043D\u0430\u0441",
      cancel: "\u041E\u0442\u043A\u0430\u0436\u0438",
      clear: "\u0411\u0440\u0438\u0448\u0438",
      confirm: "OK",
      selectDate: "\u0418\u0437\u0430\u0431\u0435\u0440\u0438 \u0434\u0430\u0442\u0443\u043C",
      selectTime: "\u0418\u0437\u0430\u0431\u0435\u0440\u0438 \u0432\u0440\u0435\u043C\u0435",
      startDate: "\u0414\u0430\u0442\u0443\u043C \u043F\u043E\u0447\u0435\u0442\u043A\u0430",
      startTime: "\u0412\u0440\u0435\u043C\u0435 \u043F\u043E\u0447\u0435\u0442\u043A\u0430",
      endDate: "\u0414\u0430\u0442\u0443\u043C \u0437\u0430\u0432\u0440\u0448\u0435\u0442\u043A\u0430",
      endTime: "\u0412\u0440\u0435\u043C\u0435 \u0437\u0430\u0432\u0440\u0448\u0435\u0442\u043A\u0430",
      prevYear: "\u041F\u0440\u0435\u0442\u0445\u043E\u0434\u043D\u0430 \u0433\u043E\u0434\u0438\u043D\u0430",
      nextYear: "\u0421\u043B\u0435\u0434\u0435\u045B\u0430 \u0433\u043E\u0434\u0438\u043D\u0430",
      prevMonth: "\u041F\u0440\u0435\u0442\u0445\u043E\u0434\u043D\u0438 \u043C\u0435\u0441\u0435\u0446",
      nextMonth: "\u0421\u043B\u0435\u0434\u0435\u045B\u0438 \u043C\u0435\u0441\u0435\u0446",
      year: "\u0433\u043E\u0434\u0438\u043D\u0430",
      month1: "\u0458\u0430\u043D\u0443\u0430\u0440",
      month2: "\u0444\u0435\u0431\u0440\u0443\u0430\u0440",
      month3: "\u043C\u0430\u0440\u0442",
      month4: "\u0430\u043F\u0440\u0438\u043B",
      month5: "\u043C\u0430\u0458",
      month6: "\u0458\u0443\u043D",
      month7: "\u0458\u0443\u043B",
      month8: "\u0430\u0432\u0433\u0443\u0441\u0442",
      month9: "\u0441\u0435\u043F\u0442\u0435\u043C\u0431\u0430\u0440",
      month10: "\u043E\u043A\u0442\u043E\u0431\u0430\u0440",
      month11: "\u043D\u043E\u0432\u0435\u043C\u0431\u0430\u0440",
      month12: "\u0434\u0435\u0446\u0435\u043C\u0431\u0430\u0440",
      week: "\u0441\u0435\u0434\u043C\u0438\u0446\u0430",
      weeks: {
        sun: "\u041D\u0435\u0434",
        mon: "\u041F\u043E\u043D",
        tue: "\u0423\u0442\u043E",
        wed: "\u0421\u0440\u0435",
        thu: "\u0427\u0435\u0442",
        fri: "\u041F\u0435\u0442",
        sat: "\u0421\u0443\u0431"
      },
      months: {
        jan: "\u0458\u0430\u043D",
        feb: "\u0444\u0435\u0431",
        mar: "\u043C\u0430\u0440",
        apr: "\u0430\u043F\u0440",
        may: "\u043C\u0430\u0458",
        jun: "\u0458\u0443\u043D",
        jul: "\u0458\u0443\u043B",
        aug: "\u0430\u0432\u0433",
        sep: "\u0441\u0435\u043F",
        oct: "\u043E\u043A\u0442",
        nov: "\u043D\u043E\u0432",
        dec: "\u0434\u0435\u0446"
      }
    },
    select: {
      loading: "\u0423\u0447\u0438\u0442\u0430\u0432\u0430\u045A\u0435",
      noMatch: "\u041D\u0435\u043C\u0430 \u0440\u0435\u0437\u0443\u043B\u0442\u0430\u0442\u0430",
      noData: "\u041D\u0435\u043C\u0430 \u043F\u043E\u0434\u0430\u0442\u0430\u043A\u0430",
      placeholder: "\u0418\u0437\u0430\u0431\u0435\u0440\u0438"
    },
    mention: {
      loading: "\u0423\u0447\u0438\u0442\u0430\u0432\u0430\u045A\u0435"
    },
    cascader: {
      noMatch: "\u041D\u0435\u043C\u0430 \u0440\u0435\u0437\u0443\u043B\u0442\u0430\u0442\u0430",
      loading: "\u0423\u0447\u0438\u0442\u0430\u0432\u0430\u045A\u0435",
      placeholder: "\u0418\u0437\u0430\u0431\u0435\u0440\u0438",
      noData: "\u041D\u0435\u043C\u0430 \u043F\u043E\u0434\u0430\u0442\u0430\u043A\u0430"
    },
    pagination: {
      goto: "\u0418\u0434\u0438 \u043D\u0430",
      pagesize: "/\u0441\u0442\u0440\u0430\u043D\u0438",
      total: "\u0423\u043A\u0443\u043F\u043D\u043E {total}",
      pageClassifier: "",
      page: "\u0421\u0442\u0440\u0430\u043D\u0430",
      prev: "\u0418\u0434\u0438 \u043D\u0430 \u043F\u0440\u0435\u0442\u0445\u043E\u0434\u043D\u0443 \u0441\u0442\u0440\u0430\u043D\u0443",
      next: "\u0418\u0434\u0438 \u043D\u0430 \u0441\u043B\u0435\u0434\u0435\u045B\u0443 \u0441\u0442\u0440\u0430\u043D\u0443",
      currentPage: "\u0441\u0442\u0440\u0430\u043D\u0430 {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "\u041F\u043E\u0440\u0443\u043A\u0430",
      confirm: "OK",
      cancel: "\u041E\u0442\u043A\u0430\u0436\u0438",
      error: "\u041D\u0435\u0438\u0441\u043F\u0440\u0430\u0432\u0430\u043D \u0443\u043D\u043E\u0441"
    },
    upload: {
      deleteTip: "\u043F\u0440\u0438\u0442\u0438\u0441\u043D\u0438 \u0411\u0420\u0418\u0428\u0418 \u0434\u0430 \u043E\u0431\u0440\u0438\u0448\u0435\u0448",
      delete: "\u0411\u0440\u0438\u0448\u0438",
      preview: "\u0412\u0438\u0434\u0438",
      continue: "\u041D\u0430\u0441\u0442\u0430\u0432\u0438"
    },
    table: {
      emptyText: "\u041D\u0435\u043C\u0430 \u043F\u043E\u0434\u0430\u0442\u0430\u043A\u0430",
      confirmFilter: "\u041F\u043E\u0442\u0432\u0440\u0434\u0438",
      resetFilter: "\u0420\u0435\u0441\u0435\u0442\u0443\u0458",
      clearFilter: "\u0421\u0432\u0435",
      sumText: "\u0417\u0431\u0438\u0440"
    },
    tree: {
      emptyText: "\u041D\u0435\u043C\u0430 \u043F\u043E\u0434\u0430\u0442\u0430\u043A\u0430"
    },
    transfer: {
      noMatch: "\u041D\u0435\u043C\u0430 \u0440\u0435\u0437\u0443\u043B\u0442\u0430\u0442\u0430",
      noData: "\u041D\u0435\u043C\u0430 \u043F\u043E\u0434\u0430\u0442\u0430\u043A\u0430",
      titles: ["\u041B\u0438\u0441\u0442\u0430 1", "\u041B\u0438\u0441\u0442\u0430 2"],
      filterPlaceholder: "\u0423\u043D\u0435\u0441\u0438 \u043A\u0459\u0443\u0447\u043D\u0443 \u0440\u0435\u0447",
      noCheckedFormat: "{total} \u0441\u0442\u0430\u0432\u043A\u0438",
      hasCheckedFormat: "{checked}/{total} \u043E\u0431\u0435\u043B\u0435\u0436\u0435\u043D\u0438\u0445"
    },
    image: {
      error: "\u041D\u0415\u0423\u0421\u041F\u0415\u0428\u041D\u041E"
    },
    pageHeader: {
      title: "\u041D\u0430\u0437\u0430\u0434"
    },
    popconfirm: {
      confirmButtonText: "\u0414\u0430",
      cancelButtonText: "\u041D\u0435"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { sr as default };
