/*! Element Plus v2.10.2 */

var fr = {
  name: "fr",
  el: {
    breadcrumb: {
      label: `<PERSON><PERSON> d'Ariane`
    },
    colorpicker: {
      confirm: "Confirmer",
      clear: "Effacer",
      defaultLabel: "color picker",
      description: "La couleur actuelle est {color}. Appuyer sur Entr\xE9e pour s\xE9lectionner une nouvelle couleur."
    },
    datepicker: {
      now: "Maintenant",
      today: "Auj.",
      cancel: "Annuler",
      clear: "Effacer",
      confirm: "Confirmer",
      dateTablePrompt: "Utiliser les touches fl\xE9ch\xE9es et appuyer sur Entr\xE9e pour s\xE9lectionner le jour du mois",
      monthTablePrompt: "Utiliser les touches fl\xE9ch\xE9es et appuyer sur Entr\xE9e pour s\xE9lectionner le mois",
      yearTablePrompt: "Utiliser les touches fl\xE9ch\xE9es et appuyer sur Entr\xE9e pour s\xE9lectionner l'ann\xE9e",
      selectedDate: "Date s\xE9lectionn\xE9e",
      selectDate: "Choisir date",
      selectTime: "Choisir horaire",
      startDate: "Date d\xE9but",
      startTime: "Horaire d\xE9but",
      endDate: "Date fin",
      endTime: "Horaire fin",
      prevYear: "Ann\xE9e pr\xE9c\xE9dente",
      nextYear: "Ann\xE9e suivante",
      prevMonth: "Mois pr\xE9c\xE9dent",
      nextMonth: "Mois suivant",
      year: "",
      month1: "Janvier",
      month2: "F\xE9vrier",
      month3: "Mars",
      month4: "Avril",
      month5: "Mai",
      month6: "Juin",
      month7: "Juillet",
      month8: "Ao\xFBt",
      month9: "Septembre",
      month10: "Octobre",
      month11: "Novembre",
      month12: "D\xE9cembre",
      week: "Semaine",
      weeks: {
        sun: "Dim",
        mon: "Lun",
        tue: "Mar",
        wed: "Mer",
        thu: "Jeu",
        fri: "Ven",
        sat: "Sam"
      },
      weeksFull: {
        sun: "Dimanche",
        mon: "Lundi",
        tue: "Mardi",
        wed: "Mercredi",
        thu: "Jeudi",
        fri: "Vendredi",
        sat: "Samedi"
      },
      months: {
        jan: "Jan",
        feb: "F\xE9v",
        mar: "Mar",
        apr: "Avr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Ao\xFB",
        sep: "Sep",
        oct: "Oct",
        nov: "Nov",
        dec: "D\xE9c"
      }
    },
    inputNumber: {
      decrease: "d\xE9cr\xE9menter",
      increase: "incr\xE9menter"
    },
    select: {
      loading: "Chargement",
      noMatch: "Aucune correspondance",
      noData: "Aucune donn\xE9e",
      placeholder: "Choisir"
    },
    mention: {
      loading: "Chargement"
    },
    cascader: {
      noMatch: "Aucune correspondance",
      loading: "Chargement",
      placeholder: "Choisir",
      noData: "Aucune donn\xE9e"
    },
    pagination: {
      goto: "Aller \xE0",
      pagesize: "/page",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Aller \xE0 la page pr\xE9c\xE9dente",
      next: "Aller \xE0 la page suivante",
      currentPage: "page {pager}",
      prevPages: "{pager} pages pr\xE9c\xE9dentes",
      nextPages: "{pager} pages suivantes",
      deprecationWarning: "Utilisations obsol\xE8tes d\xE9tect\xE9es, veuillez vous r\xE9f\xE9rer \xE0 la documentation el-pagination pour plus de d\xE9tails"
    },
    dialog: {
      close: "Fermer la bo\xEEte de dialogue"
    },
    drawer: {
      close: "Fermer la bo\xEEte de dialogue"
    },
    messagebox: {
      title: "Message",
      confirm: "Confirmer",
      cancel: "Annuler",
      error: "Erreur",
      close: "Fermer la bo\xEEte de dialogue"
    },
    upload: {
      deleteTip: "Cliquer sur supprimer pour retirer le fichier",
      delete: "Supprimer",
      preview: "Aper\xE7u",
      continue: "Continuer"
    },
    slider: {
      defaultLabel: "curseur entre {min} et {max}",
      defaultRangeStartLabel: "choisir la valeur de d\xE9part",
      defaultRangeEndLabel: "s\xE9lectionner la valeur finale"
    },
    table: {
      emptyText: "Aucune donn\xE9e",
      confirmFilter: "Confirmer",
      resetFilter: "R\xE9initialiser",
      clearFilter: "Tous",
      sumText: "Somme"
    },
    tour: {
      next: "suivant",
      previous: "pr\xE9c\xE9dent",
      finish: "fin"
    },
    tree: {
      emptyText: "Aucune donn\xE9e"
    },
    transfer: {
      noMatch: "Aucune correspondance",
      noData: "Aucune donn\xE9e",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Entrer un mot clef",
      noCheckedFormat: "{total} elements",
      hasCheckedFormat: "{checked}/{total} coch\xE9(s)"
    },
    image: {
      error: "ECHEC"
    },
    pageHeader: {
      title: "Retour"
    },
    popconfirm: {
      confirmButtonText: "Oui",
      cancelButtonText: "Non"
    },
    carousel: {
      leftArrow: "Fl\xE8che du carrousel vers la gauche",
      rightArrow: "Fl\xE8che du carrousel vers la droite",
      indicator: "Passer au carrousel index {index}"
    }
  }
};

export { fr as default };
