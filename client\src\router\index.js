import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard/product',
    name: 'ProductManage',
    component: () => import('../views/ProductManage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard/stock',
    name: 'StockManage',
    component: () => import('../views/StockManage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard/customer',
    name: 'CustomerManage',
    component: () => import('../views/CustomerManage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard/orders',
    name: 'OrderManage',
    component: () => import('../views/OrderManage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard/dishes',
    name: 'DishesManage',
    component: () => import('../views/DishesManage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/dashboard/categories',
    name: 'CategoriesManage',
    component: () => import('../views/CategoriesManage.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const isAuthenticated = authStore.isAuthenticated

  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (to.meta.requiresGuest && isAuthenticated) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router 