import type { ExtractPropTypes } from 'vue';
import type Watermark from './watermark.vue';
export interface WatermarkFontType {
    color?: string;
    fontSize?: number | string;
    fontWeight?: 'normal' | 'light' | 'weight' | number;
    fontStyle?: 'none' | 'normal' | 'italic' | 'oblique';
    fontFamily?: string;
    textAlign?: 'start' | 'end' | 'left' | 'right' | 'center';
    textBaseline?: 'top' | 'hanging' | 'middle' | 'alphabetic' | 'ideographic' | 'bottom';
}
export declare const watermarkProps: {
    readonly zIndex: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 9, boolean>;
    readonly rotate: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, -22, boolean>;
    readonly width: NumberConstructor;
    readonly height: NumberConstructor;
    readonly image: StringConstructor;
    readonly content: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | string[]) | (() => string | string[]) | ((new (...args: any[]) => string | string[]) | (() => string | string[]))[], unknown, unknown, "Element Plus", boolean>;
    readonly font: {
        readonly type: import("vue").PropType<WatermarkFontType>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly gap: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => [number, number]) | (() => [number, number]) | ((new (...args: any[]) => [number, number]) | (() => [number, number]))[], unknown, unknown, () => number[], boolean>;
    readonly offset: {
        readonly type: import("vue").PropType<[number, number]>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export type WatermarkProps = ExtractPropTypes<typeof watermarkProps>;
export type WatermarkInstance = InstanceType<typeof Watermark> & unknown;
