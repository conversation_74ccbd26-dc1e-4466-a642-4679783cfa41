{"version": 3, "file": "tour2.js", "sources": ["../../../../../../packages/components/tour/src/tour.vue"], "sourcesContent": ["<template>\n  <el-teleport :to=\"appendTo\">\n    <div :class=\"kls\" v-bind=\"$attrs\">\n      <el-tour-mask\n        :visible=\"mergedShowMask\"\n        :fill=\"mergedMaskStyle?.color\"\n        :style=\"mergedMaskStyle?.style\"\n        :pos=\"pos\"\n        :z-index=\"mergedZIndex\"\n        :target-area-clickable=\"targetAreaClickable\"\n      />\n      <el-tour-content\n        v-if=\"modelValue\"\n        :key=\"current\"\n        :reference=\"triggerTarget\"\n        :placement=\"mergedPlacement\"\n        :show-arrow=\"mergedShowArrow\"\n        :z-index=\"mergedZIndex\"\n        :style=\"mergedContentStyle\"\n        @close=\"onEscClose\"\n      >\n        <el-tour-steps :current=\"current\" @update-total=\"onUpdateTotal\">\n          <slot />\n        </el-tour-steps>\n      </el-tour-content>\n    </div>\n  </el-teleport>\n  <!-- just for IDE -->\n  <slot v-if=\"false\" name=\"indicators\" :current=\"current + 1\" :total=\"total\" />\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, ref, toRef, useSlots, watch } from 'vue'\nimport { useVModel } from '@vueuse/core'\nimport { useNamespace, useZIndex } from '@element-plus/hooks'\nimport { isBoolean } from '@element-plus/utils'\nimport ElTeleport from '@element-plus/components/teleport'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElTourMask from './mask.vue'\nimport ElTourContent from './content.vue'\nimport ElTourSteps from './steps'\nimport { tourEmits, tourProps } from './tour'\nimport { tourKey, useTarget } from './helper'\nimport type { TourStepProps } from './step'\n\ndefineOptions({\n  name: 'ElTour',\n})\n\nconst props = defineProps(tourProps)\nconst emit = defineEmits(tourEmits)\n\nconst ns = useNamespace('tour')\nconst total = ref(0)\nconst currentStep = ref<TourStepProps>()\n\nconst current = useVModel(props, 'current', emit, {\n  passive: true,\n})\n\nconst currentTarget = computed(() => currentStep.value?.target)\n\nconst kls = computed(() => [\n  ns.b(),\n  mergedType.value === 'primary' ? ns.m('primary') : '',\n])\n\nconst mergedPlacement = computed(\n  () => currentStep.value?.placement || props.placement\n)\n\nconst mergedContentStyle = computed(\n  () => currentStep.value?.contentStyle ?? props.contentStyle\n)\n\nconst mergedMask = computed(() => currentStep.value?.mask ?? props.mask)\nconst mergedShowMask = computed(() => !!mergedMask.value && props.modelValue)\nconst mergedMaskStyle = computed(() =>\n  isBoolean(mergedMask.value) ? undefined : mergedMask.value\n)\n\nconst mergedShowArrow = computed(\n  () =>\n    !!currentTarget.value && (currentStep.value?.showArrow ?? props.showArrow)\n)\n\nconst mergedScrollIntoViewOptions = computed(\n  () => currentStep.value?.scrollIntoViewOptions ?? props.scrollIntoViewOptions\n)\nconst mergedType = computed(() => currentStep.value?.type ?? props.type)\n\nconst { nextZIndex } = useZIndex()\nconst nowZIndex = nextZIndex()\nconst mergedZIndex = computed(() => props.zIndex ?? nowZIndex)\n\nconst { mergedPosInfo: pos, triggerTarget } = useTarget(\n  currentTarget,\n  toRef(props, 'modelValue'),\n  toRef(props, 'gap'),\n  mergedMask,\n  mergedScrollIntoViewOptions\n)\n\nwatch(\n  () => props.modelValue,\n  (val) => {\n    if (!val) {\n      current.value = 0\n    }\n  }\n)\n\nconst onEscClose = () => {\n  if (props.closeOnPressEscape) {\n    emit(UPDATE_MODEL_EVENT, false)\n    emit('close', current.value)\n  }\n}\n\nconst onUpdateTotal = (val: number) => {\n  total.value = val\n}\n\nconst slots = useSlots()\n\nprovide(tourKey, {\n  currentStep,\n  current,\n  total,\n  showClose: toRef(props, 'showClose'),\n  closeIcon: toRef(props, 'closeIcon') as any,\n  mergedType: mergedType as any,\n  ns,\n  slots,\n  updateModelValue(modelValue) {\n    emit(UPDATE_MODEL_EVENT, modelValue)\n  },\n  onClose() {\n    emit('close', current.value)\n  },\n  onFinish() {\n    emit('finish')\n  },\n  onChange() {\n    emit(CHANGE_EVENT, current.value)\n  },\n})\n</script>\n"], "names": ["useNamespace", "ref", "useVModel", "computed", "isBoolean", "useZIndex", "useTarget", "toRef", "watch", "UPDATE_MODEL_EVENT", "useSlots", "provide", "tourKey"], "mappings": ";;;;;;;;;;;;;;;;;;uCA6Cc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,KAAA,GAAQC,QAAI,CAAC,CAAA,CAAA;AACnB,IAAA,MAAM,cAAcA,OAAmB,EAAA,CAAA;AAEvC,IAAA,MAAM,OAAU,GAAAC,cAAA,CAAU,KAAO,EAAA,SAAA,EAAW,IAAM,EAAA;AAAA,MAChD,OAAS,EAAA,IAAA;AAAA,KACV,CAAA,CAAA;AAED,IAAA,MAAM,aAAgB,GAAAC,YAAA,CAAS,MAAM;AAErC,MAAM,IAAA,EAAA,CAAA;AAAqB,MACzB,OAAK,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,CAAA;AAAA,KAAA,CACL;AAAmD,IACrD,MAAC,GAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAA,EAAA,CAAA,CAAM,EAAkB;AAAA,MACtB,UAAM,CAAA,KAAA,KAAmB,SAAA,GAAA,EAAA,CAAA,CAAA,CAAa,SAAM,CAAA,GAAA,EAAA;AAAA,KAC9C,CAAA,CAAA;AAEA,IAAA,MAAM,eAAqB,GAAAA,YAAA,CAAA,MAAA;AAAA,MACzB,IAAM,EAAA,CAAA;AAAyC,MACjD,OAAA,CAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,KAAA,CAAA,SAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACA,IAAM,MAAA,iCAAgC,CAAA,MAAa;AACnD,MAAA,IAAM,EAAkB,EAAA,EAAA,CAAA;AAAA,MAAS,OACrB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAgB,CAAA,KAAI,SAAY,GAAW,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,YAAA,CAAA;AAAA,KACvD,CAAA,CAAA;AAEA,IAAA,MAAM,UAAkB,GAAAA,YAAA,CAAA,MAAA;AAAA,MACtB,IAAA,EACE,EAAE,EAAA,CAAA;AAA8D,MACpE,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAoC,IAAA,MAC5B,cAAY,GAAOA,YAAA,CAAA,MAAA,CAAA,CAAA,UAAA,CAAA,KAA+B,IAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,IAC1D,MAAA,eAAA,GAAAA,YAAA,CAAA,MAAAC,eAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,eAAsB,GAAAD,YAAA,CAAA;AAE5B,MAAM,IAAA,EAAE,EAAW,EAAA,CAAA;AACnB,MAAA,sBAA6B,CAAA,KAAA,KAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAC7B,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,2BAAsB,GAAAA,YAAkB,CAAA,MAAA;AAAA,MAC5C,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACA,aAAa,CAAY,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,qBAAA,CAAA;AAAA,KACzB,CAAA,CAAA;AAAkB,IAClB,MAAA,UAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACA,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACF,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA,QACQ,UAAM,EAAA,GAAAE,iBAAA,EAAA,CAAA;AAAA,IAAA,MACH,SAAA,GAAA,UAAA,EAAA,CAAA;AACP,IAAA,MAAA,YAAU,GAAAF,YAAA,CAAA,MAAA;AACR,MAAA,IAAA,EAAA,CAAA;AAAgB,MAClB,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,EAAA,aAAA,EAAA,GAAA,EAAA,aAAA,EAAA,GAAAG,gBAAA,CAAA,aAAA,EAAAC,SAAA,CAAA,KAAA,EAAA,YAAA,CAAA,EAAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,UAAA,EAAA,2BAAA,CAAA,CAAA;AAEA,IAAAC,SAAA,CAAM,sBAAmB,EAAA,CAAA,GAAA,KAAA;AACvB,MAAA,IAAI,MAAM;AACR,QAAA;AACA,OAAK;AAAsB,KAC7B,CAAA,CAAA;AAAA,IACF,MAAA,UAAA,GAAA,MAAA;AAEA,MAAM,IAAA,KAAA,CAAA,kBAAiC,EAAA;AACrC,QAAA,IAAM,CAAQC,wBAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QAChB,IAAA,CAAA,OAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAEA,OAAA;AAEA,KAAA,CAAA;AAAiB,IACf,MAAA,aAAA,GAAA,CAAA,GAAA,KAAA;AAAA,MACA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAAA,KACA,CAAA;AAAA,IACA,MAAA,KAAA,GAAiBC,YAAA,EAAA,CAAA;AAAkB,IACnCC,WAAA,CAAAC,cAAiB,EAAA;AAAkB,MACnC,WAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,qBAAiB,KAAY,EAAA,WAAA,CAAA;AAC3B,MAAA,uCAAmC,CAAA;AAAA,MACrC,UAAA;AAAA,MACA,EAAU;AACR,MAAK,KAAA;AAAsB,MAC7B,gBAAA,CAAA,UAAA,EAAA;AAAA,QACW,IAAA,CAAAH,wBAAA,EAAA,UAAA,CAAA,CAAA;AACT,OAAA;AAAa,MACf,OAAA,GAAA;AAAA,QACW,IAAA,CAAA,OAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACT,OAAK;AAA2B,MAClC,QAAA,GAAA;AAAA,QACD,IAAA,CAAA,QAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}