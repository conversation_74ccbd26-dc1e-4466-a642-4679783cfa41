'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var checkbox$1 = require('./src/checkbox2.js');
var checkboxButton = require('./src/checkbox-button.js');
var checkboxGroup$1 = require('./src/checkbox-group2.js');
var checkboxGroup = require('./src/checkbox-group.js');
var checkbox = require('./src/checkbox.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElCheckbox = install.withInstall(checkbox$1["default"], {
  CheckboxButton: checkboxButton["default"],
  CheckboxGroup: checkboxGroup$1["default"]
});
const ElCheckboxButton = install.withNoopInstall(checkboxButton["default"]);
const ElCheckboxGroup = install.withNoopInstall(checkboxGroup$1["default"]);

exports.checkboxGroupEmits = checkboxGroup.checkboxGroupEmits;
exports.checkboxGroupProps = checkboxGroup.checkboxGroupProps;
exports.checkboxEmits = checkbox.checkboxEmits;
exports.checkboxProps = checkbox.checkboxProps;
exports.checkboxGroupContextKey = constants.checkboxGroupContextKey;
exports.ElCheckbox = ElCheckbox;
exports.ElCheckboxButton = ElCheckboxButton;
exports.ElCheckboxGroup = ElCheckboxGroup;
exports["default"] = ElCheckbox;
//# sourceMappingURL=index.js.map
