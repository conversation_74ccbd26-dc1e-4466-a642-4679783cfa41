import type { ExtractPropTypes } from 'vue';
export declare const backtopProps: {
    /**
     * @description the button will not show until the scroll height reaches this value.
     */
    readonly visibilityHeight: {
        readonly type: NumberConstructor;
        readonly default: 200;
    };
    /**
     * @description the target to trigger scroll.
     */
    readonly target: {
        readonly type: StringConstructor;
        readonly default: "";
    };
    /**
     * @description right distance.
     */
    readonly right: {
        readonly type: NumberConstructor;
        readonly default: 40;
    };
    /**
     * @description bottom distance.
     */
    readonly bottom: {
        readonly type: NumberConstructor;
        readonly default: 40;
    };
};
export type BacktopProps = ExtractPropTypes<typeof backtopProps>;
export declare const backtopEmits: {
    click: (evt: MouseEvent) => boolean;
};
export type BacktopEmits = typeof backtopEmits;
