{"version": 3, "file": "make-installer.js", "sources": ["../../../packages/element-plus/make-installer.ts"], "sourcesContent": ["import { provideGlobalConfig } from '@element-plus/components/config-provider'\nimport { INSTALLED_KEY } from '@element-plus/constants'\nimport { version } from './version'\n\nimport type { App, Plugin } from 'vue'\nimport type { ConfigProviderContext } from '@element-plus/components/config-provider'\n\nexport const makeInstaller = (components: Plugin[] = []) => {\n  const install = (app: App, options?: ConfigProviderContext) => {\n    if (app[INSTALLED_KEY]) return\n\n    app[INSTALLED_KEY] = true\n    components.forEach((c) => app.use(c))\n\n    if (options) provideGlobalConfig(options, app, true)\n  }\n\n  return {\n    version,\n    install,\n  }\n}\n"], "names": ["INSTALLED_KEY", "provideGlobalConfig", "version"], "mappings": ";;;;;;;;AAGY,MAAC,aAAa,GAAG,CAAC,UAAU,GAAG,EAAE,KAAK;AAClD,EAAE,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO,KAAK;AACpC,IAAI,IAAI,GAAG,CAACA,iBAAa,CAAC;AAC1B,MAAM,OAAO;AACb,IAAI,GAAG,CAACA,iBAAa,CAAC,GAAG,IAAI,CAAC;AAC9B,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,OAAO;AACf,MAAMC,mCAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9C,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,aAAIC,eAAO;AACX,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}