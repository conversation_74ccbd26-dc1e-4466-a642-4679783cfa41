const DEFAULT_DYNAMIC_LIST_ITEM_SIZE = 50;
const ITEM_RENDER_EVT = "itemRendered";
const SCROLL_EVT = "scroll";
const FORWARD = "forward";
const BACKWARD = "backward";
const AUTO_ALIGNMENT = "auto";
const SMART_ALIGNMENT = "smart";
const START_ALIGNMENT = "start";
const CENTERED_ALIGNMENT = "center";
const END_ALIGNMENT = "end";
const HORIZONTAL = "horizontal";
const VERTICAL = "vertical";
const LTR = "ltr";
const RTL = "rtl";
const RTL_OFFSET_NAG = "negative";
const RTL_OFFSET_POS_ASC = "positive-ascending";
const RTL_OFFSET_POS_DESC = "positive-descending";
const ScrollbarSizeKey = {
  [HORIZONTAL]: "height",
  [VERTICAL]: "width"
};
const ScrollbarDirKey = {
  [HORIZONTAL]: "left",
  [VERTICAL]: "top"
};
const SCROLLBAR_MIN_SIZE = 20;

export { AUTO_ALIGNMENT, BACKWARD, CENTERED_ALIGNMENT, DEFAULT_DYNAMIC_LIST_ITEM_SIZE, END_ALIGNMENT, FORWARD, HORIZONTAL, ITEM_RENDER_EVT, LTR, RTL, RTL_OFFSET_NAG, RTL_OFFSET_POS_ASC, RTL_OFFSET_POS_DESC, SCROLLBAR_MIN_SIZE, SCROLL_EVT, SMART_ALIGNMENT, START_ALIGNMENT, ScrollbarDirKey, ScrollbarSizeKey, VERTICAL };
//# sourceMappingURL=defaults.mjs.map
