{"version": 3, "file": "nl.mjs", "sources": ["../../../../../packages/locale/lang/nl.ts"], "sourcesContent": ["export default {\n  name: 'nl',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Bevestig',\n      clear: 'Wissen',\n    },\n    datepicker: {\n      now: 'Nu',\n      today: '<PERSON><PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON>',\n      clear: 'Legen',\n      confirm: 'Bevestig',\n      selectDate: 'Selecteer datum',\n      selectTime: 'Selecteer tijd',\n      startDate: 'Startdatum',\n      startTime: 'Starttijd',\n      endDate: 'Einddatum',\n      endTime: 'Eindtijd',\n      prevYear: 'Vorig jaar',\n      nextYear: 'Volgend jaar',\n      prevMonth: 'Vorige maand',\n      nextMonth: 'Volgende maand',\n      year: '',\n      month1: 'januari',\n      month2: 'februari',\n      month3: 'maart',\n      month4: 'april',\n      month5: 'mei',\n      month6: 'juni',\n      month7: 'juli',\n      month8: 'augustus',\n      month9: 'september',\n      month10: 'oktober',\n      month11: 'november',\n      month12: 'december',\n      // week: 'week',\n      weeks: {\n        sun: 'Zo',\n        mon: 'Ma',\n        tue: 'Di',\n        wed: 'Wo',\n        thu: 'Do',\n        fri: 'Vr',\n        sat: 'Za',\n      },\n      months: {\n        jan: 'jan',\n        feb: 'feb',\n        mar: 'maa',\n        apr: 'apr',\n        may: 'mei',\n        jun: 'jun',\n        jul: 'jul',\n        aug: 'aug',\n        sep: 'sep',\n        oct: 'okt',\n        nov: 'nov',\n        dec: 'dec',\n      },\n    },\n    select: {\n      loading: 'Laden',\n      noMatch: 'Geen overeenkomende resultaten',\n      noData: 'Geen data',\n      placeholder: 'Selecteer',\n    },\n    mention: {\n      loading: 'Laden',\n    },\n    cascader: {\n      noMatch: 'Geen overeenkomende resultaten',\n      loading: 'Laden',\n      placeholder: 'Selecteer',\n      noData: 'Geen data',\n    },\n    pagination: {\n      goto: 'Ga naar',\n      pagesize: '/pagina',\n      total: 'Totaal {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Bericht',\n      confirm: 'Bevestig',\n      cancel: 'Annuleren',\n      error: 'Ongeldige invoer',\n    },\n    upload: {\n      deleteTip: 'Kies verwijder om te wissen',\n      delete: 'Verwijder',\n      preview: 'Voorbeeld',\n      continue: 'Doorgaan',\n    },\n    table: {\n      emptyText: 'Geen data',\n      confirmFilter: 'Bevestigen',\n      resetFilter: 'Reset',\n      clearFilter: 'Alles',\n      sumText: 'Som',\n    },\n    tree: {\n      emptyText: 'Geen data',\n    },\n    transfer: {\n      noMatch: 'Geen overeenkomende resultaten',\n      noData: 'Geen data',\n      titles: ['Lijst 1', 'Lijst 2'],\n      filterPlaceholder: 'Geef zoekwoerd',\n      noCheckedFormat: '{total} items',\n      hasCheckedFormat: '{checked}/{total} geselecteerd',\n    },\n    image: {\n      error: 'MISLUKT',\n    },\n    pageHeader: {\n      title: 'Terug',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nee',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,IAAI;AACf,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,WAAW,EAAE,WAAW;AAC9B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,MAAM,EAAE,WAAW;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6BAA6B;AAC9C,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,aAAa,EAAE,YAAY;AACjC,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,WAAW;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,gBAAgB;AACzC,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,gCAAgC;AACxD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}