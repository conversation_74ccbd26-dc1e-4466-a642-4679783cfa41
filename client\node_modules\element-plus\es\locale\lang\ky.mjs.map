{"version": 3, "file": "ky.mjs", "sources": ["../../../../../packages/locale/lang/ky.ts"], "sourcesContent": ["export default {\n  name: 'ky',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Мурунку',\n      clear: 'ачык',\n    },\n    datepicker: {\n      now: 'азыр',\n      today: 'бүгүн',\n      cancel: 'жокко чыгарылды',\n      clear: 'ачык',\n      confirm: 'белгилөө',\n      selectDate: 'дата',\n      selectTime: 'тандоо убактысы',\n      startDate: 'Башталган датасы',\n      startTime: 'Start убакыт',\n      endDate: 'Бүткөн датасы',\n      endTime: 'End убакыт',\n      prevYear: 'өткөн жылы',\n      nextYear: 'бир жылдан кийин',\n      prevMonth: 'Өткөн айда',\n      nextMonth: 'Кийинки ай',\n      year: 'жыл',\n      month1: 'биринчи ай',\n      month2: 'Экин<PERSON>и айда',\n      month3: 'Үчүнчү айда',\n      month4: 'Төртүнчү айда',\n      month5: 'бешинчи айда',\n      month6: 'Алгач<PERSON>ы алты ай',\n      month7: 'жетинчи айда',\n      month8: 'сегизинчи ай',\n      month9: 'Алгачкы тогуз ай',\n      month10: 'онунчу айда',\n      month11: 'он биринчи ай',\n      month12: 'он экинчи айда',\n      // week: '周次',\n      weeks: {\n        sun: 'жети жума',\n        mon: 'дүйшөмбү',\n        tue: 'шейшемби',\n        wed: 'шаршемби',\n        thu: 'бейшемби',\n        fri: 'жума',\n        sat: 'ишемби',\n      },\n      months: {\n        jan: 'биринчи ай',\n        feb: 'Экинчи айда',\n        mar: 'Үчүнчү айда',\n        apr: 'Төртүнчү айда',\n        may: 'бешинчи айда',\n        jun: 'Алгачкы алты ай',\n        jul: 'жетинчи айда',\n        aug: 'сегизинчи ай',\n        sep: 'Алгачкы тогуз ай',\n        oct: 'онунчу айда',\n        nov: 'он биринчи ай',\n        dec: 'он экинчи айда',\n      },\n    },\n    select: {\n      loading: 'Жүктөлүүдө',\n      noMatch: 'Дал келген маалыматтар',\n      noData: 'маалымат жок',\n      placeholder: 'тандоо',\n    },\n    mention: {\n      loading: 'Жүктөлүүдө',\n    },\n    cascader: {\n      noMatch: 'Дал келген маалыматтар',\n      loading: 'Жүктөлүүдө',\n      placeholder: 'тандоо',\n      noData: 'маалымат жок',\n    },\n    pagination: {\n      goto: 'Мурунку',\n      pagesize: 'бир',\n      total: 'бүтүндөй {total} сан ',\n      pageClassifier: 'бет',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'тез',\n      confirm: 'белгилөө',\n      cancel: 'жокко чыгарылды',\n      error: 'Маалыматтарды киргизүү мыйзамдуу эмес!',\n    },\n    upload: {\n      deleteTip: 'Жок кылуу баскычын басуу жок',\n      delete: 'жок кылуу',\n      preview: 'ЖМКнын картинки',\n      continue: 'жүктөп бер',\n    },\n    table: {\n      emptyText: 'маалымат жок',\n      confirmFilter: 'чыпка',\n      resetFilter: 'кайра орнотуу',\n      clearFilter: 'бүткөн',\n      sumText: 'Бардыгы болуп',\n    },\n    tree: {\n      emptyText: 'маалымат жок',\n    },\n    transfer: {\n      noMatch: 'Дал келген маалыматтар',\n      noData: 'маалымат жок',\n      titles: ['1 тизмеси', '2 тизмеси'],\n      filterPlaceholder: 'Сураныч, издөө кирет',\n      noCheckedFormat: 'бүтүндөй {total} сан',\n      hasCheckedFormat: 'Тандалган {checked}/{total} сан',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,KAAK,EAAE,0BAA0B;AACvC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,0BAA0B;AACrC,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,uFAAuF;AACrG,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,UAAU,EAAE,0BAA0B;AAC5C,MAAM,UAAU,EAAE,uFAAuF;AACzG,MAAM,SAAS,EAAE,6FAA6F;AAC9G,MAAM,SAAS,EAAE,4CAA4C;AAC7D,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,OAAO,EAAE,0CAA0C;AACzD,MAAM,QAAQ,EAAE,yDAAyD;AACzE,MAAM,QAAQ,EAAE,wFAAwF;AACxG,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,MAAM,EAAE,2EAA2E;AACzF,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,MAAM,EAAE,kFAAkF;AAChG,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,MAAM,EAAE,wFAAwF;AACtG,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,OAAO,EAAE,sEAAsE;AACrF,MAAM,OAAO,EAAE,4EAA4E;AAC3F,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,mDAAmD;AAChE,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,yDAAyD;AACtE,QAAQ,GAAG,EAAE,+DAA+D;AAC5E,QAAQ,GAAG,EAAE,+DAA+D;AAC5E,QAAQ,GAAG,EAAE,2EAA2E;AACxF,QAAQ,GAAG,EAAE,qEAAqE;AAClF,QAAQ,GAAG,EAAE,kFAAkF;AAC/F,QAAQ,GAAG,EAAE,qEAAqE;AAClF,QAAQ,GAAG,EAAE,qEAAqE;AAClF,QAAQ,GAAG,EAAE,wFAAwF;AACrG,QAAQ,GAAG,EAAE,+DAA+D;AAC5E,QAAQ,GAAG,EAAE,sEAAsE;AACnF,QAAQ,GAAG,EAAE,4EAA4E;AACzF,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,OAAO,EAAE,4HAA4H;AAC3I,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,WAAW,EAAE,sCAAsC;AACzD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,8DAA8D;AAC7E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4HAA4H;AAC3I,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,MAAM,EAAE,qEAAqE;AACnF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,KAAK,EAAE,8EAA8E;AAC3F,MAAM,cAAc,EAAE,oBAAoB;AAC1C,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,MAAM,EAAE,uFAAuF;AACrG,MAAM,KAAK,EAAE,kNAAkN;AAC/N,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,sJAAsJ;AACvK,MAAM,MAAM,EAAE,mDAAmD;AACjE,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,QAAQ,EAAE,yDAAyD;AACzE,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,aAAa,EAAE,gCAAgC;AACrD,MAAM,WAAW,EAAE,2EAA2E;AAC9F,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,OAAO,EAAE,2EAA2E;AAC1F,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,qEAAqE;AACtF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4HAA4H;AAC3I,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,MAAM,EAAE,CAAC,8CAA8C,EAAE,8CAA8C,CAAC;AAC9G,MAAM,iBAAiB,EAAE,2GAA2G;AACpI,MAAM,eAAe,EAAE,6EAA6E;AACpG,MAAM,gBAAgB,EAAE,6FAA6F;AACrH,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}