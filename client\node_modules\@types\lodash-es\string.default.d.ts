import camelCase from "./camelCase";
import capitalize from "./capitalize";
import deburr from "./deburr";
import endsWith from "./endsWith";
import escape from "./escape";
import escapeRegExp from "./escapeRegExp";
import kebabCase from "./kebabCase";
import lowerCase from "./lowerCase";
import lowerFirst from "./lowerFirst";
import pad from "./pad";
import padEnd from "./padEnd";
import padStart from "./padStart";
import parseInt from "./parseInt";
import repeat from "./repeat";
import replace from "./replace";
import snakeCase from "./snakeCase";
import split from "./split";
import startCase from "./startCase";
import startsWith from "./startsWith";
import template from "./template";
import templateSettings from "./templateSettings";
import toLower from "./toLower";
import toUpper from "./toUpper";
import trim from "./trim";
import trimEnd from "./trimEnd";
import trimStart from "./trimStart";
import truncate from "./truncate";
import unescape from "./unescape";
import upperCase from "./upperCase";
import upperFirst from "./upperFirst";
import words from "./words";

declare const defaultExport: {
    camelCase: typeof camelCase;
    capitalize: typeof capitalize;
    deburr: typeof deburr;
    endsWith: typeof endsWith;
    escape: typeof escape;
    escapeRegExp: typeof escapeRegExp;
    kebabCase: typeof kebabCase;
    lowerCase: typeof lowerCase;
    lowerFirst: typeof lowerFirst;
    pad: typeof pad;
    padEnd: typeof padEnd;
    padStart: typeof padStart;
    parseInt: typeof parseInt;
    repeat: typeof repeat;
    replace: typeof replace;
    snakeCase: typeof snakeCase;
    split: typeof split;
    startCase: typeof startCase;
    startsWith: typeof startsWith;
    template: typeof template;
    templateSettings: typeof templateSettings;
    toLower: typeof toLower;
    toUpper: typeof toUpper;
    trim: typeof trim;
    trimEnd: typeof trimEnd;
    trimStart: typeof trimStart;
    truncate: typeof truncate;
    unescape: typeof unescape;
    upperCase: typeof upperCase;
    upperFirst: typeof upperFirst;
    words: typeof words;
};
export default defaultExport;
