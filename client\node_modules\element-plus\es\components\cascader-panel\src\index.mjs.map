{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/index.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b('panel'), ns.is('bordered', border)]\"\n    @keydown=\"handleKeyDown\"\n  >\n    <el-cascader-menu\n      v-for=\"(menu, index) in menus\"\n      :key=\"index\"\n      :ref=\"(item) => (menuList[index] = item)\"\n      :index=\"index\"\n      :nodes=\"[...menu]\"\n    >\n      <template #empty>\n        <slot name=\"empty\" />\n      </template>\n    </el-cascader-menu>\n  </div>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  nextTick,\n  onBeforeUpdate,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { cloneDeep, flattenDeep, isEqual } from 'lodash-unified'\nimport {\n  castArray,\n  focusNode,\n  getSibling,\n  isClient,\n  isEmpty,\n  scrollIntoView,\n  unique,\n} from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport ElCascaderMenu from './menu.vue'\nimport Store from './store'\nimport Node from './node'\nimport { CommonProps, useCascaderConfig } from './config'\nimport { checkNode, getMenuIndex, sortByOriginalOrder } from './utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  default as CascaderNode,\n  CascaderNodeValue,\n  CascaderOption,\n  CascaderValue,\n  RenderLabel,\n} from './node'\n\nimport type { ElCascaderPanelContext } from './types'\n\nexport default defineComponent({\n  name: 'ElCascaderPanel',\n\n  components: {\n    ElCascaderMenu,\n  },\n\n  props: {\n    ...CommonProps,\n    border: {\n      type: Boolean,\n      default: true,\n    },\n    renderLabel: Function as PropType<RenderLabel>,\n  },\n\n  emits: [UPDATE_MODEL_EVENT, CHANGE_EVENT, 'close', 'expand-change'],\n\n  setup(props, { emit, slots }) {\n    // for interrupt sync check status in lazy mode\n    let manualChecked = false\n\n    const ns = useNamespace('cascader')\n    const config = useCascaderConfig(props)\n\n    let store: Nullable<Store> = null\n    const initialLoaded = ref(true)\n    const menuList = ref<any[]>([])\n    const checkedValue = ref<Nullable<CascaderValue>>(null)\n    const menus = ref<CascaderNode[][]>([])\n    const expandingNode = ref<Nullable<CascaderNode>>(null)\n    const checkedNodes = ref<CascaderNode[]>([])\n\n    const isHoverMenu = computed(() => config.value.expandTrigger === 'hover')\n    const renderLabelFn = computed(() => props.renderLabel || slots.default)\n\n    const initStore = () => {\n      const { options } = props\n      const cfg = config.value\n\n      manualChecked = false\n      store = new Store(options, cfg)\n      menus.value = [store.getNodes()]\n\n      if (cfg.lazy && isEmpty(props.options)) {\n        initialLoaded.value = false\n        lazyLoad(undefined, (list) => {\n          if (list) {\n            store = new Store(list, cfg)\n            menus.value = [store.getNodes()]\n          }\n          initialLoaded.value = true\n          syncCheckedValue(false, true)\n        })\n      } else {\n        syncCheckedValue(false, true)\n      }\n    }\n\n    const lazyLoad: ElCascaderPanelContext['lazyLoad'] = (node, cb) => {\n      const cfg = config.value\n      node! = node || new Node({}, cfg, undefined, true)\n      node.loading = true\n\n      const resolve = (dataList: CascaderOption[]) => {\n        const _node = node as Node\n        const parent = _node.root ? null : _node\n        dataList && store?.appendNodes(dataList, parent as any)\n        _node.loading = false\n        _node.loaded = true\n        _node.childrenData = _node.childrenData || []\n        cb && cb(dataList)\n      }\n\n      cfg.lazyLoad(node, resolve as any)\n    }\n\n    const expandNode: ElCascaderPanelContext['expandNode'] = (node, silent) => {\n      const { level } = node\n      const newMenus = menus.value.slice(0, level)\n      let newExpandingNode: Nullable<CascaderNode>\n\n      if (node.isLeaf) {\n        newExpandingNode = node.pathNodes[level - 2]\n      } else {\n        newExpandingNode = node\n        newMenus.push(node.children)\n      }\n\n      if (expandingNode.value?.uid !== newExpandingNode?.uid) {\n        expandingNode.value = node\n        menus.value = newMenus\n        !silent && emit('expand-change', node?.pathValues || [])\n      }\n    }\n\n    const handleCheckChange: ElCascaderPanelContext['handleCheckChange'] = (\n      node,\n      checked,\n      emitClose = true\n    ) => {\n      const { checkStrictly, multiple } = config.value\n      const oldNode = checkedNodes.value[0]\n      manualChecked = true\n\n      !multiple && oldNode?.doCheck(false)\n      node.doCheck(checked)\n      calculateCheckedValue()\n      emitClose && !multiple && !checkStrictly && emit('close')\n      !emitClose && !multiple && !checkStrictly && expandParentNode(node)\n    }\n\n    const expandParentNode = (node) => {\n      if (!node) return\n      node = node.parent\n      expandParentNode(node)\n      node && expandNode(node)\n    }\n\n    const getFlattedNodes = (leafOnly: boolean) => {\n      return store?.getFlattedNodes(leafOnly)\n    }\n\n    const getCheckedNodes = (leafOnly: boolean) => {\n      return getFlattedNodes(leafOnly)?.filter((node) => node.checked !== false)\n    }\n\n    const clearCheckedNodes = () => {\n      checkedNodes.value.forEach((node) => node.doCheck(false))\n      calculateCheckedValue()\n      menus.value = menus.value.slice(0, 1)\n      expandingNode.value = null\n      emit('expand-change', [])\n    }\n\n    const calculateCheckedValue = () => {\n      const { checkStrictly, multiple } = config.value\n      const oldNodes = checkedNodes.value\n      const newNodes = getCheckedNodes(!checkStrictly)!\n      // ensure the original order\n      const nodes = sortByOriginalOrder(oldNodes, newNodes)\n      const values = nodes.map((node) => node.valueByOption)\n      checkedNodes.value = nodes\n      checkedValue.value = multiple ? values : values[0] ?? null\n    }\n\n    const syncCheckedValue = (loaded = false, forced = false) => {\n      const { modelValue } = props\n      const { lazy, multiple, checkStrictly } = config.value\n      const leafOnly = !checkStrictly\n\n      if (\n        !initialLoaded.value ||\n        manualChecked ||\n        (!forced && isEqual(modelValue, checkedValue.value))\n      )\n        return\n\n      if (lazy && !loaded) {\n        const values: CascaderNodeValue[] = unique(\n          flattenDeep(castArray(modelValue))\n        )\n        const nodes = values\n          .map((val) => store?.getNodeByValue(val))\n          .filter((node) => !!node && !node.loaded && !node.loading) as Node[]\n\n        if (nodes.length) {\n          nodes.forEach((node) => {\n            lazyLoad(node, () => syncCheckedValue(false, forced))\n          })\n        } else {\n          syncCheckedValue(true, forced)\n        }\n      } else {\n        const values = multiple ? castArray(modelValue) : [modelValue]\n        const nodes = unique(\n          values.map((val) => store?.getNodeByValue(val, leafOnly))\n        ) as Node[]\n        syncMenuState(nodes, forced)\n        checkedValue.value = cloneDeep(modelValue)\n      }\n    }\n\n    const syncMenuState = (\n      newCheckedNodes: CascaderNode[],\n      reserveExpandingState = true\n    ) => {\n      const { checkStrictly } = config.value\n      const oldNodes = checkedNodes.value\n      const newNodes = newCheckedNodes.filter(\n        (node) => !!node && (checkStrictly || node.isLeaf)\n      )\n      const oldExpandingNode = store?.getSameNode(expandingNode.value!)\n      const newExpandingNode =\n        (reserveExpandingState && oldExpandingNode) || newNodes[0]\n\n      if (newExpandingNode) {\n        newExpandingNode.pathNodes.forEach((node) => expandNode(node, true))\n      } else {\n        expandingNode.value = null\n      }\n\n      oldNodes.forEach((node) => node.doCheck(false))\n      reactive(newNodes).forEach((node) => node.doCheck(true))\n      checkedNodes.value = newNodes\n      nextTick(scrollToExpandingNode)\n    }\n\n    const scrollToExpandingNode = () => {\n      if (!isClient) return\n\n      menuList.value.forEach((menu) => {\n        const menuElement = menu?.$el\n        if (menuElement) {\n          const container = menuElement.querySelector(\n            `.${ns.namespace.value}-scrollbar__wrap`\n          )\n          const activeNode =\n            menuElement.querySelector(\n              `.${ns.b('node')}.${ns.is('active')}:last-child`\n            ) || menuElement.querySelector(`.${ns.b('node')}.in-active-path`)\n          scrollIntoView(container, activeNode)\n        }\n      })\n    }\n\n    const handleKeyDown = (e: KeyboardEvent) => {\n      const target = e.target as HTMLElement\n      const { code } = e\n\n      switch (code) {\n        case EVENT_CODE.up:\n        case EVENT_CODE.down: {\n          e.preventDefault()\n          const distance = code === EVENT_CODE.up ? -1 : 1\n          focusNode(\n            getSibling(target, distance, `.${ns.b('node')}[tabindex=\"-1\"]`)\n          )\n          break\n        }\n        case EVENT_CODE.left: {\n          e.preventDefault()\n          const preMenu = menuList.value[getMenuIndex(target) - 1]\n          const expandedNode = preMenu?.$el.querySelector(\n            `.${ns.b('node')}[aria-expanded=\"true\"]`\n          )\n          focusNode(expandedNode)\n          break\n        }\n        case EVENT_CODE.right: {\n          e.preventDefault()\n          const nextMenu = menuList.value[getMenuIndex(target) + 1]\n          const firstNode = nextMenu?.$el.querySelector(\n            `.${ns.b('node')}[tabindex=\"-1\"]`\n          )\n          focusNode(firstNode)\n          break\n        }\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n          checkNode(target)\n          break\n      }\n    }\n\n    provide(\n      CASCADER_PANEL_INJECTION_KEY,\n      reactive({\n        config,\n        expandingNode,\n        checkedNodes,\n        isHoverMenu,\n        initialLoaded,\n        renderLabelFn,\n        lazyLoad,\n        expandNode,\n        handleCheckChange,\n      })\n    )\n\n    watch([config, () => props.options], initStore, {\n      deep: true,\n      immediate: true,\n    })\n\n    watch(\n      () => props.modelValue,\n      () => {\n        manualChecked = false\n        syncCheckedValue()\n      },\n      {\n        deep: true,\n      }\n    )\n\n    watch(\n      () => checkedValue.value,\n      (val) => {\n        if (!isEqual(val, props.modelValue)) {\n          emit(UPDATE_MODEL_EVENT, val)\n          emit(CHANGE_EVENT, val)\n        }\n      }\n    )\n\n    onBeforeUpdate(() => (menuList.value = []))\n\n    onMounted(() => !isEmpty(props.modelValue) && syncCheckedValue())\n\n    return {\n      ns,\n      menuList,\n      menus,\n      checkedNodes,\n      handleKeyDown,\n      handleCheckChange,\n      getFlattedNodes,\n      /**\n       * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n       */\n      getCheckedNodes,\n      /**\n       * @description clear checked nodes\n       */\n      clearCheckedNodes,\n      calculateCheckedValue,\n      scrollToExpandingNode,\n    }\n  },\n})\n</script>\n"], "names": ["_resolveComponent", "_openBlock", "_createElementBlock", "_normalizeClass", "_Fragment", "_renderList", "_createBlock", "_withCtx", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;;;;;AAoEA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,iBAAA;AAAA,EAEN,UAAY,EAAA;AAAA,IACV,cAAA;AAAA,GACF;AAAA,EAEA,KAAO,EAAA;AAAA,IACL,GAAG,WAAA;AAAA,IACH,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,WAAa,EAAA,QAAA;AAAA,GACf;AAAA,EAEA,KAAO,EAAA,CAAC,kBAAoB,EAAA,YAAA,EAAc,SAAS,eAAe,CAAA;AAAA,EAElE,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAM,OAAS,EAAA;AAE5B,IAAA,IAAI,aAAgB,GAAA,KAAA,CAAA;AAEpB,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAA,MAAA,GAAS,kBAAkB,KAAK,CAAA,CAAA;AAEtC,IAAA,IAAI,KAAyB,GAAA,IAAA,CAAA;AAC7B,IAAM,MAAA,aAAA,GAAgB,IAAI,IAAI,CAAA,CAAA;AAC9B,IAAM,MAAA,QAAA,GAAW,GAAW,CAAA,EAAE,CAAA,CAAA;AAC9B,IAAM,MAAA,YAAA,GAAe,IAA6B,IAAI,CAAA,CAAA;AACtD,IAAM,MAAA,KAAA,GAAQ,GAAsB,CAAA,EAAE,CAAA,CAAA;AACtC,IAAM,MAAA,aAAA,GAAgB,IAA4B,IAAI,CAAA,CAAA;AACtD,IAAM,MAAA,YAAA,GAAe,GAAoB,CAAA,EAAE,CAAA,CAAA;AAE3C,IAAA,MAAM,cAAc,QAAS,CAAA,MAAM,MAAO,CAAA,KAAA,CAAM,kBAAkB,OAAO,CAAA,CAAA;AACzE,IAAA,MAAM,gBAAgB,QAAS,CAAA,MAAM,KAAM,CAAA,WAAA,IAAe,MAAM,OAAO,CAAA,CAAA;AAEvE,IAAA,MAAM,YAAY,MAAM;AACtB,MAAM,MAAA,EAAE,SAAY,GAAA,KAAA,CAAA;AACpB,MAAA,MAAM,MAAM,MAAO,CAAA,KAAA,CAAA;AAEnB,MAAgB,aAAA,GAAA,KAAA,CAAA;AAChB,MAAQ,KAAA,GAAA,IAAI,KAAM,CAAA,OAAA,EAAS,GAAG,CAAA,CAAA;AAC9B,MAAA,KAAA,CAAM,KAAQ,GAAA,CAAC,KAAM,CAAA,QAAA,EAAU,CAAA,CAAA;AAE/B,MAAA,IAAI,GAAI,CAAA,IAAA,IAAQ,OAAQ,CAAA,KAAA,CAAM,OAAO,CAAG,EAAA;AACtC,QAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AACtB,QAAS,QAAA,CAAA,KAAA,CAAA,EAAW,CAAC,IAAS,KAAA;AAC5B,UAAA,IAAI,IAAM,EAAA;AACR,YAAQ,KAAA,GAAA,IAAI,KAAM,CAAA,IAAA,EAAM,GAAG,CAAA,CAAA;AAC3B,YAAA,KAAA,CAAM,KAAQ,GAAA,CAAC,KAAM,CAAA,QAAA,EAAU,CAAA,CAAA;AAAA,WACjC;AACA,UAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,UAAA,gBAAA,CAAiB,OAAO,IAAI,CAAA,CAAA;AAAA,SAC7B,CAAA,CAAA;AAAA,OACI,MAAA;AACL,QAAA,gBAAA,CAAiB,OAAO,IAAI,CAAA,CAAA;AAAA,OAC9B;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,QAAA,GAA+C,CAAC,IAAA,EAAM,EAAO,KAAA;AACjE,MAAA,MAAM,MAAM,MAAO,CAAA,KAAA,CAAA;AACnB,MAAA,IAAA,GAAQ,QAAQ,IAAI,IAAA,CAAK,EAAI,EAAA,GAAA,EAAK,QAAW,IAAI,CAAA,CAAA;AACjD,MAAA,IAAA,CAAK,OAAU,GAAA,IAAA,CAAA;AAEf,MAAM,MAAA,OAAA,GAAU,CAAC,QAA+B,KAAA;AAC9C,QAAA,MAAM,KAAQ,GAAA,IAAA,CAAA;AACd,QAAM,MAAA,MAAA,GAAS,KAAM,CAAA,IAAA,GAAO,IAAO,GAAA,KAAA,CAAA;AACnC,QAAY,QAAA,KAAA,KAAA,IAAmB,IAAA,GAAA,KAAA,CAAA,GAAA,KAAuB,CAAA,WAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACtD,QAAA,KAAA,CAAM,OAAU,GAAA,KAAA,CAAA;AAChB,QAAA,KAAA,CAAM,MAAS,GAAA,IAAA,CAAA;AACf,QAAM,KAAA,CAAA,YAAA,GAAe,KAAM,CAAA,YAAA,IAAgB,EAAC,CAAA;AAC5C,QAAA,EAAA,IAAM,GAAG,QAAQ,CAAA,CAAA;AAAA,OACnB,CAAA;AAEA,MAAI,GAAA,CAAA,QAAA,CAAS,MAAM,OAAc,CAAA,CAAA;AAAA,KACnC,CAAA;AAEA,IAAM,MAAA,UAAA,GAAmD,CAAC,IAAA,EAAM,MAAW,KAAA;AACzE,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAM,EAAW,KAAA,EAAA,GAAA,IAAA,CAAM;AACvB,MAAI,MAAA,QAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAEJ,MAAA,IAAI,gBAAa,CAAA;AACf,MAAmB,IAAA,IAAA,CAAA,MAAA,EAAA;AAAwB,QACtC,gBAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACL,OAAmB,MAAA;AACnB,QAAS,mBAAU,IAAQ,CAAA;AAAA,QAC7B,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AAEA,OAAA;AACE,MAAA,IAAA,CAAA,CAAA,EAAA,GAAA,aAAsB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,OAAA,gBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAA,GAAA,CAAA,EAAA;AACtB,QAAA,aAAc,CAAA,KAAA,GAAA,IAAA,CAAA;AACd,QAAA,WAAW,GAAK,QAAA,CAAA;AAAuC,QACzD,CAAA,MAAA,IAAA,IAAA,CAAA,eAAA,EAAA,CAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,UAAA,KAAA,EAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AAKE,IAAA,MAAA,iBAAQ,GAAwB,CAAA,IAAA,EAAA,OAAW,EAAA,SAAA,GAAA,IAAA,KAAA;AAC3C,MAAM,MAAA,EAAA,aAAuB,EAAA,QAAA,EAAA,GAAO,MAAA,CAAA,KAAA,CAAA;AACpC,MAAgB,MAAA,OAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAEhB,MAAC,aAAY,GAAS,IAAA,CAAA;AACtB,MAAA,CAAA,aAAoB,OAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,OAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACpB,MAAsB,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACtB,MAAA,qBAAc,EAAA,CAAA;AACd,MAAA,cAAc,QAAC,IAAA,CAAY,aAAC,IAAA;AAAsC,MACpE,CAAA,SAAA,IAAA,CAAA,QAAA,IAAA,CAAA,aAAA,IAAA,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,gBAAO,GAAA,CAAA,IAAA,KAAA;AACX,MAAA,IAAA,CAAA,IAAY;AACZ,QAAA,OAAA;AACA,MAAA,IAAA,GAAA,YAAmB;AAAI,MACzB,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAM,IAAA,IAAA,UAAA,CAAA,IAAmB,CAAsB,CAAA;AAC7C,KAAO,CAAA;AAA+B,IACxC,MAAA,eAAA,GAAA,CAAA,QAAA,KAAA;AAEA,MAAM,OAAA,KAAA,IAAA,IAAA,GAAmB,KAAsB,CAAA,GAAA,KAAA,CAAA,eAAA,CAAA,QAAA,CAAA,CAAA;AAC7C,KAAO,CAAA;AAAkE,IAC3E,MAAA,eAAA,GAAA,CAAA,QAAA,KAAA;AAEA,MAAA,IAAM;AACJ,MAAA,OAAA,CAAA,EAAA,GAAa,eAAe,SAAS,CAAK,KAAA,IAAA,GAAQ,KAAK,CAAC,GAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,KAAA,KAAA,CAAA,CAAA;AACxD,KAAsB,CAAA;AACtB,IAAA,MAAA,iBAA0B,GAAA,MAAA;AAC1B,MAAA,YAAA,CAAA,KAAsB,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACtB,MAAK,qBAAA,EAAkB,CAAC;AAAA,MAC1B,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAEA,MAAA;AACE,MAAA,IAAA,CAAA,eAAQ,EAAA,EAAwB,CAAA,CAAA;AAChC,KAAA,CAAA;AACA,IAAM,MAAA,qBAA2B,GAAA,MAAC;AAElC,MAAM,IAAA,EAAA,CAAA;AACN,MAAA,MAAM,eAAe,EAAA,QAAK,EAAA,SAA2B,CAAA,KAAA,CAAA;AACrD,MAAA,MAAA,QAAqB,GAAA,YAAA,CAAA,KAAA,CAAA;AACrB,MAAA,MAAA,QAAqB,GAAA,eAAW,CAAS,CAAA,aAAA,CAAO,CAAC;AAAK,MACxD,MAAA,KAAA,GAAA,mBAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA;AAEA,MAAA,MAAyB,MAAA,GAAA,KAAA,CAAA,GAAC,CAAS,CAAA,IAAA,KAAA,IAAA,CAAO,aAAmB,CAAA,CAAA;AAC3D,MAAM,qBAAiB,KAAA,CAAA;AACvB,MAAA,YAAQ,CAAA,KAAgB,GAAA,QAAA,GAAA,MAAc,IAAI,EAAO,GAAA,MAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA;AACjD,KAAA,CAAA;AAEA,IACE,MAAA,gBACA,GAAA,CAAA,MAAA,GAAA,KAAA,EAAA,MACE,GAAU,KAAA,KAAA;AAEZ,MAAA,MAAA,EAAA,UAAA,EAAA,GAAA,KAAA,CAAA;AAEF,MAAI,MAAA,EAAA,IAAQ,EAAS,QAAA,EAAA,aAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA;AACnB,MAAA,MAAA,QAAoC,GAAA,CAAA,aAAA,CAAA;AAAA,MAClC,IAAA,CAAA,aAAsB,CAAA,KAAA,IAAA,aAAW,IAAA,CAAA,MAAA,IAAA,OAAA,CAAA,UAAA,EAAA,YAAA,CAAA,KAAA,CAAA;AAAA,QACnC,OAAA;AACA,MAAM,IAAA,IAAA,IAAA,CAAA;AAIN,QAAA,YAAkB,GAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAChB,QAAM,MAAA,KAAA,GAAA,MAAkB,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA;AACtB,QAAA,IAAA,KAAA,CAAA,MAAe,EAAA;AAAqC,UACtD,KAAC,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAAA,YACI,QAAA,CAAA,IAAA,EAAA,MAAA,gBAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACL,WAAA,CAAA,CAAA;AAA6B,SAC/B,MAAA;AAAA,UACK,gBAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AACL,SAAA;AACA,OAAA,MAAA;AAAc,QACZ,MAAA,SAAY,oBAA8B,CAAA,UAAA,CAAA,cAAc,CAAA,CAAA;AAAA,QAC1D,MAAA,KAAA,GAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,cAAA,CAAA,GAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AACA,QAAA,aAAA,CAAc,OAAO,MAAM,CAAA,CAAA;AAC3B,QAAa,YAAA,CAAA,KAAA,GAAQ,UAAU,UAAU,CAAA,CAAA;AAAA,OAC3C;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,aAAgB,GAAA,CACpB,eACA,EAAA,qBAAA,GAAwB,IACrB,KAAA;AACH,MAAM,MAAA,EAAE,aAAc,EAAA,GAAI,MAAO,CAAA,KAAA,CAAA;AACjC,MAAA,MAAM,WAAW,YAAa,CAAA,KAAA,CAAA;AAC9B,MAAA,MAAM,WAAW,eAAgB,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,CAAA,IAAA,KAAA,aAAA,IAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,MAAA,MACrB,gBAAE,mBAA+B,KAAA,CAAA,GAAA,KAAA,CAAA,WAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAC7C,MAAA,gBAAA,GAAA,qBAAA,IAAA,gBAAA,IAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,IAAA,gBAAyB,EAAA;AACzB,QAAA,gBACG,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,UAA8C,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAEjD,OAAA,MAAsB;AACpB,QAAA,aAAA,CAAA,aAA2B;AAAwC,OAC9D;AACL,MAAA,QAAA,CAAA,OAAc,CAAQ,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,MACxB,QAAA,CAAA,QAAA,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAEA,MAAA,kBAAkB,GAAA,QAAc,CAAA;AAChC,MAAS,QAAA,CAAA;AACT,KAAA,CAAA;AACA,IAAA,MAAA,qBAA8B,GAAA,MAAA;AAAA,MAChC,IAAA,CAAA,QAAA;AAEA,QAAA;AACE,MAAA,QAAe,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAEf,QAAS,MAAA,WAAc,GAAA,IAAU,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA;AAC/B,QAAA,IAAA;AACA,UAAA,MAAiB,SAAA,GAAA,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,CAAA,CAAA;AACf,UAAA,MAAM,aAAwB,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA,CAAA,IAAA,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAAA,UAC5B,cAAO,CAAA,SAAe,EAAA,UAAA,CAAA,CAAA;AAAA,SACxB;AACA,OAAA,CAAA,CAAA;AACc,KACV,CAAA;AAAmC,IACrC,MAAA,iBAAiB,CAAc,KAAA;AACjC,MAAA,MAAA,MAAA,GAAA,CAAA,CAAA;AAAoC,MACtC,MAAA,EAAA,IAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MACF,QAAC,IAAA;AAAA,QACH,KAAA,UAAA,CAAA,EAAA,CAAA;AAEA,QAAM,KAAA,UAAA,CAAA,IAAsC,EAAA;AAC1C,UAAA,CAAA,CAAM,cAAW,EAAA,CAAA;AACjB,UAAM,cAAW,GAAA,IAAA,KAAA,UAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAEjB,UAAA,SAAc,CAAA,UAAA,CAAA,MAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AAAA,gBACI;AAAA,SAChB;AACE,QAAA,KAAiB,UAAA,CAAA,IAAA,EAAA;AACjB,UAAA,CAAA,CAAA,cAAiB,EAAA,CAAA;AACjB,UAAA,MAAA,OAAA,GAAA,QAAA,CAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,UACE,MAAA,eAAmB,OAAU,IAAA,OAAS,KAAA,CAAA,GAAwB,OAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,CAAA;AAAA,UAChE,SAAA,CAAA,YAAA,CAAA,CAAA;AACA,UAAA,MAAA;AAAA,SACF;AAAA,QACA,KAAK,WAAW,KAAM,EAAA;AACpB,UAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AACjB,UAAA,MAAM,WAAmB,QAAA,CAAA,KAAM,CAAa,YAAA,CAAA,UAAW,CAAA,CAAA,CAAA;AACvD,UAAM,MAAA,SAAA,GAAA,YAAwB,IAAI,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAAA,UAAA,SACzB,CAAE,SAAO,CAAA,CAAA;AAAA,UAClB,MAAA;AACA,SAAA;AACA,QAAA,KAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACF,KAAA,UAAA,CAAA,WAAA;AAAA,UACA,gBAAuB,CAAA,CAAA;AACrB,UAAA,MAAiB;AACjB,OAAA;AACA,KAAM,CAAA;AAA0B,IAAA,OAAA,CAC9B,4BAAgB,EAAA,QAAA,CAAA;AAAA,MAClB,MAAA;AACA,MAAA,aAAA;AACA,MAAA,YAAA;AAAA,MACF,WAAA;AAAA,MAAA,aACgB;AAAA,MAAA,aACA;AACd,MAAA,QAAA;AACA,MAAA,UAAA;AAAA,MACJ,iBAAA;AAAA,KACF,CAAA,CAAA,CAAA;AAEA,IAAA,KAAA,CAAA,CAAA,MAAA,EAAA,MAAA,KAAA,CAAA,OAAA,CAAA,EAAA,SAAA,EAAA;AAAA,MACE,IAAA,EAAA,IAAA;AAAA,MACA,SAAS,EAAA,IAAA;AAAA,KACP,CAAA,CAAA;AAAA,IACA,KAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAAA,MACA,aAAA,GAAA,KAAA,CAAA;AAAA,MACA,gBAAA,EAAA,CAAA;AAAA,KACA,EAAA;AAAA,MACA,IAAA,EAAA,IAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,KAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACA,IAAA,CAAA,OAAA,CAAA,GAAA,EAAA,KAAA,CAAA,UAAA,CAAA,EAAA;AAAA,QACD,IAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACH,IAAA,CAAA,YAAA,EAAA,GAAA,CAAA,CAAA;AAEA,OAAA;AAAgD,KAAA,CAC9C,CAAM;AAAA,IAAA,cACK,CAAA,MAAA,QAAA,CAAA,KAAA,GAAA,EAAA,CAAA,CAAA;AAAA,IACb,SAAC,CAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,gBAAA,EAAA,CAAA,CAAA;AAED,IAAA,OAAA;AAAA,MACE;AAAY,MACZ,QAAM;AACJ,MAAgB,KAAA;AAChB,MAAiB,YAAA;AAAA,MACnB,aAAA;AAAA,MACA,iBAAA;AAAA,MAAA,eACQ;AAAA,MACR,eAAA;AAAA,MACF,iBAAA;AAEA,MAAA,qBAAA;AAAA,MACE,qBAAmB;AAAA,KAAA,CACnB;AACE,GAAA;AACE,CAAA,CAAA,CAAA;AACsB,SACxB,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACF,MAAA,2BAAA,GAAAA,gBAAA,CAAA,kBAAA,CAAA,CAAA;AAAA,EACF,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAEA,IAAA,KAAA,EAAAC,cAAsB,CAAA,CAAA,IAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,OAAG,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAE1C,IAAA,SAAA,OAAgB;AAEhB,GAAO,EAAA;AAAA,KACLF,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAE,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,EAAA,KAAA,KAAA;AAAA,MACA,OAAAJ,SAAA,EAAA,EAAAK,WAAA,CAAA,2BAAA,EAAA;AAAA,QACA,GAAA,EAAA,KAAA;AAAA,QACA,OAAA,EAAA,IAAA;AAAA,QACA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,IAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA,EAAA,CAAA,GAAA,IAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,UAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,CAAA;AAAA,SAIA,CAAA;AAAA,QAAA,CAAA,EAAA,CAAA;AAAA,OAAA,EAAA,IAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CAAA,EAAA,GAAA,CAAA;AAAA,GAIA,EAAA,EAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AAAA,CACA;AACA,oBACF,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,WAAA,CAAA,CAAA,CAAA;;;;"}