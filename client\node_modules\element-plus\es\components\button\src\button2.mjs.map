{"version": 3, "file": "button2.mjs", "sources": ["../../../../../../packages/components/button/src/button.vue"], "sourcesContent": ["<template>\n  <component\n    :is=\"tag\"\n    ref=\"_ref\"\n    v-bind=\"_props\"\n    :class=\"buttonKls\"\n    :style=\"buttonStyle\"\n    @click=\"handleClick\"\n  >\n    <template v-if=\"loading\">\n      <slot v-if=\"$slots.loading\" name=\"loading\" />\n      <el-icon v-else :class=\"ns.is('loading')\">\n        <component :is=\"loadingIcon\" />\n      </el-icon>\n    </template>\n    <el-icon v-else-if=\"icon || $slots.icon\">\n      <component :is=\"icon\" v-if=\"icon\" />\n      <slot v-else name=\"icon\" />\n    </el-icon>\n    <span\n      v-if=\"$slots.default\"\n      :class=\"{ [ns.em('text', 'expand')]: shouldAddSpace }\"\n    >\n      <slot />\n    </span>\n  </component>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useButton } from './use-button'\nimport { buttonEmits, buttonProps } from './button'\nimport { useButtonCustomStyle } from './button-custom'\n\ndefineOptions({\n  name: 'ElButton',\n})\n\nconst props = defineProps(buttonProps)\nconst emit = defineEmits(buttonEmits)\n\nconst buttonStyle = useButtonCustomStyle(props)\nconst ns = useNamespace('button')\nconst {\n  _ref,\n  _size,\n  _type,\n  _disabled,\n  _props,\n  _plain,\n  _round,\n  shouldAddSpace,\n  handleClick,\n} = useButton(props, emit)\nconst buttonKls = computed(() => [\n  ns.b(),\n  ns.m(_type.value),\n  ns.m(_size.value),\n  ns.is('disabled', _disabled.value),\n  ns.is('loading', props.loading),\n  ns.is('plain', _plain.value),\n  ns.is('round', _round.value),\n  ns.is('circle', props.circle),\n  ns.is('text', props.text),\n  ns.is('link', props.link),\n  ns.is('has-bg', props.bg),\n])\n\ndefineExpose({\n  /** @description button html element */\n  ref: _ref,\n  /** @description button size */\n  size: _size,\n  /** @description button type */\n  type: _type,\n  /** @description button disabled */\n  disabled: _disabled,\n  /** @description whether adding space */\n  shouldAddSpace,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_resolveDynamicComponent", "_mergeProps", "_unref"], "mappings": ";;;;;;;;mCAoCc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,WAAA,GAAc,qBAAqB,KAAK,CAAA,CAAA;AAC9C,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA;AAAA,MACJ,IAAA;AAAA,MACA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,SAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,cAAA;AAAA,MACA,WAAA;AAAA,KACF,GAAI,SAAU,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AACzB,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAAA,MAC/B,GAAG,CAAE,EAAA;AAAA,MACL,EAAA,CAAG,CAAE,CAAA,KAAA,CAAM,KAAK,CAAA;AAAA,MAChB,EAAA,CAAG,CAAE,CAAA,KAAA,CAAM,KAAK,CAAA;AAAA,MAChB,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,MACjC,EAAG,CAAA,EAAA,CAAG,SAAW,EAAA,KAAA,CAAM,OAAO,CAAA;AAAA,MAC9B,EAAG,CAAA,EAAA,CAAG,OAAS,EAAA,MAAA,CAAO,KAAK,CAAA;AAAA,MAC3B,EAAG,CAAA,EAAA,CAAG,OAAS,EAAA,MAAA,CAAO,KAAK,CAAA;AAAA,MAC3B,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA,MAC5B,EAAG,CAAA,EAAA,CAAG,MAAQ,EAAA,KAAA,CAAM,IAAI,CAAA;AAAA,MACxB,EAAG,CAAA,EAAA,CAAG,MAAQ,EAAA,KAAA,CAAM,IAAI,CAAA;AAAA,MACxB,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,KAAA,CAAM,EAAE,CAAA;AAAA,KACzB,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAAA,GAAA,EAAA,IAAA;AAAA,MAEX,IAAK,EAAA,KAAA;AAAA,MAAA,IAAA,EAAA,KAAA;AAAA,MAEL,QAAM,EAAA,SAAA;AAAA,MAAA,cAAA;AAAA,KAAA,CAEN,CAAM;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAEN,OAAUA,SAAA,EAAA,EAAAC,WAAA,CAAAC,uBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAAC,UAAA,CAAA;AAAA,QAAA,OAAA,EAAA,MAAA;AAAA,QAEV,GAAA,EAAA,IAAA;AAAA,OACD,EAAAC,KAAA,CAAA,MAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}