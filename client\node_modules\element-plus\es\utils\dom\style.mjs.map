{"version": 3, "file": "style.mjs", "sources": ["../../../../../packages/utils/dom/style.ts"], "sourcesContent": ["import { isNumber, isObject, isString, isStringNumber } from '../types'\nimport { isClient } from '../browser'\nimport { camelize } from '../strings'\nimport { entriesOf, keysOf } from '../objects'\nimport { debugWarn } from '../error'\nimport type { CSSProperties } from 'vue'\n\nconst SCOPE = 'utils/dom/style'\n\nexport const classNameToArray = (cls = '') =>\n  cls.split(' ').filter((item) => !!item.trim())\n\nexport const hasClass = (el: Element, cls: string): boolean => {\n  if (!el || !cls) return false\n  if (cls.includes(' ')) throw new Error('className should not contain space.')\n  return el.classList.contains(cls)\n}\n\nexport const addClass = (el: Element, cls: string) => {\n  if (!el || !cls.trim()) return\n  el.classList.add(...classNameToArray(cls))\n}\n\nexport const removeClass = (el: Element, cls: string) => {\n  if (!el || !cls.trim()) return\n  el.classList.remove(...classNameToArray(cls))\n}\n\nexport const getStyle = (\n  element: HTMLElement,\n  styleName: keyof CSSProperties\n): string => {\n  if (!isClient || !element || !styleName) return ''\n\n  let key = camelize(styleName)\n  if (key === 'float') key = 'cssFloat'\n  try {\n    const style = (element.style as any)[key]\n    if (style) return style\n    const computed: any = document.defaultView?.getComputedStyle(element, '')\n    return computed ? computed[key] : ''\n  } catch {\n    return (element.style as any)[key]\n  }\n}\n\nexport const setStyle = (\n  element: HTMLElement,\n  styleName: CSSProperties | keyof CSSProperties,\n  value?: string | number\n) => {\n  if (!element || !styleName) return\n\n  if (isObject(styleName)) {\n    entriesOf(styleName).forEach(([prop, value]) =>\n      setStyle(element, prop, value)\n    )\n  } else {\n    const key: any = camelize(styleName)\n    element.style[key] = value as any\n  }\n}\n\nexport const removeStyle = (\n  element: HTMLElement,\n  style: CSSProperties | keyof CSSProperties\n) => {\n  if (!element || !style) return\n\n  if (isObject(style)) {\n    keysOf(style).forEach((prop) => removeStyle(element, prop))\n  } else {\n    setStyle(element, style, '')\n  }\n}\n\nexport function addUnit(value?: string | number, defaultUnit = 'px') {\n  if (!value) return ''\n  if (isNumber(value) || isStringNumber(value)) {\n    return `${value}${defaultUnit}`\n  } else if (isString(value)) {\n    return value\n  }\n  debugWarn(SCOPE, 'binding value must be a string or number')\n}\n"], "names": [], "mappings": ";;;;;;AAKA,MAAM,KAAK,GAAG,iBAAiB,CAAC;AACpB,MAAC,gBAAgB,GAAG,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;AACjF,MAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK;AACrC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG;AACjB,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;AACvB,IAAI,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC3D,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpC,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK;AACrC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AACxB,IAAI,OAAO;AACX,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK;AACxC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AACxB,IAAI,OAAO;AACX,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,KAAK;AAChD,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS;AACzC,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AAChC,EAAE,IAAI,GAAG,KAAK,OAAO;AACrB,IAAI,GAAG,GAAG,UAAU,CAAC;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,IAAI,KAAK;AACb,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACrG,IAAI,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACzC,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,KAAK;AACvD,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS;AAC5B,IAAI,OAAO;AACX,EAAE,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC3B,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AACtF,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK;AAC/C,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK;AACxB,IAAI,OAAO;AACX,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACvB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAChE,GAAG,MAAM;AACT,IAAI,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACjC,GAAG;AACH,EAAE;AACK,SAAS,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,EAAE;AACnD,EAAE,IAAI,CAAC,KAAK;AACZ,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AAChD,IAAI,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;AACpC,GAAG,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC9B,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,CAAC,KAAK,EAAE,0CAA0C,CAAC,CAAC;AAC/D;;;;"}