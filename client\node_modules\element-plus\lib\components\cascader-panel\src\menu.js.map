{"version": 3, "file": "menu.js", "sources": ["../../../../../../packages/components/cascader-panel/src/menu.vue"], "sourcesContent": ["<template>\n  <el-scrollbar\n    :key=\"menuId\"\n    tag=\"ul\"\n    role=\"menu\"\n    :class=\"ns.b()\"\n    :wrap-class=\"ns.e('wrap')\"\n    :view-class=\"[ns.e('list'), ns.is('empty', isEmpty)]\"\n    @mousemove=\"handleMouseMove\"\n    @mouseleave=\"clearHoverZone\"\n  >\n    <el-cascader-node\n      v-for=\"node in nodes\"\n      :key=\"node.uid\"\n      :node=\"node\"\n      :menu-id=\"menuId\"\n      @expand=\"handleExpand\"\n    />\n    <div v-if=\"isLoading\" :class=\"ns.e('empty-text')\">\n      <el-icon size=\"14\" :class=\"ns.is('loading')\">\n        <loading />\n      </el-icon>\n      {{ t('el.cascader.loading') }}\n    </div>\n    <div v-else-if=\"isEmpty\" :class=\"ns.e('empty-text')\">\n      <slot name=\"empty\">{{ t('el.cascader.noData') }}</slot>\n    </div>\n    <!-- eslint-disable-next-line vue/html-self-closing -->\n    <svg\n      v-else-if=\"panel?.isHoverMenu\"\n      ref=\"hoverZone\"\n      :class=\"ns.e('hover-zone')\"\n    ></svg>\n  </el-scrollbar>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, getCurrentInstance, inject, ref } from 'vue'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport { useId, useLocale, useNamespace } from '@element-plus/hooks'\nimport { Loading } from '@element-plus/icons-vue'\nimport ElIcon from '@element-plus/components/icon'\nimport ElCascaderNode from './node.vue'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { default as CascaderNode } from './node'\nimport type { PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\n\nexport default defineComponent({\n  name: 'ElCascaderMenu',\n\n  components: {\n    Loading,\n    ElIcon,\n    ElScrollbar,\n    ElCascaderNode,\n  },\n\n  props: {\n    nodes: {\n      type: Array as PropType<CascaderNode[]>,\n      required: true,\n    },\n    index: {\n      type: Number,\n      required: true,\n    },\n  },\n\n  setup(props) {\n    const instance = getCurrentInstance()!\n    const ns = useNamespace('cascader-menu')\n\n    const { t } = useLocale()\n    const id = useId()\n    let activeNode: Nullable<HTMLElement> = null\n    let hoverTimer: Nullable<number> = null\n\n    const panel = inject(CASCADER_PANEL_INJECTION_KEY)!\n\n    const hoverZone = ref<null | SVGSVGElement>(null)\n\n    const isEmpty = computed(() => !props.nodes.length)\n    const isLoading = computed(() => !panel.initialLoaded)\n    const menuId = computed(() => `${id.value}-${props.index}`)\n\n    const handleExpand = (e: MouseEvent) => {\n      activeNode = e.target as HTMLElement\n    }\n\n    const handleMouseMove = (e: MouseEvent) => {\n      if (!panel.isHoverMenu || !activeNode || !hoverZone.value) return\n\n      if (activeNode.contains(e.target as HTMLElement)) {\n        clearHoverTimer()\n\n        const el = instance.vnode.el as HTMLElement\n        const { left } = el.getBoundingClientRect()\n        const { offsetWidth, offsetHeight } = el\n        const startX = e.clientX - left\n        const top = activeNode.offsetTop\n        const bottom = top + activeNode.offsetHeight\n\n        hoverZone.value.innerHTML = `\n          <path style=\"pointer-events: auto;\" fill=\"transparent\" d=\"M${startX} ${top} L${offsetWidth} 0 V${top} Z\" />\n          <path style=\"pointer-events: auto;\" fill=\"transparent\" d=\"M${startX} ${bottom} L${offsetWidth} ${offsetHeight} V${bottom} Z\" />\n        `\n      } else if (!hoverTimer) {\n        hoverTimer = window.setTimeout(\n          clearHoverZone,\n          panel.config.hoverThreshold\n        )\n      }\n    }\n\n    const clearHoverTimer = () => {\n      if (!hoverTimer) return\n      clearTimeout(hoverTimer)\n      hoverTimer = null\n    }\n\n    const clearHoverZone = () => {\n      if (!hoverZone.value) return\n      hoverZone.value.innerHTML = ''\n      clearHoverTimer()\n    }\n    return {\n      ns,\n      panel,\n      hoverZone,\n      isEmpty,\n      isLoading,\n      menuId,\n      t,\n      handleExpand,\n      handleMouseMove,\n      clearHoverZone,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "Loading", "ElIcon", "ElScrollbar", "ElCascaderNode", "getCurrentInstance", "useNamespace", "useLocale", "useId", "inject", "CASCADER_PANEL_INJECTION_KEY", "ref", "computed", "_createBlock", "_normalizeClass", "_openBlock", "_createElementBlock", "_Fragment", "_renderList", "_createVNode", "_withCtx", "_createTextVNode", "_toDisplayString", "_createCommentVNode", "_export_sfc"], "mappings": ";;;;;;;;;;;;;;;AAiDA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,gBAAA;AAAA,EAEN,UAAY,EAAA;AAAA,aACVC,gBAAA;AAAA,YACAC,YAAA;AAAA,iBACAC,mBAAA;AAAA,oBACAC,eAAA;AAAA,GACF;AAAA,EAEA,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,KAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,GACF;AAAA,EAEA,MAAM,KAAO,EAAA;AACX,IAAA,MAAM,WAAWC,sBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,EAAA,GAAKC,qBAAa,eAAe,CAAA,CAAA;AAEvC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIC,iBAAU,EAAA,CAAA;AACxB,IAAA,MAAM,KAAKC,aAAM,EAAA,CAAA;AACjB,IAAA,IAAI,UAAoC,GAAA,IAAA,CAAA;AACxC,IAAA,IAAI,UAA+B,GAAA,IAAA,CAAA;AAEnC,IAAM,MAAA,KAAA,GAAQC,WAAOC,kCAA4B,CAAA,CAAA;AAEjD,IAAM,MAAA,SAAA,GAAYC,QAA0B,IAAI,CAAA,CAAA;AAEhD,IAAA,MAAM,UAAUC,YAAS,CAAA,MAAM,CAAC,KAAA,CAAM,MAAM,MAAM,CAAA,CAAA;AAClD,IAAA,MAAM,SAAY,GAAAA,YAAA,CAAS,MAAM,CAAC,MAAM,aAAa,CAAA,CAAA;AACrD,IAAM,MAAA,MAAA,GAASA,aAAS,MAAM,CAAA,EAAG,GAAG,KAAK,CAAA,CAAA,EAAI,KAAM,CAAA,KAAK,CAAE,CAAA,CAAA,CAAA;AAE1D,IAAM,MAAA,YAAA,GAAe,CAAC,CAAkB,KAAA;AACtC,MAAA,UAAA,GAAa,CAAE,CAAA,MAAA,CAAA;AAAA,KACjB,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,CAAkB,KAAA;AACzC,MAAA,IAAI,CAAC,KAAM,CAAA,WAAA,IAAe,CAAC,UAAc,IAAA,CAAC,UAAU,KAAO;AAE3D,QAAA,OAAe;AACb,MAAgB,IAAA,UAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA;AAEhB,QAAM;AACN,QAAA,MAAM,EAAE,GAAA,QAAS,CAAG,KAAsB,CAAA,EAAA,CAAA;AAC1C,QAAM,MAAA,EAAE,IAAa,EAAA,GAAA,EAAA,CAAA,qBAAiB,EAAA,CAAA;AACtC,QAAM,MAAA,EAAA,WAAqB,EAAA,YAAA,EAAA,GAAA,EAAA,CAAA;AAC3B,QAAA,MAAM,MAAM,GAAW,CAAA,CAAA,OAAA,GAAA,IAAA,CAAA;AACvB,QAAM,MAAA,GAAA,GAAA,UAA0B,CAAA,SAAA,CAAA;AAEhC,QAAA,MAAA,YAA4B,GAAA,UAAA,CAAA,YAAA,CAAA;AAAA,QAAA,SAAA,CAAA,KAAA,CAAA,SAAA,GAAA,CAAA;AAC0E,qEACvC,EAAA,MAAM,IAAI,GAAM,CAAA,EAAA,EAAA,gBAAoB,EAAA,GAAA,CAAA;AAAuB,qEAAA,EAAA,MAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,EAAA,WAAA,CAAA,CAAA,EAAA,YAAA,CAAA,EAAA,EAAA,MAAA,CAAA;AAAA,QAE5H,CAAA,CAAA;AACE,OAAA,MAAA,IAAA,CAAA,UAAoB,EAAA;AAAA,QAClB,UAAA,GAAA,MAAA,CAAA,UAAA,CAAA,cAAA,EAAA,KAAA,CAAA,MAAA,CAAA,cAAA,CAAA,CAAA;AAAA,OAAA;AACa,KACf,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,MAAA;AAAA,MACF,IAAA,CAAA,UAAA;AAEA,QAAA;AACE,MAAA,YAAiB,CAAA,UAAA,CAAA,CAAA;AACjB,MAAA,UAAA,GAAa,IAAU,CAAA;AACvB,KAAa,CAAA;AAAA,IACf,MAAA,cAAA,GAAA,MAAA;AAEA,MAAA,IAAM;AACJ,QAAI;AACJ,MAAA,SAAA,CAAU,MAAM,SAAY,GAAA,EAAA,CAAA;AAC5B,MAAgB,eAAA,EAAA,CAAA;AAAA,KAClB,CAAA;AACA,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MACA,KAAA;AAAA,MACA,SAAA;AAAA,MACA,OAAA;AAAA,MACA,SAAA;AAAA,MACA,MAAA;AAAA,MACA,CAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;;;;0BA3ICC,eAgCe,CAAA,uBAAA,EAAA;AAAA,IA/BZ,GAAK,EAAA,IAAA,CAAA,MAAA;AAAA,IACN,GAAI,EAAA,IAAA;AAAA,IACJ,IAAK,EAAA,MAAA;AAAA,IACJ,KAAA,EAAKC,kBAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,EAAA,CAAA;AAAA,IACX,YAAA,EAAY,QAAG,CAAC,CAAA,MAAA,CAAA;AAAA,IAChB,YAAA,EAAU,CAAG,IAAG,CAAA,EAAA,CAAA,CAAA,CAAC,SAAU,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,SAAU,IAAO,CAAA,OAAA,CAAA,CAAA;AAAA,IACjD,WAAW,EAAA,IAAA,CAAA,eAAA;AAAA,IACX,YAAY,EAAA,IAAA,CAAA,cAAA;AAAA,GAAA,EAAA;yBAGX,MAAqB;AAAA,MADvB,IAAA,EAAA,CAAA;AAAA,MAME,OAAA;AAAA,SAAAC,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAC,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,KAAA;AAAA,UALe,OAAAH,aAAA,EAAA,EAAAF,eAAJ,CAAA,2BAAA,EAAA;;AAKX,YAJC;AAAU,YACV,SAAA,EAAA,IAAA,CAAA,MAAA;AAAA,YACA,QAAS,EAAA,IAAA,CAAA,YAAA;AAAA,WAAA,EACD,IAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA,CAAA;AAAA,SAAA,CAAA,EAAA,GAAA,CAAA;;;;;UAEXM,eAAA,CAAA,kBAAA,EAAA;AAAA,YAKM,IAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAAL,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAAA,WAAA,EAAA;YALsB,OAAA,EAAAM,WAAA,CAAA;AAAM,cAAAD,eAAA,CAAA,kBAAA,CAAA;;YAGtB,CAAA,EAAA,CAAA;AAAA,WAAA,EAFI,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,UAAME,mBAAO,CAAA,GAAA,GAAAC,mBAAK,CAAA,IAAA,CAAA,CAAA,CAAA,qBAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,SAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,OAAA,IAAAP,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;;AACnB,UAAA,KAAA,EAAAF,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;AAAA,SAAA,EAAA;;;;;AACH,UAAAS,yEACN,CAAA;AAAA,WAAAR,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,YAAA,GAAA,EAAA,WAAA;AAAA,YAAA,KAAA,EAAAF,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;AAAA,WAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;;;AAEN,IAEM,CAAA,EAAA,CAAA;AAAA,GAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,aAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AAF+B,qBAAA,gBAAAU,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}