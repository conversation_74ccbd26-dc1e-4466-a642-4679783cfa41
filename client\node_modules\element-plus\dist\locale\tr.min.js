/*! Element Plus v2.10.2 */(function(e,a){typeof exports=="object"&&typeof module!="undefined"?module.exports=a():typeof define=="function"&&define.amd?define(a):(e=typeof globalThis!="undefined"?globalThis:e||self,e.ElementPlusLocaleTr=a())})(this,function(){"use strict";var e={name:"tr",el:{breadcrumb:{label:"Breadcrumb"},colorpicker:{confirm:"Onayla",clear:"Temizle"},datepicker:{now:"\u015Eimdi",today:"Bug\xFCn",cancel:"\u0130ptal",clear:"Temizle",confirm:"Onayla",selectDate:"Tarih se\xE7",selectTime:"Saat se\xE7",startDate:"Ba\u015Flang\u0131\xE7 Tarihi",startTime:"Ba\u015Flang\u0131\xE7 Saati",endDate:"Biti\u015F Tarihi",endTime:"Biti\u015F Saati",prevYear:"\xD6nceki Y\u0131l",nextYear:"Sonraki Y\u0131l",prevMonth:"\xD6nceki Ay",nextMonth:"Sonraki Ay",year:"",month1:"Ocak",month2:"\u015Eubat",month3:"Mart",month4:"Nisan",month5:"May\u0131s",month6:"Haziran",month7:"Temmuz",month8:"A\u011Fustos",month9:"Eyl\xFCl",month10:"Ekim",month11:"Kas\u0131m",month12:"Aral\u0131k",weeks:{sun:"Paz",mon:"Pzt",tue:"Sal",wed:"\xC7ar",thu:"Per",fri:"Cum",sat:"Cmt"},months:{jan:"Oca",feb:"\u015Eub",mar:"Mar",apr:"Nis",may:"May",jun:"Haz",jul:"Tem",aug:"A\u011Fu",sep:"Eyl",oct:"Eki",nov:"Kas",dec:"Ara"}},select:{loading:"Y\xFCkleniyor",noMatch:"E\u015Fle\u015Fen veri bulunamad\u0131",noData:"Veri yok",placeholder:"Se\xE7"},mention:{loading:"Y\xFCkleniyor"},cascader:{noMatch:"E\u015Fle\u015Fen veri bulunamad\u0131",loading:"Y\xFCkleniyor",placeholder:"Se\xE7",noData:"Veri yok"},pagination:{goto:"Git",pagesize:"/sayfa",total:"Toplam {total}",pageClassifier:"",page:"Page",prev:"Go to previous page",next:"Go to next page",currentPage:"page {pager}",prevPages:"Previous {pager} pages",nextPages:"Next {pager} pages"},messagebox:{title:"Mesaj",confirm:"Onayla",cancel:"\u0130ptal",error:"\u0130llegal giri\u015F"},upload:{deleteTip:"kald\u0131rmak i\xE7in delete tu\u015Funa bas",delete:"Sil",preview:"G\xF6r\xFCnt\xFCle",continue:"Devam"},table:{emptyText:"Veri yok",confirmFilter:"Onayla",resetFilter:"S\u0131f\u0131rla",clearFilter:"Hepsi",sumText:"Sum"},tree:{emptyText:"Veri yok"},transfer:{noMatch:"E\u015Fle\u015Fen veri bulunamad\u0131",noData:"Veri yok",titles:["Liste 1","Liste 2"],filterPlaceholder:"Anahtar kelimeleri gir",noCheckedFormat:"{total} adet",hasCheckedFormat:"{checked}/{total} se\xE7ildi"},image:{error:"BA\u015EARISIZ OLDU"},pageHeader:{title:"Geri"},popconfirm:{confirmButtonText:"Evet",cancelButtonText:"Hay\u0131r"},carousel:{leftArrow:"Carousel arrow left",rightArrow:"Carousel arrow right",indicator:"Carousel switch to index {index}"}}};return e});
//# sourceMappingURL=tr.min.js.map
