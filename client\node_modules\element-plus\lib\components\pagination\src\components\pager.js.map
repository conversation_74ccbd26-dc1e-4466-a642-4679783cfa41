{"version": 3, "file": "pager.js", "sources": ["../../../../../../../packages/components/pagination/src/components/pager.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Pager from './pager.vue'\n\nexport const paginationPagerProps = buildProps({\n  currentPage: {\n    type: Number,\n    default: 1,\n  },\n  pageCount: {\n    type: Number,\n    required: true,\n  },\n  pagerCount: {\n    type: Number,\n    default: 7,\n  },\n  disabled: Boolean,\n} as const)\n\nexport type PaginationPagerProps = ExtractPropTypes<typeof paginationPagerProps>\n\nexport type PagerInstance = InstanceType<typeof Pager> & unknown\n"], "names": ["buildProps"], "mappings": ";;;;;;AACY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;"}