{"version": 3, "file": "breadcrumb2.mjs", "sources": ["../../../../../../packages/components/breadcrumb/src/breadcrumb.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"breadcrumb\"\n    :class=\"ns.b()\"\n    :aria-label=\"t('el.breadcrumb.label')\"\n    role=\"navigation\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { onMounted, provide, ref } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { breadcrumbKey } from './constants'\nimport { breadcrumbProps } from './breadcrumb'\n\ndefineOptions({\n  name: 'ElBreadcrumb',\n})\n\nconst { t } = useLocale()\nconst props = defineProps(breadcrumbProps)\n\nconst ns = useNamespace('breadcrumb')\nconst breadcrumb = ref<HTMLDivElement>()\n\nprovide(breadcrumbKey, props)\n\nonMounted(() => {\n  const items = breadcrumb.value!.querySelectorAll(`.${ns.e('item')}`)\n  if (items.length) {\n    items[items.length - 1].setAttribute('aria-current', 'page')\n  }\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;mCAiBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AAGxB,IAAM,MAAA,EAAA,GAAK,aAAa,YAAY,CAAA,CAAA;AACpC,IAAA,MAAM,aAAa,GAAoB,EAAA,CAAA;AAEvC,IAAA,OAAA,CAAQ,eAAe,KAAK,CAAA,CAAA;AAE5B,IAAA,SAAA,CAAU,MAAM;AACd,MAAM,MAAA,KAAA,GAAQ,WAAW,KAAO,CAAA,gBAAA,CAAiB,IAAI,EAAG,CAAA,CAAA,CAAE,MAAM,CAAC,CAAE,CAAA,CAAA,CAAA;AACnE,MAAA,IAAI,MAAM,MAAQ,EAAA;AAChB,QAAA,KAAA,CAAM,MAAM,MAAS,GAAA,CAAC,CAAE,CAAA,YAAA,CAAa,gBAAgB,MAAM,CAAA,CAAA;AAAA,OAC7D;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;"}