{"version": 3, "file": "tokens.js", "sources": ["../../../../../../packages/components/roving-focus-group/src/tokens.ts"], "sourcesContent": ["import type { InjectionKey, Ref, StyleValue } from 'vue'\nimport type { ElRovingFocusGroupProps } from './roving-focus-group'\n\ntype EventHandler<T = Event> = (e: T) => void\n\nexport type RovingGroupInjectionContext = {\n  currentTabbedId: Ref<string | null>\n  dir: Ref<ElRovingFocusGroupProps['dir']>\n  loop: Ref<ElRovingFocusGroupProps['loop']>\n  orientation: Ref<ElRovingFocusGroupProps['orientation']>\n  tabIndex: Ref<number>\n  rovingFocusGroupRef: Ref<HTMLElement | undefined>\n  rovingFocusGroupRootStyle: Ref<StyleValue>\n  onBlur: EventHandler\n  onFocus: EventHandler<FocusEvent>\n  onMousedown: EventHandler\n  onItemFocus: (id: string) => void\n  onItemShiftTab: () => void\n}\n\nexport type RovingFocusGroupItemInjectionContext = {\n  rovingFocusGroupItemRef: Ref<HTMLElement | undefined>\n  tabIndex: Ref<number>\n  handleMousedown: EventHandler\n  handleFocus: EventHandler\n  handleKeydown: EventHandler\n}\n\nexport const ROVING_FOCUS_GROUP_INJECTION_KEY: InjectionKey<RovingGroupInjectionContext> =\n  Symbol('elRovingFocusGroup')\n\nexport const ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY: InjectionKey<RovingFocusGroupItemInjectionContext> =\n  Symbol('elRovingFocusGroupItem')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,gCAAgC,GAAG,MAAM,CAAC,oBAAoB,EAAE;AACjE,MAAC,qCAAqC,GAAG,MAAM,CAAC,wBAAwB;;;;;"}