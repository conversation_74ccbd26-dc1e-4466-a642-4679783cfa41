# 驴肉美食店铺后台管理系统

一个基于Vue3和Node.js的现代化后台管理系统，专为驴肉美食店铺设计。

## 技术栈

### 前端
- Vue 3 (Composition API)
- Vue Router 4
- Pinia (状态管理)
- Element Plus (UI组件库)
- Vite (构建工具)
- Axios (HTTP客户端)

### 后端
- Node.js
- Express.js
- JWT (身份认证)
- bcryptjs (密码加密)
- express-validator (数据验证)
- CORS (跨域处理)

## 功能特性

- 🔐 用户认证与授权
- 📱 响应式设计
- 🎨 现代化UI界面
- 🔒 路由守卫
- 📊 数据统计展示
- 🛡️ 安全防护

### 🍽️ 菜品管理
- **菜品列表管理**：支持卡片视图和表格视图切换
- **分类管理**：菜品分类的增删改查
- **菜品状态管理**：上架/下架功能
- **图片上传**：支持菜品图片上传
- **搜索筛选**：按名称、分类、状态筛选菜品

### 📊 数据统计
- **实时统计**：菜品总数、上架数量、分类数量等
- **可视化图表**：销售趋势、用户活跃度等
- **品牌分析**：各品牌销售数据对比

### 🛒 订单管理
- **订单列表**：完整的订单信息展示
- **订单状态**：待付款、已付款、已发货、已完成等
- **订单详情**：详细的订单信息和商品列表
- **批量操作**：支持批量处理订单

### 📦 库存管理
- **库存监控**：实时库存数量显示
- **库存预警**：低库存提醒
- **仓库管理**：多仓库库存管理

### 👥 客户管理
- **客户信息**：客户基本信息和联系方式
- **客户分类**：客户等级管理
- **订单历史**：客户购买记录

## 快速开始

### 1. 安装依赖

```bash
# 安装所有依赖（包括前端和后端）
npm run install-all
```

### 2. 启动开发服务器

```bash
# 同时启动前端和后端服务
npm run dev
```

### 3. 访问应用

- 前端地址: http://localhost:3000
- 后端API: http://localhost:3001

## 默认登录信息

- 用户名: `admin`
- 密码: `password`

## 项目结构

```
驴肉美食/
├── client/                 # 前端Vue3应用
│   ├── src/
│   │   ├── views/         # 页面组件
│   │   │   ├── Dashboard.vue          # 主面板
│   │   │   ├── DashboardHome.vue      # 首页概览
│   │   │   ├── DishesManage.vue       # 菜品列表管理
│   │   │   ├── CategoriesManage.vue   # 分类管理
│   │   │   ├── OrderManage.vue        # 订单管理
│   │   │   ├── ProductManage.vue      # 产品管理
│   │   │   ├── StockManage.vue        # 库存管理
│   │   │   ├── CustomerManage.vue     # 客户管理
│   │   │   └── Login.vue              # 登录页面
│   │   ├── stores/        # Pinia状态管理
│   │   ├── router/        # 路由配置
│   │   └── main.js        # 应用入口
│   ├── index.html         # HTML模板
│   ├── vite.config.js     # Vite配置
│   └── package.json       # 前端依赖
├── server/                # 后端Node.js应用
│   ├── index.js          # 服务器入口
│   └── package.json      # 后端依赖
├── package.json          # 根目录配置
└── README.md            # 项目说明
```

## API接口

### 认证相关
- `POST /api/login` - 用户登录
- `GET /api/verify` - 验证Token
- `GET /api/user` - 获取用户信息

### 健康检查
- `GET /api/health` - 服务器健康状态

## 开发说明

### 前端开发
- 使用Vue 3 Composition API
- 状态管理使用Pinia
- UI组件库使用Element Plus
- 路由使用Vue Router 4

### 后端开发
- 使用Express.js框架
- JWT进行身份认证
- bcryptjs加密密码
- express-validator验证数据

## 部署说明

### 生产环境构建

```bash
# 构建前端
cd client && npm run build

# 启动后端服务
cd server && npm start
```

## 注意事项

1. 确保Node.js版本 >= 16
2. 后端服务默认运行在3001端口
3. 前端服务默认运行在3000端口
4. 生产环境请修改JWT密钥

## 许可证

MIT License 