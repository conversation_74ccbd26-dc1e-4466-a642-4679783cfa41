'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var SortOrder = /* @__PURE__ */ ((SortOrder2) => {
  SortOrder2["ASC"] = "asc";
  SortOrder2["DESC"] = "desc";
  return SortOrder2;
})(SortOrder || {});
var Alignment = /* @__PURE__ */ ((Alignment2) => {
  Alignment2["CENTER"] = "center";
  Alignment2["RIGHT"] = "right";
  return Alignment2;
})(Alignment || {});
var FixedDir = /* @__PURE__ */ ((FixedDir2) => {
  FixedDir2["LEFT"] = "left";
  FixedDir2["RIGHT"] = "right";
  return FixedDir2;
})(FixedDir || {});
const oppositeOrderMap = {
  ["asc" /* ASC */]: "desc" /* DESC */,
  ["desc" /* DESC */]: "asc" /* ASC */
};
const sortOrders = ["asc" /* ASC */, "desc" /* DESC */];

exports.Alignment = Alignment;
exports.FixedDir = FixedDir;
exports.SortOrder = SortOrder;
exports.oppositeOrderMap = oppositeOrderMap;
exports.sortOrders = sortOrders;
//# sourceMappingURL=constants.js.map
