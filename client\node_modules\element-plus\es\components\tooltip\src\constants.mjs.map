{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/tooltip/src/constants.ts"], "sourcesContent": ["import type { Arrayable } from '@element-plus/utils'\nimport type { Injection<PERSON>ey, Ref } from 'vue'\nimport type { TooltipTriggerType } from './trigger'\n\nexport type ElTooltipInjectionContext = {\n  controlled: Ref<boolean>\n  id: Ref<string>\n  open: Ref<boolean>\n  trigger: Ref<Arrayable<TooltipTriggerType>>\n  onOpen: (e?: Event) => void\n  onClose: (e?: Event) => void\n  onToggle: (e: Event) => void\n  onShow: () => void\n  onHide: () => void\n  onBeforeShow: () => void\n  onBeforeHide: () => void\n  updatePopper: () => void\n}\n\nexport const TOOLTIP_INJECTION_KEY: InjectionKey<ElTooltipInjectionContext> =\n  Symbol('elTooltip')\n"], "names": [], "mappings": "AAAY,MAAC,qBAAqB,GAAG,MAAM,CAAC,WAAW;;;;"}