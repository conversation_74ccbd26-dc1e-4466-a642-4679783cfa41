@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(select-dropdown) {
  z-index: calc(#{getCssVar('index-top')} + 1);
  border-radius: getCssVar('border-radius-base');
  box-sizing: border-box;

  .#{$namespace}-scrollbar.is-empty .#{$namespace}-select-dropdown__list {
    padding: 0;
  }
}

@include b(select-dropdown__loading) {
  padding: map.get($select-dropdown, 'empty-padding');
  margin: 0;
  text-align: center;
  color: map.get($select-dropdown, 'empty-color');
  font-size: getCssVar('select-font-size');
}

@include b(select-dropdown__empty) {
  padding: map.get($select-dropdown, 'empty-padding');
  margin: 0;
  text-align: center;
  color: map.get($select-dropdown, 'empty-color');
  font-size: getCssVar('select-font-size');
}

@include b(select-dropdown__wrap) {
  max-height: map.get($select-dropdown, 'max-height');
}

@include b(select-dropdown__list) {
  list-style: none;
  padding: map.get($select-dropdown, 'padding');
  margin: 0;
  box-sizing: border-box;

  &.#{$namespace}-vl__window {
    // for select-v2
    margin: map.get($select-dropdown, 'padding');
    padding: 0;
  }
}

@include b(select-dropdown__header) {
  padding: map.get($select-dropdown, 'header-padding');
  border-bottom: map.get($select-dropdown, 'border');
}

@include b(select-dropdown__footer) {
  padding: map.get($select-dropdown, 'footer-padding');
  border-top: map.get($select-dropdown, 'border');
}
