{"version": 3, "file": "sw.mjs", "sources": ["../../../../../packages/locale/lang/sw.ts"], "sourcesContent": ["export default {\n  name: 'sw',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'sawa',\n      clear: 'futa',\n      defaultLabel: 'kichagua rangi',\n      description:\n        'rangi ya sasa ni {color}. bonyeza kitufe cha kuingia ili kuchagua rangi mpya.',\n    },\n    datepicker: {\n      now: 'sasa',\n      today: 'leo',\n      cancel: 'kufuta',\n      clear: 'futa',\n      confirm: 'sawa',\n      dateTablePrompt:\n        'Tumia vitufe vya vishale na uweke ili kuchagua siku katika mwezi',\n      monthTablePrompt:\n        'Tumia vitufe vya vishale na uingize ili kuchagua mwezi',\n      yearTablePrompt: 'Tumia vitufe vya vishale na uingize ili kuchagua mwaka',\n      selectedDate: 'tarehe iliyochaguliwa',\n      selectDate: 'chagua tarehe',\n      selectTime: 'chagua muda',\n      startDate: 'siku ya kuanza',\n      startTime: 'muda wa kuanza',\n      endDate: 'tarehe ya mwisho',\n      endTime: 'wakati wa mwisho',\n      prevYear: 'mwaka uliopita',\n      nextYear: 'mwaka ujao',\n      prevMonth: 'mwezi uliopita',\n      nextMonth: 'mwezi ujao',\n      year: '',\n      month1: 'mwezi wa kwanza',\n      month2: 'mwezi wa pili',\n      month3: 'mwezi tatu',\n      month4: 'mwezi wa nne',\n      month5: 'Mwezi wa tano',\n      month6: 'mwezi wa sita',\n      month7: 'mwezi wa saba',\n      month8: 'mwezi wa nane',\n      month9: 'mwezi wa tisa',\n      month10: 'mwezi wa kumi',\n      month11: 'mwezi wa kumi na moja',\n      month12: 'mwezi wa kumi na mbili',\n      week: 'siku saba',\n      weeks: {\n        sun: 'jpili',\n        mon: 'jtatu',\n        tue: 'jnne',\n        wed: 'jtano',\n        thu: 'alh',\n        fri: 'jumaa',\n        sat: 'jmosi',\n      },\n      weeksFull: {\n        sun: 'jumapili',\n        mon: 'jumatatu',\n        tue: 'jumanne',\n        wed: 'jumatano',\n        thu: 'alhamisi',\n        fri: 'ijumaaa',\n        sat: 'jumamosi',\n      },\n      months: {\n        jan: 'mwezi 1',\n        feb: 'mwezi 2',\n        mar: 'mwezi 3',\n        apr: 'mwezi 4',\n        may: 'mwezi 5',\n        jun: 'mwezi 6',\n        jul: 'mwezi 7',\n        aug: 'mwezi 8',\n        sep: 'mwezi 9',\n        oct: 'mwezi 10',\n        nov: 'mwezi 11',\n        dec: 'mwezi 12',\n      },\n    },\n    inputNumber: {\n      decrease: 'kupunguza idadi',\n      increase: 'kuongeza idadi',\n    },\n    select: {\n      loading: 'inapakia',\n      noMatch: 'hakuna data inayolingana',\n      noData: 'hakuna data',\n      placeholder: 'chagua',\n    },\n    mention: {\n      loading: 'inapakia',\n    },\n    dropdown: {\n      toggleDropdown: 'geuza kunyuzi',\n    },\n    cascader: {\n      noMatch: 'hakuna data inayolingana',\n      loading: 'pakia',\n      placeholder: 'chagua',\n      noData: 'hakuna data',\n    },\n    pagination: {\n      goto: 'enda kwenye',\n      pagesize: '/kurasa',\n      total: 'jumla {total}',\n      pageClassifier: '',\n      page: 'kurasa',\n      prev: 'Nenda kwenye ukurasa uliopita',\n      next: 'Nenda kwenye ukurasa unaofuata',\n      currentPage: 'kurasa {pager}',\n      prevPages: 'Kurasa za {pager} zilizopita',\n      nextPages: 'Kurasa {pager} zinazofuata',\n      deprecationWarning:\n        'Matumizi yaliyoacha kutumika yamegunduliwa, tafadhali rejelea hati za el-pagination kwa maelezo zaidi',\n    },\n    dialog: {\n      close: 'funga kidirisha hiki',\n    },\n    drawer: {\n      close: 'funga kidirisha hiki',\n    },\n    messagebox: {\n      title: 'ujumbe',\n      confirm: 'sawa',\n      cancel: 'futa',\n      error: 'Uingizaji haramu',\n      close: 'Funga kidirisha hiki',\n    },\n    upload: {\n      deleteTip: 'bonyeza kufuta ili kuondoa',\n      delete: 'kufuta',\n      preview: 'hakikisho',\n      continue: 'endelea',\n    },\n    slider: {\n      defaultLabel: 'kitelelzi kati ya {min} na {max}',\n      defaultRangeStartLabel: 'cahgua thamani ya kuanzia',\n      defaultRangeEndLabel: 'chagua thamani ya mwisho',\n    },\n    table: {\n      emptyText: 'hakuna data',\n      confirmFilter: 'thibitisha',\n      resetFilter: 'weka upya',\n      clearFilter: 'zote',\n      sumText: 'jumla',\n    },\n    tree: {\n      emptyText: 'hakuna data',\n    },\n    transfer: {\n      noMatch: 'hakuna data inayolingana',\n      noData: 'hakuna data',\n      titles: ['orodha ya kwanza', 'orodha ya pili'],\n      filterPlaceholder: 'Ingiza neno kuu',\n      noCheckedFormat: '{total} vitu',\n      hasCheckedFormat: '{checked}/{total} imechaguliwa',\n    },\n    image: {\n      error: 'imeshindwa',\n    },\n    pageHeader: {\n      title: 'nyuma',\n    },\n    popconfirm: {\n      confirmButtonText: 'ndio',\n      cancelButtonText: 'hapana',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,WAAW,EAAE,+EAA+E;AAClG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,eAAe,EAAE,kEAAkE;AACzF,MAAM,gBAAgB,EAAE,wDAAwD;AAChF,MAAM,eAAe,EAAE,wDAAwD;AAC/E,MAAM,YAAY,EAAE,uBAAuB;AAC3C,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,UAAU;AACzB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,eAAe;AACrC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,IAAI,EAAE,+BAA+B;AAC3C,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,kBAAkB,EAAE,uGAAuG;AACjI,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,sBAAsB;AACnC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,sBAAsB;AACnC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,KAAK,EAAE,sBAAsB;AACnC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,SAAS;AACzB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,kCAAkC;AACtD,MAAM,sBAAsB,EAAE,2BAA2B;AACzD,MAAM,oBAAoB,EAAE,0BAA0B;AACtD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,aAAa,EAAE,YAAY;AACjC,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,aAAa;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AACpD,MAAM,iBAAiB,EAAE,iBAAiB;AAC1C,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,gBAAgB,EAAE,gCAAgC;AACxD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,MAAM;AAC/B,MAAM,gBAAgB,EAAE,QAAQ;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}