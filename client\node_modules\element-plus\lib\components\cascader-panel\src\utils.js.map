{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/cascader-panel/src/utils.ts"], "sourcesContent": ["import { isLeaf } from '@element-plus/utils'\nimport type { default as CascaderNode } from './node'\n\nexport const getMenuIndex = (el: HTMLElement) => {\n  if (!el) return 0\n  const pieces = el.id.split('-')\n  return Number(pieces[pieces.length - 2])\n}\n\nexport const checkNode = (el: HTMLElement) => {\n  if (!el) return\n\n  const input = el.querySelector('input')\n  if (input) {\n    input.click()\n  } else if (isLeaf(el)) {\n    el.click()\n  }\n}\n\nexport const sortByOriginalOrder = (\n  oldNodes: CascaderNode[],\n  newNodes: CascaderNode[]\n) => {\n  const newNodesCopy = newNodes.slice(0)\n  const newIds = newNodesCopy.map((node) => node.uid)\n  const res = oldNodes.reduce((acc, item) => {\n    const index = newIds.indexOf(item.uid)\n    if (index > -1) {\n      acc.push(item)\n      newNodesCopy.splice(index, 1)\n      newIds.splice(index, 1)\n    }\n    return acc\n  }, [] as CascaderNode[])\n\n  res.push(...newNodesCopy)\n\n  return res\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AACY,MAAC,YAAY,GAAG,CAAC,EAAE,KAAK;AACpC,EAAE,IAAI,CAAC,EAAE;AACT,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,EAAE;AACU,MAAC,SAAS,GAAG,CAAC,EAAE,KAAK;AACjC,EAAE,IAAI,CAAC,EAAE;AACT,IAAI,OAAO;AACX,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1C,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;AAClB,GAAG,MAAM,IAAIA,WAAM,CAAC,EAAE,CAAC,EAAE;AACzB,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AACf,GAAG;AACH,EAAE;AACU,MAAC,mBAAmB,GAAG,CAAC,QAAQ,EAAE,QAAQ,KAAK;AAC3D,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;AACtD,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AAC7C,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACpB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB,MAAM,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACpC,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;AAC5B,EAAE,OAAO,GAAG,CAAC;AACb;;;;;;"}