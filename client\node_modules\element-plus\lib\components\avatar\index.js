'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var avatar$1 = require('./src/avatar.js');
var avatar = require('./src/avatar2.js');
var install = require('../../utils/vue/install.js');

const ElAvatar = install.withInstall(avatar$1["default"]);

exports.avatarEmits = avatar.avatarEmits;
exports.avatarProps = avatar.avatarProps;
exports.ElAvatar = ElAvatar;
exports["default"] = ElAvatar;
//# sourceMappingURL=index.js.map
