export { AngleAxisOption as <PERSON>leAxisComponentOption, AnimationDelayCallback, AnimationDelayCallbackParam as AnimationDelayCallbackParams, AnimationDurationCallback, AriaOption as AriaComponentOption, AxisPointerOption as AxisPointerComponentOption, BarSeriesOption, BoxplotSeriesOption, BrushOption as BrushComponentOption, CalendarOption as CalendarComponentOption, CandlestickSeriesOption, ContinousVisualMapOption as ContinousVisualMapComponentOption, CustomSeriesOption, CustomSeriesRenderItem, CustomSeriesRenderItemAPI, CustomSeriesRenderItemParams, CustomSeriesRenderItemReturn, DataZoomComponentOption, DatasetOption as DatasetComponentOption, CallbackDataParams as DefaultLabelFormatterCallbackParams, EChartsOption, EffectScatterSeriesOption, FunnelSeriesOption, GaugeSeriesOption, GeoOption as GeoComponentOption, GraphSeriesOption, GraphicComponentLooseOption as <PERSON>ComponentOption, GridOption as GridComponentOption, HeatmapS<PERSON>Option, InsideDataZoomOption as InsideDataZoomComponentOption, LabelFormatterCallback, LabelLayoutOptionCallback, LabelLayoutOptionCallbackParams, LegendComponentOption, LineSeriesOption, LinesSeriesOption, MapSeriesOption, MarkAreaOption as MarkAreaComponentOption, MarkLineOption as MarkLineComponentOption, MarkPointOption as MarkPointComponentOption, ParallelCoordinateSystemOption as ParallelComponentOption, ParallelSeriesOption, PictorialBarSeriesOption, PieSeriesOption, PiecewiseVisualMapOption as PiecewiseVisualMapComponentOption, LegendOption as PlainLegendComponentOption, PolarOption as PolarComponentOption, RadarOption as RadarComponentOption, RadarSeriesOption, RadiusAxisOption as RadiusAxisComponentOption, RegisteredSeriesOption, SankeySeriesOption, ScatterSeriesOption, ScrollableLegendOption as ScrollableLegendComponentOption, SeriesOption, SingleAxisOption as SingleAxisComponentOption, SliderDataZoomOption as SliderDataZoomComponentOption, SunburstSeriesOption, ThemeRiverSeriesOption, TimelineOption as TimelineComponentOption, TitleOption as TitleComponentOption, ToolboxComponentOption, TooltipFormatterCallback as TooltipComponentFormatterCallback, TopLevelFormatterParams as TooltipComponentFormatterCallbackParams, TooltipOption as TooltipComponentOption, TooltipPositionCallback as TooltipComponentPositionCallback, TooltipPositionCallbackParams as TooltipComponentPositionCallbackParams, TreeSeriesOption, TreemapSeriesOption, VisualMapComponentOption, XAXisOption as XAXisComponentOption, YAXisOption as YAXisComponentOption } from './shared';