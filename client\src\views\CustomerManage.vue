<template>
  <div class="page-container">
    <el-card>
      <div class="toolbar">
        <el-input v-model="search" placeholder="搜索客户名称" style="width: 200px; margin-right: 10px;" clearable />
        <el-button type="primary" @click="openAddDialog">添加客户</el-button>
      </div>
      <el-table :data="filteredCustomers" style="width: 100%; margin-top: 16px;" border>
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="name" label="客户名称" />
        <el-table-column prop="contact" label="联系方式" />
        <el-table-column prop="address" label="地址" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="removeCustomer(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="editMode ? '编辑客户' : '添加客户'" v-model="dialogVisible">
      <el-form :model="form" label-width="80px">
        <el-form-item label="客户名称">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input v-model="form.contact" />
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="form.address" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCustomer">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const search = ref('')
const dialogVisible = ref(false)
const editMode = ref(false)
const form = ref({ id: null, name: '', contact: '', address: '' })
const customers = ref([
  { id: 1, name: '张三', contact: '13800000001', address: '北京市朝阳区' },
  { id: 2, name: '李四', contact: '13800000002', address: '上海市浦东新区' },
  { id: 3, name: '王五', contact: '13800000003', address: '广州市天河区' }
])
const filteredCustomers = computed(() => {
  if (!search.value) return customers.value
  return customers.value.filter(c => c.name.includes(search.value))
})
function openAddDialog() {
  editMode.value = false
  form.value = { id: null, name: '', contact: '', address: '' }
  dialogVisible.value = true
}
function openEditDialog(row) {
  editMode.value = true
  form.value = { ...row }
  dialogVisible.value = true
}
function saveCustomer() {
  if (!form.value.name) return ElMessage.error('请输入客户名称')
  if (editMode.value) {
    const idx = customers.value.findIndex(c => c.id === form.value.id)
    if (idx !== -1) customers.value[idx] = { ...form.value }
    ElMessage.success('编辑成功')
  } else {
    form.value.id = Date.now()
    customers.value.push({ ...form.value })
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
}
function removeCustomer(id) {
  customers.value = customers.value.filter(c => c.id !== id)
  ElMessage.success('删除成功')
}
</script>
<style scoped>
.page-container { padding: 18px; }
.toolbar { margin-bottom: 10px; display: flex; align-items: center; }
</style> 