@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(skeleton) {
  @include css-var-from-global(('skeleton', 'circle-size'), ('avatar', 'size'));
}

@mixin circle-size($size) {
  width: $size;
  height: $size;
  line-height: $size;
}

@include b(skeleton) {
  @include e(item) {
    background: getCssVar('skeleton-color');
    display: inline-block;
    height: 16px;
    border-radius: getCssVar('border-radius-base');
    width: 100%;
  }

  @include e(circle) {
    border-radius: 50%;

    width: getCssVar('skeleton-circle-size');
    height: getCssVar('skeleton-circle-size');
    line-height: getCssVar('skeleton-circle-size');
  }

  @include e(button) {
    height: 40px;
    width: 64px;
    border-radius: 4px;
  }

  @include e(p) {
    width: 100%;
    @include when(last) {
      width: 61%;
    }

    @include when(first) {
      width: 33%;
    }
  }

  @include e(text) {
    width: 100%;
    height: getCssVar('font-size-small');
  }

  @include e(caption) {
    height: getCssVar('font-size-extra-small');
  }

  @include e(h1) {
    height: getCssVar('font-size-extra-large');
  }

  @include e(h3) {
    height: getCssVar('font-size-large');
  }

  @include e(h5) {
    height: getCssVar('font-size-medium');
  }

  @include e(image) {
    width: unset;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0;

    svg {
      color: getCssVar('svg-monochrome-grey');
      fill: currentColor;
      width: 22%;
      height: 22%;
    }
  }
}
