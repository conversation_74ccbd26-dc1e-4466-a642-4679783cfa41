{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/checkbox/src/constants.ts"], "sourcesContent": ["import type { InjectionKey, ToRefs, WritableComputedRef } from 'vue'\nimport type { CheckboxGroupProps } from './checkbox-group'\n\ntype CheckboxGroupContext = {\n  modelValue?: WritableComputedRef<any>\n  changeEvent?: (...args: any) => any\n} & ToRefs<\n  Pick<\n    CheckboxGroupProps,\n    'size' | 'min' | 'max' | 'disabled' | 'validateEvent' | 'fill' | 'textColor'\n  >\n>\n\nexport const checkboxGroupContextKey: InjectionKey<CheckboxGroupContext> =\n  Symbol('checkboxGroupContextKey')\n"], "names": [], "mappings": "AAAY,MAAC,uBAAuB,GAAG,MAAM,CAAC,yBAAyB;;;;"}