{"version": 3, "file": "ms.min.js", "sources": ["../../../../packages/locale/lang/ms.ts"], "sourcesContent": ["export default {\n  name: 'ms',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb',\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON>',\n      defaultLabel: 'pemilih warna',\n      description:\n        'warna semasa ialah {warna}. tekan enter untuk memilih warna baharu.',\n      alphaLabel: 'pilih nilai alfa',\n    },\n    datepicker: {\n      now: '<PERSON>karang',\n      today: 'Hari ini',\n      cancel: '<PERSON><PERSON>',\n      clear: 'Pa<PERSON>',\n      confirm: 'OK',\n      dateTablePrompt:\n        'Gunakan kekunci anak panah dan masukkan untuk memilih hari dalam bulan tersebut',\n      monthTablePrompt:\n        'Gunakan kekunci anak panah dan masukkan untuk memilih bulan',\n      yearTablePrompt:\n        'Gunakan kekunci anak panah dan masukkan untuk memilih tahun',\n      selectedDate: 'Tarikh yang dipilih',\n      selectDate: 'Pilih tarikh',\n      selectTime: '<PERSON><PERSON>h masa',\n      startDate: '<PERSON><PERSON><PERSON>',\n      startTime: 'Ma<PERSON>',\n      endDate: '<PERSON><PERSON><PERSON>',\n      endTime: 'Masa <PERSON>',\n      prevYear: '<PERSON>hun <PERSON>bel<PERSON>',\n      nextYear: 'Tahun Depan',\n      prevMonth: 'Bulan Sebelumnya',\n      nextMonth: 'Bulan Depan',\n      year: '',\n      month1: 'Januari',\n      month2: 'Februari',\n      month3: 'Mac',\n      month4: 'April',\n      month5: 'Mei',\n      month6: 'Jun',\n      month7: 'Julai',\n      month8: 'Ogos',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Disember',\n      week: 'minggu',\n      weeks: {\n        sun: 'Ahd',\n        mon: 'Isn',\n        tue: 'Sel',\n        wed: 'Rab',\n        thu: 'Kha',\n        fri: 'Jum',\n        sat: 'Sab',\n      },\n      weeksFull: {\n        sun: 'Ahad',\n        mon: 'Isnin',\n        tue: 'Selasa',\n        wed: 'Rabu',\n        thu: 'Khamis',\n        fri: 'Jumaat',\n        sat: 'Sabtu',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mac',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dis',\n      },\n    },\n    inputNumber: {\n      decrease: 'mengurangkan',\n      increase: 'meningkatkan',\n    },\n    select: {\n      loading: 'Memuatkan',\n      noMatch: 'Tiada data yang sepadan',\n      noData: 'Tiada data',\n      placeholder: 'Pilih',\n    },\n    mention: {\n      loading: 'Memuatkan',\n    },\n    dropdown: {\n      toggleDropdown: 'Togol Dropdown',\n    },\n    cascader: {\n      noMatch: 'Tiada data yang sepadan',\n      loading: 'Memuatkan',\n      placeholder: 'Pilih',\n      noData: 'Tiada data',\n    },\n    pagination: {\n      goto: 'Pergi ke',\n      pagesize: '/halaman',\n      total: 'Jumlah {total}',\n      pageClassifier: '',\n      page: 'Halaman',\n      prev: 'Halaman sebelumnya',\n      next: 'Halaman seterusnya',\n      currentPage: 'halaman {pager}',\n      prevPages: 'Halaman {pager} sebelumnya',\n      nextPages: 'Halaman {pager} seterusnya',\n      deprecationWarning:\n        'Penggunaan yang ditamatkan dikesan, sila rujuk dokumentasi el-pagination untuk butiran lanjut',\n    },\n    dialog: {\n      close: 'Tutup dialog ini',\n    },\n    drawer: {\n      close: 'Tutup dialog ini',\n    },\n    messagebox: {\n      title: 'Mesej',\n      confirm: 'OK',\n      Batal: 'Dibatalkan',\n      error: 'Input haram',\n      close: 'Tutup dialog ini',\n    },\n    upload: {\n      deleteTip: 'tekan padam untuk mengalih keluar',\n      delete: 'Padam',\n      preview: 'Pratonton',\n      continue: 'Teruskan',\n    },\n    slider: {\n      defaultLabel: 'peluncur antara {min} dan {maks}',\n      defaultRangeStartLabel: 'pilih nilai mula',\n      defaultRangeEndLabel: 'pilih nilai akhir',\n    },\n    table: {\n      emptyText: 'Tiada Data',\n      confirmFilter: 'OK',\n      resetFilter: 'Reset',\n      clearFilter: 'Semua',\n      sumText: 'Jumlah',\n    },\n    tour: {\n      next: 'Seterusnya',\n      previous: 'Sebelumnya',\n      finish: 'Selesai',\n    },\n    tree: {\n      emptyText: 'Tiada Data',\n    },\n    transfer: {\n      noMatch: 'Tiada data yang sepadan',\n      noData: 'Tiada Data',\n      titles: ['Senarai 1', 'Senarai 2'],\n      filterPlaceholder: 'Masukkan kata kunci',\n      noCheckedFormat: '{total} barang',\n      hasCheckedFormat: '{checked}/{total} diperiksa',\n    },\n    image: {\n      error: 'FAILED',\n    },\n    pageHeader: {\n      title: 'Kembali',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ya',\n      BatalButtonText: 'Tidak',\n    },\n    carousel: {\n      leftArrow: 'Anak panah karusel ke kiri',\n      rightArrow: 'Anak panah karusel ke kanan',\n      indicator: 'Tukar karusel kepada indeks {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,qEAAqE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,iFAAiF,CAAC,gBAAgB,CAAC,6DAA6D,CAAC,eAAe,CAAC,6DAA6D,CAAC,YAAY,CAAC,qBAAqB,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,4BAA4B,CAAC,SAAS,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,+FAA+F,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,mCAAmC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,kCAAkC,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,UAAU,CAAC,6BAA6B,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAAC;;;;;;;;"}