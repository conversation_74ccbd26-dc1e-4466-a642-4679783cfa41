{"version": 3, "file": "radio-group.js", "sources": ["../../../../../../packages/components/radio/src/radio-group.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { radioEmits } from './radio'\nimport type { ExtractPropTypes } from 'vue'\nimport type RadioGroup from './radio-group.vue'\n\nexport const radioGroupProps = buildProps({\n  /**\n   * @description native `id` attribute\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description the size of radio buttons or bordered radios\n   */\n  size: useSizeProp,\n  /**\n   * @description whether the nesting radios are disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description border and background color when button is active\n   */\n  fill: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description font color when button is active\n   */\n  textColor: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description native `name` attribute\n   */\n  name: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport type RadioGroupProps = ExtractPropTypes<typeof radioGroupProps>\n\nexport const radioGroupEmits = radioEmits\nexport type RadioGroupEmits = typeof radioGroupEmits\nexport type RadioGroupInstance = InstanceType<typeof RadioGroup> & unknown\n"], "names": ["buildProps", "useSizeProp", "useAriaProps", "radioEmits"], "mappings": ";;;;;;;;;AAGY,MAAC,eAAe,GAAGA,kBAAU,CAAC;AAC1C,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,eAAe,GAAGC;;;;;"}