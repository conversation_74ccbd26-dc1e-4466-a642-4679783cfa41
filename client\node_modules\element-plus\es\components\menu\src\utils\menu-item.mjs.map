{"version": 3, "file": "menu-item.mjs", "sources": ["../../../../../../../packages/components/menu/src/utils/menu-item.ts"], "sourcesContent": ["// @ts-nocheck\nimport { triggerEvent } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport SubMenu from './submenu'\n\nclass MenuItem {\n  public submenu: SubMenu = null\n  constructor(public domNode: HTMLElement, namespace: string) {\n    this.submenu = null\n    this.init(namespace)\n  }\n\n  init(namespace: string): void {\n    this.domNode.setAttribute('tabindex', '0')\n    const menuChild = this.domNode.querySelector(`.${namespace}-menu`)\n    if (menuChild) {\n      this.submenu = new SubMenu(this, menuChild)\n    }\n    this.addListeners()\n  }\n\n  addListeners(): void {\n    this.domNode.addEventListener('keydown', (event: KeyboardEvent) => {\n      let prevDef = false\n      switch (event.code) {\n        case EVENT_CODE.down: {\n          triggerEvent(event.currentTarget as HTMLElement, 'mouseenter')\n          this.submenu && this.submenu.gotoSubIndex(0)\n          prevDef = true\n          break\n        }\n        case EVENT_CODE.up: {\n          triggerEvent(event.currentTarget as HTMLElement, 'mouseenter')\n          this.submenu &&\n            this.submenu.gotoSubIndex(this.submenu.subMenuItems.length - 1)\n          prevDef = true\n          break\n        }\n        case EVENT_CODE.tab: {\n          triggerEvent(event.currentTarget as HTMLElement, 'mouseleave')\n          break\n        }\n        case EVENT_CODE.enter:\n        case EVENT_CODE.numpadEnter:\n        case EVENT_CODE.space: {\n          prevDef = true\n          ;(event.currentTarget as HTMLElement).click()\n          break\n        }\n      }\n      if (prevDef) {\n        event.preventDefault()\n      }\n    })\n  }\n}\n\nexport default MenuItem\n"], "names": [], "mappings": ";;;;AAGA,MAAM,QAAQ,CAAC;AACf,EAAE,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE;AAClC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK;AACxD,MAAM,IAAI,OAAO,GAAG,KAAK,CAAC;AAC1B,MAAM,QAAQ,KAAK,CAAC,IAAI;AACxB,QAAQ,KAAK,UAAU,CAAC,IAAI,EAAE;AAC9B,UAAU,YAAY,CAAC,KAAK,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AACvD,UAAU,OAAO,GAAG,IAAI,CAAC;AACzB,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,UAAU,CAAC,EAAE,EAAE;AAC5B,UAAU,YAAY,CAAC,KAAK,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;AAC1D,UAAU,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC1F,UAAU,OAAO,GAAG,IAAI,CAAC;AACzB,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,UAAU,CAAC,GAAG,EAAE;AAC7B,UAAU,YAAY,CAAC,KAAK,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;AAC1D,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,KAAK,UAAU,CAAC,KAAK,CAAC;AAC9B,QAAQ,KAAK,UAAU,CAAC,WAAW,CAAC;AACpC,QAAQ,KAAK,UAAU,CAAC,KAAK,EAAE;AAC/B,UAAU,OAAO,GAAG,IAAI,CAAC;AACzB,UAAU,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACtC,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH;;;;"}