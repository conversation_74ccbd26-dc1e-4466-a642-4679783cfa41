{"version": 3, "file": "directive.js", "sources": ["../../../../../../packages/components/popover/src/directive.ts"], "sourcesContent": ["import type { DirectiveBinding, ObjectDirective } from 'vue'\nimport type { PopoverInstance } from './popover'\n\nconst attachEvents = (el: HTMLElement, binding: DirectiveBinding) => {\n  const popperComponent: PopoverInstance = binding.arg || binding.value\n  const popover = popperComponent?.popperRef\n  if (popover) {\n    popover.triggerRef = el\n  }\n}\n\nexport default {\n  mounted(el, binding) {\n    attachEvents(el, binding)\n  },\n  updated(el, binding) {\n    attachEvents(el, binding)\n  },\n} as ObjectDirective\n\nexport const VPopover = 'popover'\n"], "names": [], "mappings": ";;;;AAAA,MAAM,YAAY,GAAG,CAAC,EAAE,EAAE,OAAO,KAAK;AACtC,EAAE,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC;AACvD,EAAE,MAAM,OAAO,GAAG,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,SAAS,CAAC;AAC/E,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;AAC5B,GAAG;AACH,CAAC,CAAC;AACF,uBAAe;AACf,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AACvB,IAAI,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AACvB,IAAI,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC9B,GAAG;AACH,CAAC,CAAC;AACU,MAAC,QAAQ,GAAG;;;;;"}