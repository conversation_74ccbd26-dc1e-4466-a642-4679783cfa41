{"version": 3, "file": "tab-bar2.js", "sources": ["../../../../../../packages/components/tabs/src/tab-bar.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"barRef\"\n    :class=\"[ns.e('active-bar'), ns.is(rootTabs!.props.tabPosition)]\"\n    :style=\"barStyle\"\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  getCurrentInstance,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  ref,\n  watch,\n} from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { capitalize, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabBarProps } from './tab-bar'\n\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElTabBar'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabBarProps)\n\nconst instance = getCurrentInstance()!\nconst rootTabs = inject(tabsRootContextKey)\nif (!rootTabs) throwError(COMPONENT_NAME, '<el-tabs><el-tab-bar /></el-tabs>')\n\nconst ns = useNamespace('tabs')\n\nconst barRef = ref<HTMLDivElement>()\nconst barStyle = ref<CSSProperties>()\n\nconst getBarStyle = (): CSSProperties => {\n  let offset = 0\n  let tabSize = 0\n\n  const sizeName = ['top', 'bottom'].includes(rootTabs.props.tabPosition)\n    ? 'width'\n    : 'height'\n  const sizeDir = sizeName === 'width' ? 'x' : 'y'\n  const position = sizeDir === 'x' ? 'left' : 'top'\n\n  props.tabs.every((tab) => {\n    const $el = instance.parent?.refs?.[`tab-${tab.uid}`] as HTMLElement\n    if (!$el) return false\n\n    if (!tab.active) {\n      return true\n    }\n\n    offset = $el[`offset${capitalize(position)}`]\n    tabSize = $el[`client${capitalize(sizeName)}`]\n\n    const tabStyles = window.getComputedStyle($el)\n\n    if (sizeName === 'width') {\n      tabSize -=\n        Number.parseFloat(tabStyles.paddingLeft) +\n        Number.parseFloat(tabStyles.paddingRight)\n      offset += Number.parseFloat(tabStyles.paddingLeft)\n    }\n    return false\n  })\n\n  return {\n    [sizeName]: `${tabSize}px`,\n    transform: `translate${capitalize(sizeDir)}(${offset}px)`,\n  }\n}\n\nconst update = () => (barStyle.value = getBarStyle())\n\nconst saveObserver = [] as ReturnType<typeof useResizeObserver>[]\nconst observerTabs = () => {\n  saveObserver.forEach((observer) => observer.stop())\n  saveObserver.length = 0\n  const list = instance.parent?.refs as Record<string, HTMLElement>\n  if (!list) return\n  for (const key in list) {\n    if (key.startsWith('tab-')) {\n      const _el = list[key]\n      if (_el) {\n        saveObserver.push(useResizeObserver(_el, update))\n      }\n    }\n  }\n}\n\nwatch(\n  () => props.tabs,\n  async () => {\n    await nextTick()\n    update()\n\n    observerTabs()\n  },\n  { immediate: true }\n)\nconst barObserever = useResizeObserver(barRef, () => update())\n\nonBeforeUnmount(() => {\n  saveObserver.forEach((observer) => observer.stop())\n  saveObserver.length = 0\n  barObserever.stop()\n})\n\ndefineExpose({\n  /** @description tab root html element */\n  ref: barRef,\n  /** @description method to manually update tab bar style */\n  update,\n})\n</script>\n"], "names": ["getCurrentInstance", "inject", "tabsRootContextKey", "throwError", "capitalize", "useResizeObserver", "watch", "nextTick", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;;uCA0Bc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAGA,IAAA,MAAM,WAAWA,sBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,QAAA,GAAWC,WAAOC,4BAAkB,CAAA,CAAA;AAC1C,IAAA,IAAI,CAAC,QAAA;AAEL,MAAMC,+BAAwB,EAAA,mCAAA,CAAA,CAAA;AAE9B,IAAA,MAAM,uBAA6B,CAAA,MAAA,CAAA,CAAA;AACnC,IAAA,MAAM,gBAA8B,EAAA,CAAA;AAEpC,IAAA,MAAM,kBAAc,EAAqB,CAAA;AACvC,IAAA,MAAI,WAAS,GAAA,MAAA;AACb,MAAA,IAAI,MAAU,GAAA,CAAA,CAAA;AAEd,MAAM,IAAA,OAAA,GAAA,CAAA,CAAA;AAGN,MAAM,MAAA,QAAA,GAAuB,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA,QAAgB,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,OAAA,GAAA,QAAA,CAAA;AAC7C,MAAM,MAAA,OAAA,GAAA,QAAuB,KAAA,OAAe,GAAA,GAAA,GAAA,GAAA,CAAA;AAE5C,MAAM,MAAA,QAAW,GAAA,OAAS,KAAA,GAAA,GAAA,MAAA,GAAA,KAAA,CAAA;AACxB,MAAA,KAAA,CAAA,UAAqB,CAAA,CAAA,GAAA,KAAA;AACrB,QAAI,IAAA,MAAa,CAAA;AAEjB,QAAI,SAAK,GAAQ,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,IAAA,EAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACf,QAAO,IAAA,CAAA,GAAA;AAAA,UACT,OAAA,KAAA,CAAA;AAEA,QAAA,IAAA,CAAA,GAAA,CAAS,MAAI,EAAA;AACb,UAAA,OAAA,IAAc,CAAA;AAEd,SAAM;AAEN,QAAA,oBAA0B,EAAAC,kBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACxB,QACE,OAAA,GAAA,GAAA,CAAA,CAAA,0BAA4B,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAE9B,QAAU,MAAA,SAAA,GAAA,MAAkB,CAAA,gBAAqB,CAAA,GAAA,CAAA,CAAA;AAAA,QACnD,IAAA,QAAA,KAAA,OAAA,EAAA;AACA,UAAO,OAAA,IAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,WAAA,CAAA,GAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,YAAA,CAAA,CAAA;AAAA,UACR,MAAA,IAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,WAAA,CAAA,CAAA;AAED,SAAO;AAAA,QACL,OAAC,KAAW,CAAA;AAAU,OAAA,CACtB;AAAoD,MACtD,OAAA;AAAA,QACF,CAAA,QAAA,GAAA,CAAA,EAAA,OAAA,CAAA,EAAA,CAAA;AAEA,QAAA,SAAe,EAAA,CAAA,SAAgB,EAAAA,kBAAA,CAAQ,OAAY,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,GAAA,CAAA;AAEnD,OAAA,CAAA;AACA,KAAA,CAAA;AACE,IAAA,MAAA,MAAA,GAAa,MAAQ,QAAc,CAAA,KAAA,GAAA,aAAc,CAAC;AAClD,IAAA,MAAA,YAAsB,GAAA,EAAA,CAAA;AACtB,IAAM,MAAA,qBAAwB;AAC9B,MAAA,IAAI,EAAO,CAAA;AACX,MAAA,oBAAwB,CAAA,CAAA,QAAA,KAAA,QAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACtB,MAAI,YAAe,CAAA,MAAA,GAAA,CAAA,CAAA;AACjB,MAAM,MAAA,IAAA,GAAA,CAAA,EAAM,WAAQ,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA;AACpB,MAAA,IAAA,CAAA,IAAS;AACP,QAAA,OAAA;AAAgD,MAClD,KAAA,MAAA,GAAA,IAAA,IAAA,EAAA;AAAA,QACF,IAAA,GAAA,CAAA,UAAA,CAAA,MAAA,CAAA,EAAA;AAAA,UACF,MAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,UACF,IAAA,GAAA,EAAA;AAEA,YAAA,YAAA,CAAA,IAAA,CAAAC,sBAAA,CAAA,GAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAAA;AACc,SACA;AACV,OAAA;AACA,KAAO,CAAA;AAEP,IAAaC,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,EAAA,YAAA;AAAA,MACf,MAAAC,YAAA,EAAA,CAAA;AAAA,MACA;AAAkB,MACpB,YAAA,EAAA,CAAA;AACA,KAAA,EAAA,EAAA,SAAqB,EAAA,IAAA,EAAA,CAAA,CAAA;AAErB,IAAA,MAAA,YAAsB,GAAAF,sBAAA,CAAA,MAAA,EAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AACpB,IAAAG,mBAAa,CAAQ,MAAA;AACrB,MAAA,YAAA,CAAa,OAAS,CAAA,CAAA,QAAA,KAAA,QAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACtB,MAAA,YAAA,CAAa,MAAK,GAAA,CAAA,CAAA;AAAA,MACnB,YAAA,CAAA,IAAA,EAAA,CAAA;AAED,KAAa,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAEX,GAAK,EAAA,MAAA;AAAA,MAAA,MAAA;AAAA,KAEL,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;"}