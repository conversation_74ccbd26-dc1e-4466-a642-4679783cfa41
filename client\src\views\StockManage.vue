<template>
  <div class="page-container">
    <el-card>
      <div class="toolbar">
        <el-input v-model="search" placeholder="搜索产品名称" style="width: 200px; margin-right: 10px;" clearable />
        <el-button type="primary" @click="openAddDialog">添加库存</el-button>
      </div>
      <el-table :data="filteredStocks" style="width: 100%; margin-top: 16px;" border>
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="product" label="产品名称" />
        <el-table-column prop="quantity" label="库存数量" />
        <el-table-column prop="location" label="仓库位置" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="removeStock(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="editMode ? '编辑库存' : '添加库存'" v-model="dialogVisible">
      <el-form :model="form" label-width="80px">
        <el-form-item label="产品名称">
          <el-input v-model="form.product" />
        </el-form-item>
        <el-form-item label="库存数量">
          <el-input v-model="form.quantity" type="number" />
        </el-form-item>
        <el-form-item label="仓库位置">
          <el-input v-model="form.location" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveStock">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const search = ref('')
const dialogVisible = ref(false)
const editMode = ref(false)
const form = ref({ id: null, product: '', quantity: '', location: '' })
const stocks = ref([
  { id: 1, product: '五香驴肉', quantity: 100, location: 'A库' },
  { id: 2, product: '酱驴肉', quantity: 80, location: 'B库' },
  { id: 3, product: '驴肉火烧', quantity: 200, location: 'A库' }
])
const filteredStocks = computed(() => {
  if (!search.value) return stocks.value
  return stocks.value.filter(s => s.product.includes(search.value))
})
function openAddDialog() {
  editMode.value = false
  form.value = { id: null, product: '', quantity: '', location: '' }
  dialogVisible.value = true
}
function openEditDialog(row) {
  editMode.value = true
  form.value = { ...row }
  dialogVisible.value = true
}
function saveStock() {
  if (!form.value.product) return ElMessage.error('请输入产品名称')
  if (editMode.value) {
    const idx = stocks.value.findIndex(s => s.id === form.value.id)
    if (idx !== -1) stocks.value[idx] = { ...form.value }
    ElMessage.success('编辑成功')
  } else {
    form.value.id = Date.now()
    stocks.value.push({ ...form.value })
    ElMessage.success('添加成功')
  }
  dialogVisible.value = false
}
function removeStock(id) {
  stocks.value = stocks.value.filter(s => s.id !== id)
  ElMessage.success('删除成功')
}
</script>
<style scoped>
.page-container { padding: 18px; }
.toolbar { margin-bottom: 10px; display: flex; align-items: center; }
</style> 