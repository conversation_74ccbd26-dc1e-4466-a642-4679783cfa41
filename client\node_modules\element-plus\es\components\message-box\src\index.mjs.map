{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/message-box/src/index.vue"], "sourcesContent": ["<template>\n  <transition name=\"fade-in-linear\" @after-leave=\"$emit('vanish')\">\n    <el-overlay\n      v-show=\"visible\"\n      :z-index=\"zIndex\"\n      :overlay-class=\"[ns.is('message-box'), modalClass]\"\n      :mask=\"modal\"\n    >\n      <div\n        role=\"dialog\"\n        :aria-label=\"title\"\n        aria-modal=\"true\"\n        :aria-describedby=\"!showInput ? contentId : undefined\"\n        :class=\"`${ns.namespace.value}-overlay-message-box`\"\n        @click=\"overlayEvent.onClick\"\n        @mousedown=\"overlayEvent.onMousedown\"\n        @mouseup=\"overlayEvent.onMouseup\"\n      >\n        <el-focus-trap\n          loop\n          :trapped=\"visible\"\n          :focus-trap-el=\"rootRef\"\n          :focus-start-el=\"focusStartRef\"\n          @release-requested=\"onCloseRequested\"\n        >\n          <div\n            ref=\"rootRef\"\n            :class=\"[\n              ns.b(),\n              customClass,\n              ns.is('draggable', draggable),\n              { [ns.m('center')]: center },\n            ]\"\n            :style=\"customStyle\"\n            tabindex=\"-1\"\n            @click.stop=\"\"\n          >\n            <div\n              v-if=\"title !== null && title !== undefined\"\n              ref=\"headerRef\"\n              :class=\"[ns.e('header'), { 'show-close': showClose }]\"\n            >\n              <div :class=\"ns.e('title')\">\n                <el-icon\n                  v-if=\"iconComponent && center\"\n                  :class=\"[ns.e('status'), typeClass]\"\n                >\n                  <component :is=\"iconComponent\" />\n                </el-icon>\n                <span>{{ title }}</span>\n              </div>\n              <button\n                v-if=\"showClose\"\n                type=\"button\"\n                :class=\"ns.e('headerbtn')\"\n                :aria-label=\"t('el.messagebox.close')\"\n                @click=\"\n                  handleAction(distinguishCancelAndClose ? 'close' : 'cancel')\n                \"\n                @keydown.prevent.enter=\"\n                  handleAction(distinguishCancelAndClose ? 'close' : 'cancel')\n                \"\n              >\n                <el-icon :class=\"ns.e('close')\">\n                  <component :is=\"closeIcon || 'close'\" />\n                </el-icon>\n              </button>\n            </div>\n            <div :id=\"contentId\" :class=\"ns.e('content')\">\n              <div :class=\"ns.e('container')\">\n                <el-icon\n                  v-if=\"iconComponent && !center && hasMessage\"\n                  :class=\"[ns.e('status'), typeClass]\"\n                >\n                  <component :is=\"iconComponent\" />\n                </el-icon>\n                <div v-if=\"hasMessage\" :class=\"ns.e('message')\">\n                  <slot>\n                    <component\n                      :is=\"showInput ? 'label' : 'p'\"\n                      v-if=\"!dangerouslyUseHTMLString\"\n                      :for=\"showInput ? inputId : undefined\"\n                    >\n                      {{ !dangerouslyUseHTMLString ? message : '' }}\n                    </component>\n                    <component\n                      :is=\"showInput ? 'label' : 'p'\"\n                      v-else\n                      :for=\"showInput ? inputId : undefined\"\n                      v-html=\"message\"\n                    />\n                  </slot>\n                </div>\n              </div>\n              <div v-show=\"showInput\" :class=\"ns.e('input')\">\n                <el-input\n                  :id=\"inputId\"\n                  ref=\"inputRef\"\n                  v-model=\"inputValue\"\n                  :type=\"inputType\"\n                  :placeholder=\"inputPlaceholder\"\n                  :aria-invalid=\"validateError\"\n                  :class=\"{ invalid: validateError }\"\n                  @keydown.enter=\"handleInputEnter\"\n                />\n                <div\n                  :class=\"ns.e('errormsg')\"\n                  :style=\"{\n                    visibility: !!editorErrorMessage ? 'visible' : 'hidden',\n                  }\"\n                >\n                  {{ editorErrorMessage }}\n                </div>\n              </div>\n            </div>\n            <div :class=\"ns.e('btns')\">\n              <el-button\n                v-if=\"showCancelButton\"\n                :loading=\"cancelButtonLoading\"\n                :loading-icon=\"cancelButtonLoadingIcon\"\n                :class=\"[cancelButtonClass]\"\n                :round=\"roundButton\"\n                :size=\"btnSize\"\n                @click=\"handleAction('cancel')\"\n                @keydown.prevent.enter=\"handleAction('cancel')\"\n              >\n                {{ cancelButtonText || t('el.messagebox.cancel') }}\n              </el-button>\n              <el-button\n                v-show=\"showConfirmButton\"\n                ref=\"confirmRef\"\n                type=\"primary\"\n                :loading=\"confirmButtonLoading\"\n                :loading-icon=\"confirmButtonLoadingIcon\"\n                :class=\"[confirmButtonClasses]\"\n                :round=\"roundButton\"\n                :disabled=\"confirmButtonDisabled\"\n                :size=\"btnSize\"\n                @click=\"handleAction('confirm')\"\n                @keydown.prevent.enter=\"handleAction('confirm')\"\n              >\n                {{ confirmButtonText || t('el.messagebox.confirm') }}\n              </el-button>\n            </div>\n          </div>\n        </el-focus-trap>\n      </div>\n    </el-overlay>\n  </transition>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  markRaw,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  reactive,\n  ref,\n  toRefs,\n  watch,\n} from 'vue'\nimport ElButton from '@element-plus/components/button'\nimport { TrapFocus } from '@element-plus/directives'\nimport {\n  useDraggable,\n  useId,\n  useLockscreen,\n  useSameTarget,\n} from '@element-plus/hooks'\nimport ElInput from '@element-plus/components/input'\nimport { ElOverlay } from '@element-plus/components/overlay'\nimport {\n  TypeComponents,\n  TypeComponentsMap,\n  isFunction,\n  isString,\n  isValidComponentSize,\n} from '@element-plus/utils'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { Loading } from '@element-plus/icons-vue'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\n\nimport type { ComponentPublicInstance, PropType } from 'vue'\nimport type { ComponentSize } from '@element-plus/constants'\nimport type {\n  Action,\n  MessageBoxState,\n  MessageBoxType,\n} from './message-box.type'\n\nexport default defineComponent({\n  name: 'ElMessageBox',\n  directives: {\n    TrapFocus,\n  },\n  components: {\n    ElButton,\n    ElFocusTrap,\n    ElInput,\n    ElOverlay,\n    ElIcon,\n    ...TypeComponents,\n  },\n  inheritAttrs: false,\n  props: {\n    buttonSize: {\n      type: String as PropType<ComponentSize>,\n      validator: isValidComponentSize,\n    },\n    modal: {\n      type: Boolean,\n      default: true,\n    },\n    lockScroll: {\n      type: Boolean,\n      default: true,\n    },\n    showClose: {\n      type: Boolean,\n      default: true,\n    },\n    closeOnClickModal: {\n      type: Boolean,\n      default: true,\n    },\n    closeOnPressEscape: {\n      type: Boolean,\n      default: true,\n    },\n    closeOnHashChange: {\n      type: Boolean,\n      default: true,\n    },\n    center: Boolean,\n    draggable: Boolean,\n    overflow: Boolean,\n    roundButton: {\n      default: false,\n      type: Boolean,\n    },\n    container: {\n      type: String, // default append to body\n      default: 'body',\n    },\n    boxType: {\n      type: String as PropType<MessageBoxType>,\n      default: '',\n    },\n  },\n  emits: ['vanish', 'action'],\n  setup(props, { emit }) {\n    // const popup = usePopup(props, doClose)\n    const {\n      locale,\n      zIndex,\n      ns,\n      size: btnSize,\n    } = useGlobalComponentSettings(\n      'message-box',\n      computed(() => props.buttonSize)\n    )\n\n    const { t } = locale\n    const { nextZIndex } = zIndex\n\n    const visible = ref(false)\n    // s represents state\n    const state = reactive<MessageBoxState>({\n      // autofocus element when open message-box\n      autofocus: true,\n      beforeClose: null,\n      callback: null,\n      cancelButtonText: '',\n      cancelButtonClass: '',\n      confirmButtonText: '',\n      confirmButtonClass: '',\n      customClass: '',\n      customStyle: {},\n      dangerouslyUseHTMLString: false,\n      distinguishCancelAndClose: false,\n      icon: '',\n      closeIcon: '',\n      inputPattern: null,\n      inputPlaceholder: '',\n      inputType: 'text',\n      inputValue: '',\n      inputValidator: undefined,\n      inputErrorMessage: '',\n      message: '',\n      modalFade: true,\n      modalClass: '',\n      showCancelButton: false,\n      showConfirmButton: true,\n      type: '',\n      title: undefined,\n      showInput: false,\n      action: '' as Action,\n      confirmButtonLoading: false,\n      cancelButtonLoading: false,\n      confirmButtonLoadingIcon: markRaw(Loading),\n      cancelButtonLoadingIcon: markRaw(Loading),\n      confirmButtonDisabled: false,\n      editorErrorMessage: '',\n      // refer to: https://github.com/ElemeFE/element/commit/2999279ae34ef10c373ca795c87b020ed6753eed\n      // seemed ok for now without this state.\n      // isOnComposition: false, // temporary remove\n      validateError: false,\n      zIndex: nextZIndex(),\n    })\n\n    const typeClass = computed(() => {\n      const type = state.type\n      return { [ns.bm('icon', type)]: type && TypeComponentsMap[type] }\n    })\n\n    const contentId = useId()\n    const inputId = useId()\n\n    const iconComponent = computed(() => {\n      const type = state.type\n      return state.icon || (type && TypeComponentsMap[type]) || ''\n    })\n    const hasMessage = computed(() => !!state.message)\n    const rootRef = ref<HTMLElement>()\n    const headerRef = ref<HTMLElement>()\n    const focusStartRef = ref<HTMLElement>()\n    const inputRef = ref<ComponentPublicInstance>()\n    const confirmRef = ref<ComponentPublicInstance>()\n\n    const confirmButtonClasses = computed(() => state.confirmButtonClass)\n\n    watch(\n      () => state.inputValue,\n      async (val) => {\n        await nextTick()\n        if (props.boxType === 'prompt' && val) {\n          validate()\n        }\n      },\n      { immediate: true }\n    )\n\n    watch(\n      () => visible.value,\n      (val) => {\n        if (val) {\n          if (props.boxType !== 'prompt') {\n            if (state.autofocus) {\n              focusStartRef.value = confirmRef.value?.$el ?? rootRef.value\n            } else {\n              focusStartRef.value = rootRef.value\n            }\n          }\n          state.zIndex = nextZIndex()\n        }\n        if (props.boxType !== 'prompt') return\n        if (val) {\n          nextTick().then(() => {\n            if (inputRef.value && inputRef.value.$el) {\n              if (state.autofocus) {\n                focusStartRef.value = getInputElement() ?? rootRef.value\n              } else {\n                focusStartRef.value = rootRef.value\n              }\n            }\n          })\n        } else {\n          state.editorErrorMessage = ''\n          state.validateError = false\n        }\n      }\n    )\n\n    const draggable = computed(() => props.draggable)\n    const overflow = computed(() => props.overflow)\n    useDraggable(rootRef, headerRef, draggable, overflow)\n\n    onMounted(async () => {\n      await nextTick()\n      if (props.closeOnHashChange) {\n        window.addEventListener('hashchange', doClose)\n      }\n    })\n\n    onBeforeUnmount(() => {\n      if (props.closeOnHashChange) {\n        window.removeEventListener('hashchange', doClose)\n      }\n    })\n\n    function doClose() {\n      if (!visible.value) return\n      visible.value = false\n      nextTick(() => {\n        if (state.action) emit('action', state.action)\n      })\n    }\n\n    const handleWrapperClick = () => {\n      if (props.closeOnClickModal) {\n        handleAction(state.distinguishCancelAndClose ? 'close' : 'cancel')\n      }\n    }\n\n    const overlayEvent = useSameTarget(handleWrapperClick)\n\n    const handleInputEnter = (e: KeyboardEvent | Event) => {\n      if (state.inputType !== 'textarea') {\n        e.preventDefault()\n        return handleAction('confirm')\n      }\n    }\n\n    const handleAction = (action: Action) => {\n      if (props.boxType === 'prompt' && action === 'confirm' && !validate()) {\n        return\n      }\n\n      state.action = action\n\n      if (state.beforeClose) {\n        state.beforeClose?.(action, state, doClose)\n      } else {\n        doClose()\n      }\n    }\n\n    const validate = () => {\n      if (props.boxType === 'prompt') {\n        const inputPattern = state.inputPattern\n        if (inputPattern && !inputPattern.test(state.inputValue || '')) {\n          state.editorErrorMessage =\n            state.inputErrorMessage || t('el.messagebox.error')\n          state.validateError = true\n          return false\n        }\n        const inputValidator = state.inputValidator\n        if (isFunction(inputValidator)) {\n          const validateResult = inputValidator(state.inputValue)\n          if (validateResult === false) {\n            state.editorErrorMessage =\n              state.inputErrorMessage || t('el.messagebox.error')\n            state.validateError = true\n            return false\n          }\n          if (isString(validateResult)) {\n            state.editorErrorMessage = validateResult\n            state.validateError = true\n            return false\n          }\n        }\n      }\n      state.editorErrorMessage = ''\n      state.validateError = false\n      return true\n    }\n\n    const getInputElement = () => {\n      const inputRefs = inputRef.value?.$refs\n      return (inputRefs?.input ?? inputRefs?.textarea) as HTMLElement\n    }\n\n    const handleClose = () => {\n      handleAction('close')\n    }\n\n    // when close on press escape is disabled, pressing esc should not callout\n    // any other message box and close any other dialog-ish elements\n    // e.g. Dialog has a close on press esc feature, and when it closes, it calls\n    // props.beforeClose method to make a intermediate state by callout a message box\n    // for some verification or alerting. then if we allow global event liek this\n    // to dispatch, it could callout another message box.\n    const onCloseRequested = () => {\n      if (props.closeOnPressEscape) {\n        handleClose()\n      }\n    }\n\n    // locks the screen to prevent scroll\n    if (props.lockScroll) {\n      useLockscreen(visible)\n    }\n\n    return {\n      ...toRefs(state),\n      ns,\n      overlayEvent,\n      visible,\n      hasMessage,\n      typeClass,\n      contentId,\n      inputId,\n      btnSize,\n      iconComponent,\n      confirmButtonClasses,\n      rootRef,\n      focusStartRef,\n      headerRef,\n      inputRef,\n      confirmRef,\n      doClose, // for outside usage\n      handleClose, // for out side usage\n      onCloseRequested,\n      handleWrapperClick,\n      handleInputEnter,\n      handleAction,\n      t,\n    }\n  },\n})\n</script>\n"], "names": ["_resolveComponent", "_openBlock", "_createBlock", "_Transition", "_withCtx", "_createElementVNode", "_normalizeClass", "_createVNode", "_normalizeStyle", "_withModifiers", "_createElementBlock", "_resolveDynamicComponent", "_createCommentVNode", "_with<PERSON><PERSON><PERSON>", "_renderSlot", "_withDirectives", "_vShow", "_createTextVNode", "_toDisplayString"], "mappings": ";;;;;;;;;;;;;;;;;;AAkMA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EACN,UAAY,EAAA;AAAA,IACV,SAAA;AAAA,GACF;AAAA,EACA,UAAY,EAAA;AAAA,IACV,QAAA;AAAA,IACA,WAAA;AAAA,IACA,OAAA;AAAA,IACA,SAAA;AAAA,IACA,MAAA;AAAA,IACA,GAAG,cAAA;AAAA,GACL;AAAA,EACA,YAAc,EAAA,KAAA;AAAA,EACd,KAAO,EAAA;AAAA,IACL,UAAY,EAAA;AAAA,MACV,IAAM,EAAA,MAAA;AAAA,MACN,SAAW,EAAA,oBAAA;AAAA,KACb;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,UAAY,EAAA;AAAA,MACV,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,kBAAoB,EAAA;AAAA,MAClB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,MAAQ,EAAA,OAAA;AAAA,IACR,SAAW,EAAA,OAAA;AAAA,IACX,QAAU,EAAA,OAAA;AAAA,IACV,WAAa,EAAA;AAAA,MACX,OAAS,EAAA,KAAA;AAAA,MACT,IAAM,EAAA,OAAA;AAAA,KACR;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,MAAA;AAAA,MAAA,OAAA,EAAA,MAAA;AAAA,KAAA;AACG,IACX,OAAA,EAAA;AAAA,MACS,IAAA,EAAA,MAAA;AAAA,MACP,OAAM,EAAA,EAAA;AAAA,KAAA;AACG,GACX;AAAA,EACF,KAAA,EAAA,CAAA,QAAA,EAAA,QAAA,CAAA;AAAA,EACA,KAAA,CAAA,KAAQ,EAAA,EAAA,IAAkB,EAAA,EAAA;AAAA,IACpB,MAAA;AAEJ,MAAM,MAAA;AAAA,MACJ,MAAA;AAAA,MACA,EAAA;AAAA,MACA,IAAA,EAAA,OAAA;AAAA,KAAA,GACM,0BAAA,CAAA,aAAA,EAAA,QAAA,CAAA,MAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IACR,MAAI,EAAA,CAAA,EAAA,GAAA,MAAA,CAAA;AAAA,IACF,MAAA,EAAA,UAAA,EAAA,GAAA,MAAA,CAAA;AAAA,IACA,MAAA,OAAe,GAAA,GAAA,CAAA,KAAgB,CAAA,CAAA;AAAA,IACjC,MAAA,KAAA,GAAA,QAAA,CAAA;AAEA,MAAM,SAAQ,EAAA,IAAA;AACd,MAAM,iBAAa;AAEnB,MAAM,QAAA,EAAA,IAAU;AAEhB,MAAA,gBAAwC,EAAA,EAAA;AAAA,MAAA,iBAAA,EAAA,EAAA;AAAA,MAEtC,iBAAW,EAAA,EAAA;AAAA,MACX,kBAAa,EAAA,EAAA;AAAA,MACb,WAAU,EAAA,EAAA;AAAA,MACV,WAAkB,EAAA,EAAA;AAAA,MAClB,wBAAmB,EAAA,KAAA;AAAA,MACnB,yBAAmB,EAAA,KAAA;AAAA,MACnB,IAAoB,EAAA,EAAA;AAAA,MACpB,SAAa,EAAA,EAAA;AAAA,MACb,cAAc,IAAA;AAAA,MACd,gBAA0B,EAAA,EAAA;AAAA,MAC1B,SAA2B,EAAA,MAAA;AAAA,MAC3B,UAAM,EAAA,EAAA;AAAA,MACN,cAAW,EAAA,KAAA,CAAA;AAAA,MACX,iBAAc,EAAA,EAAA;AAAA,MACd,OAAkB,EAAA,EAAA;AAAA,MAClB,SAAW,EAAA,IAAA;AAAA,MACX,UAAY,EAAA,EAAA;AAAA,MACZ,gBAAgB,EAAA,KAAA;AAAA,MAChB,iBAAmB,EAAA,IAAA;AAAA,MACnB,IAAS,EAAA,EAAA;AAAA,MACT,KAAW,EAAA,KAAA,CAAA;AAAA,MACX,SAAY,EAAA,KAAA;AAAA,MACZ,MAAkB,EAAA,EAAA;AAAA,MAClB,oBAAmB,EAAA,KAAA;AAAA,MACnB,mBAAM,EAAA,KAAA;AAAA,MACN,wBAAO,EAAA,OAAA,CAAA,OAAA,CAAA;AAAA,MACP,uBAAW,EAAA,OAAA,CAAA,OAAA,CAAA;AAAA,MACX,qBAAQ,EAAA,KAAA;AAAA,MACR,kBAAsB,EAAA,EAAA;AAAA,MACtB,aAAqB,EAAA,KAAA;AAAA,MACrB,MAAA,EAAA,UAAA,EAAA;AAAyC,KACzC,CAAA,CAAA;AAAwC,IAAA,MACjB,SAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACvB,MAAoB,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AAAA,MAAA,OAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,IAAA,CAAA,GAAA,IAAA,IAAA,iBAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAAA,SAAA,GAAA,KAAA,EAAA,CAAA;AAAA,IAAA,MAIL,OAAA,GAAA,KAAA,EAAA,CAAA;AAAA,IAAA,mBACI,GAAA,QAAA,CAAA,MAAA;AAAA,MACpB,MAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AAED,MAAM,OAAA,KAAA,CAAA,YAAqB,IAAM,iBAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;AAC/B,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,UAAS,GAAA,QAAY,CAAA,MAAI,CAAA,CAAA,KAAA,CAAQ,OAAkB,CAAA,CAAA;AAAM,IAClE,MAAC,OAAA,GAAA,GAAA,EAAA,CAAA;AAED,IAAA,MAAM,YAAY,GAAM,EAAA,CAAA;AACxB,IAAA,MAAM,aAAgB,GAAA,GAAA,EAAA,CAAA;AAEtB,IAAM,MAAA,QAAA,GAAA,GAAA,EAAgB;AACpB,IAAA,MAAA,UAAmB,GAAA,GAAA,EAAA,CAAA;AACnB,IAAA,MAAA,oBAA8B,GAAA,QAAA,CAAA,MAAA,KAAkB,mBAAU,CAAA,CAAA;AAAA,IAC5D,KAAC,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,OAAA,GAAA,KAAA;AACD,MAAA,iBAAmB;AACnB,MAAA,IAAM,aAA2B,KAAA,QAAA,IAAA,GAAA,EAAA;AACjC,QAAA;AACA,OAAA;AACA,KAAA,EAAA,EAAA,eAA8C,EAAA,CAAA,CAAA;AAC9C,IAAA,KAAA,CAAM,aAAa,CAA6B,KAAA,EAAA,CAAA,GAAA,KAAA;AAEhD,MAAA,IAAM,EAAuB,EAAA,EAAA,CAAA;AAE7B,MAAA,IAAA,GAAA,EAAA;AAAA,YACQ,KAAM,CAAA,OAAA,KAAA,QAAA,EAAA;AAAA,cACG,KAAA,CAAA,SAAA,EAAA;AACb,YAAA,aAAe,CAAA,KAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,KAAA,IAAA,GAAA,EAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AACf,WAAI,MAAA;AACF,YAAS,aAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AAAA,WACX;AAAA,SACF;AAAA,QACE,YAAgB,GAAA,UAAA,EAAA,CAAA;AAAA,OACpB;AAEA,MAAA,IAAA,KAAA,CAAA,OAAA,KAAA,QAAA;AAAA,eACgB;AAAA,MACd,IAAS,GAAA,EAAA;AACP,QAAA,QAAS,EAAA,CAAA,IAAA,CAAA,MAAA;AACP,UAAI,IAAA,GAAA,CAAA;AACF,UAAA,IAAA,QAAU,CAAW,KAAA,IAAA,QAAA,CAAA,KAAA,CAAA,GAAA,EAAA;AACnB,YAAA,IAAA,KAAA,CAAA,SAAsB,EAAA;AAAiC,cAClD,aAAA,CAAA,KAAA,GAAA,CAAA,GAAA,GAAA,eAAA,EAAA,KAAA,IAAA,GAAA,GAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AACL,aAAA,MAAA;AAA8B,cAChC,aAAA,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AAAA,aACF;AACA,WAAA;AAA0B,SAC5B,CAAA,CAAA;AACA,OAAI,MAAA;AACJ,QAAA,KAAS,CAAA,kBAAA,GAAA,EAAA,CAAA;AACP,QAAS,KAAA,CAAA,aAAa,GAAA,KAAA,CAAA;AACpB,OAAA;AACE,KAAA,CAAA,CAAA;AACE,IAAc,MAAA,SAAA,GAAA,QAAA,CAAA,MAAA,KAAwB,CAAA,SAAA,CAAA,CAAA;AAAa,IAAA,MAAA,QAC9C,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACL,IAAA,YAAA,CAAA,OAAA,EAAA,oBAA8B,EAAA,QAAA,CAAA,CAAA;AAAA,IAChC,SAAA,CAAA,YAAA;AAAA,MACF,MAAA,QAAA,EAAA,CAAA;AAAA,MAAA,IACF,KAAC,CAAA,iBAAA,EAAA;AAAA,QACH,MAAO,CAAA,gBAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA;AACL,OAAA;AACA,KAAA,CAAA,CAAA;AAAsB,IACxB,eAAA,CAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,QACF,MAAA,CAAA,mBAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA;AAEA,OAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAa,SAAA,OAAA,GAAA;AAEb,MAAA,IAAA,CAAA,OAAsB,CAAA,KAAA;AACpB,QAAA,OAAe;AACf,MAAA,aAA6B,GAAA,KAAA,CAAA;AAC3B,MAAO,QAAA,CAAA,MAAA;AAAsC,QAC/C,IAAA,KAAA,CAAA,MAAA;AAAA,UACD,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAED,OAAA,CAAA,CAAA;AACE,KAAA;AACE,IAAO,MAAA,kBAAA,GAAA;AAAyC,MAClD,IAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,QACD,YAAA,CAAA,KAAA,CAAA,yBAAA,GAAA,OAAA,GAAA,QAAA,CAAA,CAAA;AAED,OAAA;AACE,KAAI,CAAA;AACJ,IAAA,MAAA,YAAgB,GAAA,aAAA,CAAA,kBAAA,CAAA,CAAA;AAChB,IAAA,MAAA,gBAAe,GAAA,CAAA,CAAA,KAAA;AACb,MAAA,IAAA,KAAU,CAAA,SAAQ,KAAK,UAAA;AAAsB,QAC9C,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,QACH,OAAA,YAAA,CAAA,SAAA,CAAA,CAAA;AAEA,OAAA;AACE,KAAA,CAAA;AACE,IAAa,MAAA,YAAA,GAAA,CAAA,MAAkC,KAAA;AAAkB,MACnE,IAAA,EAAA,CAAA;AAAA,MACF,IAAA,KAAA,CAAA,OAAA,KAAA,QAAA,IAAA,MAAA,KAAA,SAAA,IAAA,CAAA,QAAA,EAAA,EAAA;AAEA,QAAM,OAAA;AAEN,OAAM;AACJ,MAAI,KAAA,CAAA;AACF,MAAA,IAAE,KAAe,CAAA,WAAA,EAAA;AACjB,QAAA,CAAA,EAAA,GAAA,iBAA6B,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OAC/B,MAAA;AAAA,QACF,OAAA,EAAA,CAAA;AAEA,OAAM;AACJ,KAAA,CAAA;AACE,IAAA,MAAA,QAAA,GAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,OAAA,KAAA,QAAA,EAAA;AAEA,QAAA,MAAe,YAAA,GAAA,KAAA,CAAA,YAAA,CAAA;AAEf,QAAA,gBAAuB,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,IAAA,EAAA,CAAA,EAAA;AACrB,UAAM,KAAA,CAAA,kBAAsB,GAAA,KAAA,CAAO,iBAAO,IAAA,CAAA,CAAA,qBAAA,CAAA,CAAA;AAAA,UACrC,KAAA,CAAA,aAAA,GAAA,IAAA,CAAA;AACL,UAAQ,OAAA,KAAA,CAAA;AAAA,SACV;AAAA,QACF,MAAA,cAAA,GAAA,KAAA,CAAA,cAAA,CAAA;AAEA,QAAA,cAAuB,CAAA,cAAA,CAAA,EAAA;AACrB,UAAI,MAAM,cAAsB,GAAA,cAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAC9B,UAAA,IAAM,mBAAqB,KAAA,EAAA;AAC3B,YAAI,wBAAiB,GAAA,uBAAwB,IAAA,CAAc,sBAAK,CAAA,CAAA;AAC9D,YAAA,KACE,CAAA,aAAA,GAAA,IAAA,CAAA;AACF,YAAA,OAAsB,KAAA,CAAA;AACtB,WAAO;AAAA,UACT,IAAA,QAAA,CAAA,cAAA,CAAA,EAAA;AACA,YAAA,wBAA6B,GAAA,cAAA,CAAA;AAC7B,YAAI,KAAA,CAAA,oBAA4B,CAAA;AAC9B,YAAM,OAAA,KAAA,CAAA;AACN,WAAA;AACE,SAAA;AAEA,OAAA;AACA,MAAO,KAAA,CAAA,kBAAA,GAAA,EAAA,CAAA;AAAA,MACT,KAAA,CAAA,aAAA,GAAA,KAAA,CAAA;AACA,MAAI,OAAA,IAAA,CAAA;AACF,KAAA,CAAA;AACA,IAAA,MAAA,eAAsB,GAAA,MAAA;AACtB,MAAO,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACT,MAAA,SAAA,GAAA,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,MACF,OAAA,CAAA,EAAA,GAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,QAAA,CAAA;AAAA,KACF,CAAA;AACA,IAAA,MAAA,WAA2B,GAAA,MAAA;AAC3B,MAAA,YAAsB,CAAA,OAAA,CAAA,CAAA;AACtB,KAAO,CAAA;AAAA,IACT,MAAA,gBAAA,GAAA,MAAA;AAEA,MAAA,IAAM,wBAAwB,EAAA;AAC5B,QAAM,WAAA,EAAA,CAAA;AACN,OAAQ;AAA+B,KACzC,CAAA;AAEA,IAAA,IAAA,gBAAoB,EAAM;AACxB,MAAA,aAAa,CAAO,OAAA,CAAA,CAAA;AAAA,KACtB;AAQA,IAAA;AACE,MAAA,GAAA,OAAU,KAAoB,CAAA;AAC5B,MAAY,EAAA;AAAA,MACd,YAAA;AAAA,MACF,OAAA;AAGA,MAAA,UAAsB;AACpB,MAAA,SAAA;AAAqB,MACvB,SAAA;AAEA,MAAO,OAAA;AAAA,MACL;AAAe,MACf,aAAA;AAAA,MACA,oBAAA;AAAA,MACA,OAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,OAAA;AAAA,MACA,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,kBAAA;AAAA,MACA,gBAAA;AAAA,MACA,YAAA;AAAA,MACA,CAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CAAA,CAAA,CAAA;AACA,SAAA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,MAAA,kBAAA,GAAAA,gBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,EACA,MAAA,mBAAA,GAAAA,gBAAA,CAAA,UAAA,CAAA,CAAA;AAAA,EACA,MAAA,oBAAA,GAAAA,gBAAA,CAAA,WAAA,CAAA,CAAA;AAAA,EACA,MAAA,wBAAA,GAAAA,gBAAA,CAAA,eAAA,CAAA,CAAA;AAAA,EACA,MAAA,qBAAA,GAAAA,gBAAA,CAAA,YAAA,CAAA,CAAA;AAAA,EACF,OAAAC,SAAA,EAAA,EAAAC,WAAA,CAAAC,UAAA,EAAA;AAAA,IACF,IAAA,EAAA,gBAAA;AACF,IAAC,YAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA;;;;;;;;;AA7Wc,QAnJI,OAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,UAAkBC,mBAAa,KAAK,EAAA;AAAA,YAArD,IAAA,EAAA,QAAA;AAAA,YAAA,YAAA,EAAA,IAAA,CAAA,KAAA;wBAkJe,EAAA,MAAA;AAAA,YAAA,kBAAA,EAAA,CAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,GAAA,KAAA,CAAA;AAAA,YA/ID,KAAA,EAAAC,cAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,CAAA;AAAA,YACI,OAAA,EAAA,IAAA,CAAG;AAAgC,YAC1C,WAAA,EAAA,IAAA,CAAA,YAAA,CAAA,WAAA;AAAA,YAAA,SAAA,EAAA,IAAA,CAAA,YAAA,CAAA,SAAA;;AA4ID,YAAAC,WAAA,CAAA,wBAAA,EAAA;AAAA,cAzIC,IAAA,EAAA,EAAA;AAAA,cACQ,OAAA,EAAA,IAAA,CAAA,OAAA;AAAA,cACF,eAAA,EAAA,IAAA,CAAA,OAAA;AAAA,cACV,gBAAA,EAAgB,IAAG,CAAA,aAAY;AAAY,cACtC,kBAAA,EAAA,IAAK,CAAG,gBAAA;AAAe;AACR,8BACI,CAAA,MAAA;AAAA,kCACF,CAAA,KAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,SAAA;kBAiIP,KAAA,EAAAD,cAAA,CAAA;AAAA,oBA9Hd,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA;AAAA,oBACU,IAAA,CAAA,WAAA;AAAA,oBACM,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,WAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,oBACC,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA;AAAA,mBACG,CAAA;AAAA,kBAAA,KAAA,EAAAE,cAAA,CAAA,IAAA,CAAA,WAAA,CAAA;gCAEpB;AAuHM,kBAAA,OAAA,EAAAC,aAAA,CAAA,MAAA;AAAA,mBAtHA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,iBAAA,EACE;AAAA,kBAAA,UAAqB,KAAC,IAAA,IAAA,IAAA,CAAA,KAAA,KAAA,KAAA,CAAA,IAAAR,SAAA,EAAA,EAAAS,kBAAA,CAAA,KAAA,EAAA;AAAA,oBAAkB,GAAA,EAAA,CAAA;AAAA,oBAA2B,GAAA,EAAA,WAAK;AAAuB,oBAAoB,KAAA,EAAAJ,cAAI,CAAA,CAAA,IAAmB,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,EAAA,YAAA,EAAA,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAA,mBAAA,EAAA;AAM/I,oBAAAD,wBAAkB,EAAA;AAAA,sBACV,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA;AACT,sBAAc,IAAA,CAAA,aAAA,IAAA,IAAA,CAAA,MAAA,IAAAL,SAAA,EAAA,EAAAC,WAAA,CAAA,kBAAA,EAAA;AAAA,wBAAA,GAAA,EAAA,CAAA;wBAGD,KAAA,EAAAI,wBAAuB,CADpC,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AAAA,uBA8BM,EAAA;AAAA,wBAAA,OAAA,EAAAF,OAAA,CAAA,MAAA;AAAA,2BAAAH,SAAA,EAAA,EAAAC,WAAA,CAAAS,uBAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;yBA5BA,CAAA;AAAA,wBACE,CAAA,EAAA,CAAA;AAA4C,uBAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;AAElD,qBAAA,EAAA,CAAA,CAAA;AAAA,oBAQM,IAAA,CAAA,SAAA,IAAAX,SAAA,EAAA,EAAAS,kBAAA,CAAA,QAAA,EAAA;AAAA,sBAAA,GAAA,EAAA,CAAA;AAAA,sBARA,IAAA,EAAA,QAAO;AAAI,sBAAA,KAAA,EAAAJ,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;;AAEP,sBAAA,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAiB,2CAIf,GAAA,OAAA,GAAA,QAAA,CAAA;AAAA,sBAAA,SAAA,EAAAO,QAAA,CAAAJ,aAAA,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,yBAAA,GAAA,OAAA,GAAA,QAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAHP,qBAAA,EAAA;AAAiC,sBAAAF,WAAA,CAAA,kBAAA,EAAA;6CAElC,CAAiC,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,uBAAjC,EAAA;AAA6B,wBAAA,OAAA,EAAAH,OAAA,CAAA,MAAA;;;;AAE/B,uBAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,qBAAwB,EAAA,EAAA,EAAA,CAAA,YAAA,EAAA,SAAA,EAAA,WAAA,CAAA,CAAA,IAAAQ,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,mBAAA,EAAA,CAAA,CAAA,IAAAA,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,kBAAAP,kBAAA,CAAA,KAAA,EAAf;AAAK,oBAAA,EAAA,EAAA,IAAA,CAAA,SAAA;AAAA,oBAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;AAAA,mBAAA,EAAA;AAAA,oBAAAD,kBAAA,CAAA,KAAA,EAAA;;;;AAGR,wBAAA,GAAA,EAAA,CAAA;AAcC,wBAAA,KAAA,EAAAC,cAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,SAAA,CAAA,CAAA;yBAbF;AAAA,wBACJ,OAAK,EAAEF,OAAA,CAAA,MAAA;AAAI,sCACE,EAAAF,WAAA,CAAAS,uBAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;AAAA,yBACb,CAAA;AAAgE,wBAGhE,CAAA,EAAA,CAAA;;0BAMS,CAAA,UAAA,IAAAV,SAAA,EAAA,EAAAS,kBAAA,CAAA,KAAA,EAAA;AAAA,wBAFA,GAAA,EAAA,CAAA;AAAW,wBAAA,KAAA,EAAAJ,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA;;AACqB,wBAAxCQ,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AAAyB,0BAAA,CAAA,IAAA,CAAA,wBAAA,IAAAb,SAAA,EAAA,EAAAC,WAAA,CAAAS,uBAAA,CAAA,IAAA,CAAA,SAAA,GAAA,OAAA,GAAA,GAAA,CAAA,EAAA;;;;;;;;;4BAkDzB,GAAA,EAAA,CAAA;AAAA,4BA9CI,GAAA,EAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA,GAAA,KAAA,CAAA;AAAA,4BAAmB,SAAA,EAAA,IAAA,CAAA,OAAG;AAAC,2BAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,KAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAC/B,yBAAA,CAAA;AAAA,uBAwBM,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,qBAAA,EAAA,CAAA,CAAA;AAAA,oBAxBAG,cAAO,CAAAV,kBAAG,CAAC,KAAA,EAAA;AAAA,sBAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;;iCAEM,CAAA,mBAAe,EAAA;AAI1B,wBAAA,EAAA,EAAA,IAAA,CAAA,OAAA;AAHP,wBAAA,GAAA,EAAA,UAAQ;AAAyB,wBAAA,UAAA,EAAA,IAAA,CAAA,UAAA;6CAED,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,UAAA,GAAA,MAAA;AAAA,wBAAjC,IAAA,EAAA,IAAA,CAAA,SAAA;AAA6B,wBAAA,WAAA,EAAA,IAAA,CAAA,gBAAA;;;;yBAE/B,IAAA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,OAAA,EAAA,WAAA,CAAA,CAAA;AAAA,sBAgBMD,kBAAA,CAAA,KAAA,EAAA;AAAA,wBAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA;AAAA,wBAAA,KAAA,EAAAE,cAAA,CAAA;oCAhBuB,EAAA,CAAA,CAAA,IAAA,CAAA,kBAAM,GAAA,SAAA,GAAA,QAAA;AAAA,yBAAA,CAAA;;AACjC,qBAAA,EAAA,CAAA,CAAA,EAAA;AAcO,sBAXI,CAAAQ,KAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AADO,qBAAA,CAAA;AAEb,mBAAA,EAAA,EAAA,EAAA,CAAA,IAAA,CAAA,CAAA;AAA2B,kBAAAX,kBAAA,CAAA,KAAA,EAAA;iDAE5B,CAA8C,CAAA,CAAA,MAAA,CAAA,CAAA;AAAA,mBAAA,EAAA;AAA1C,oBAAA,IAAA,CAAA,gBAAA,IAAAJ,SAAA,EAAA,EAAAC,WAAA,CAAA,oBAAkC,EAAA;AAAA,sBAAA,GAAA,EAAA,CAAA;AAAA,sBAAA,OAAA,EAAA,IAAA,CAAA,mBAAA;AAAA,sBAAA,cAAA,EAAA,IAAA,CAAA,uBAAA;AAAA,sBAAA,KAAA,EAAAI,cAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA;;;AAExC,sBAAA,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,YAAA,CAAA,QAAA,CAAA;AACgB,sBAAA,SAAA,EAAAO,QAAA,CAAAJ,aAAA,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,YAAA,CAAA,QAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAEb,qBAAA,EAAA;AAA2B,sBAAA,OAAA,EAAAL,OACpB,CAAA,MAAA;AAAA,wBAAAa,eAAA,CAAAC,eAAA,CAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,CAAA,CAAA,CAAA,sBAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;;;;;;;;;AAKhB,sBAAA,KAAA,EAAA,IAAA,CAAA,WAAA;AAAA,sBAmBM,QAAA,EAAA,IAAA,CAAA,qBAAA;AAAA,sBAAA,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,sBAnBmB,OAAA,EAAK,CAAE,MAAA,KAAA,IAAA,CAAA,YAAI,CAAA,SAAA,CAAA;AAAA,sBAAA,SAAA,EAAAL,QAAA,CAAAJ,aAAA,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,YAAA,CAAA,SAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;;6BAUhC,EAAAL,OAAA,CAAA,MAAA;AAAA,wBAAAa,eARK,CAAAC,eAAA,CAAA,IAAA,CAAA,iBAAA,IAAA,IAAA,CAAA,CAAA,CAAA,uBAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,uBAAA,CAAA;AACD,sBACK,CAAA,EAAA,CAAA;AAAA,qBAAU,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,OAAA,EAAA,OAAA,EAAA,UAAA,EAAA,MAAA,EAAA,SAAA,EAAA,WAAA,CAAA,CAAA,EAAA;AAAA,sBAAA,CAAAF,KACZ,EAAA,IAAA,CAAA,iBAAA,CAAA;AAAA,qBAAA,CAAA;AACO,mBAAA,EAAA,CAAA,CAAA;AACC,iBACd,EAAA,EAAA,EAAA,CAAA,SAAK;AAA0B,eAC/B,CAAA;AAA+B,cAAA,CAAA,EAAA,CAAA;AAElC,aAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,oBAAA,CAAA,CAAA;AAAA,WAOM,EAAA,EAAA,EAAA,CAAA,YAAA,EAAA,kBAAA,EAAA,SAAA,EAAA,aAAA,EAAA,WAAA,CAAA,CAAA;AAAA,SAAA,CAAA;AAAA,QANH,CAAA,EAAA,CAAA;AAAW,OAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,eACN,EAAA,MAAA,CAAA,CAAA,EAAA;AAAA,QAAA,CAAAA,KAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AAAwD,OAAA,CAAA;;;AAIzC,GAAA,EAAA,CAAA,EAAA,CAAA,cAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AAAA,4BAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,WAAA,CAAA,CAAA,CAAA;;;;"}