@use 'mixins/mixins' as *;
@use 'common/var' as *;

@mixin showScrollbar {
  @include b(virtual-scrollbar) {
    opacity: 1;
  }
}

@include b(vl) {
  @include e(wrapper) {
    position: relative;
    &:hover {
      @include showScrollbar();
    }
    &.always-on {
      @include showScrollbar();
    }
  }
}

.#{$namespace}-vl__window {
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
@include b(virtual-scrollbar) {
  opacity: 0;
  transition: opacity 340ms ease-out;
  &.always-on {
    opacity: 1;
  }
}

@include b(vg) {
  @include e(wrapper) {
    position: relative;
  }
}
