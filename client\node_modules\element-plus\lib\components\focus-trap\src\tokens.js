'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const FOCUS_AFTER_TRAPPED = "focus-trap.focus-after-trapped";
const FOCUS_AFTER_RELEASED = "focus-trap.focus-after-released";
const FOCUSOUT_PREVENTED = "focus-trap.focusout-prevented";
const FOCUS_AFTER_TRAPPED_OPTS = {
  cancelable: true,
  bubbles: false
};
const FOCUSOUT_PREVENTED_OPTS = {
  cancelable: true,
  bubbles: false
};
const ON_TRAP_FOCUS_EVT = "focusAfterTrapped";
const ON_RELEASE_FOCUS_EVT = "focusAfterReleased";
const FOCUS_TRAP_INJECTION_KEY = Symbol("elFocusTrap");

exports.FOCUSOUT_PREVENTED = FOCUSOUT_PREVENTED;
exports.FOCUSOUT_PREVENTED_OPTS = FOCUSOUT_PREVENTED_OPTS;
exports.FOCUS_AFTER_RELEASED = FOCUS_AFTER_RELEASED;
exports.FOCUS_AFTER_TRAPPED = FOCUS_AFTER_TRAPPED;
exports.FOCUS_AFTER_TRAPPED_OPTS = FOCUS_AFTER_TRAPPED_OPTS;
exports.FOCUS_TRAP_INJECTION_KEY = FOCUS_TRAP_INJECTION_KEY;
exports.ON_RELEASE_FOCUS_EVT = ON_RELEASE_FOCUS_EVT;
exports.ON_TRAP_FOCUS_EVT = ON_TRAP_FOCUS_EVT;
//# sourceMappingURL=tokens.js.map
