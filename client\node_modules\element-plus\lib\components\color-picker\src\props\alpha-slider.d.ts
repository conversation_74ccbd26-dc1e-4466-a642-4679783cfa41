import type { ExtractPropTypes } from 'vue';
import type Color from '../utils/color';
export declare const alphaSliderProps: {
    readonly color: {
        readonly type: import("vue").PropType<Color>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly vertical: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
};
export type AlphaSliderProps = ExtractPropTypes<typeof alphaSliderProps>;
