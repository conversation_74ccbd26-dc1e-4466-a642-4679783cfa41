import type { ComponentPublicInstance, PropType } from 'vue';
import type { ComponentSize } from 'element-plus/es/constants';
import type { Action, MessageBoxState, MessageBoxType } from './message-box.type';
declare const _default: import("vue").DefineComponent<{
    buttonSize: {
        type: PropType<ComponentSize>;
        validator: (val: string) => val is ComponentSize | "";
    };
    modal: {
        type: BooleanConstructor;
        default: boolean;
    };
    lockScroll: {
        type: BooleanConstructor;
        default: boolean;
    };
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    closeOnClickModal: {
        type: BooleanConstructor;
        default: boolean;
    };
    closeOnPressEscape: {
        type: BooleanConstructor;
        default: boolean;
    };
    closeOnHashChange: {
        type: BooleanConstructor;
        default: boolean;
    };
    center: BooleanConstructor;
    draggable: BooleanConstructor;
    overflow: BooleanConstructor;
    roundButton: {
        default: boolean;
        type: BooleanConstructor;
    };
    container: {
        type: StringConstructor;
        default: string;
    };
    boxType: {
        type: PropType<MessageBoxType>;
        default: string;
    };
}, {
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    overlayEvent: {
        onClick: (e: MouseEvent) => void;
        onMousedown: (e: MouseEvent) => void;
        onMouseup: (e: MouseEvent) => void;
    };
    visible: import("vue").Ref<boolean>;
    hasMessage: import("vue").ComputedRef<boolean>;
    typeClass: import("vue").ComputedRef<{
        [x: string]: any;
    }>;
    contentId: import("vue").Ref<string>;
    inputId: import("vue").Ref<string>;
    btnSize: import("vue").ComputedRef<"small" | "" | "default" | "large">;
    iconComponent: import("vue").ComputedRef<any>;
    confirmButtonClasses: import("vue").ComputedRef<string>;
    rootRef: import("vue").Ref<HTMLElement | undefined>;
    focusStartRef: import("vue").Ref<HTMLElement | undefined>;
    headerRef: import("vue").Ref<HTMLElement | undefined>;
    inputRef: import("vue").Ref<ComponentPublicInstance | undefined>;
    confirmRef: import("vue").Ref<ComponentPublicInstance | undefined>;
    doClose: () => void;
    handleClose: () => void;
    onCloseRequested: () => void;
    handleWrapperClick: () => void;
    handleInputEnter: (e: KeyboardEvent | Event) => void;
    handleAction: (action: Action) => void;
    t: import("element-plus/es/hooks").Translator;
    autofocus: import("vue").Ref<boolean>;
    title: import("vue").Ref<string | undefined>;
    message: import("vue").Ref<string>;
    type: import("vue").Ref<"" | "error" | "success" | "primary" | "warning" | "info">;
    icon: import("vue").Ref<string | import("vue").FunctionalComponent<any, any> | {
        new (...args: any[]): any;
        __isFragment?: never;
        __isTeleport?: never;
        __isSuspense?: never;
    } | {
        [x: string]: any;
        setup?: ((this: void, props: Readonly<import("@vue/shared").LooseRequired<any>>, ctx: import("vue").SetupContext<any>) => any) | undefined;
        name?: string | undefined;
        template?: (string | object) | undefined;
        render?: Function | undefined;
        components?: Record<string, import("vue").Component> | undefined;
        directives?: Record<string, import("vue").Directive> | undefined;
        inheritAttrs?: boolean | undefined;
        emits?: any;
        expose?: string[] | undefined;
        serverPrefetch?: (() => Promise<any>) | undefined;
        compilerOptions?: {
            isCustomElement?: ((tag: string) => boolean) | undefined;
            whitespace?: ("preserve" | "condense") | undefined;
            comments?: boolean | undefined;
            delimiters?: [string, string] | undefined;
        } | undefined;
        call?: ((this: unknown, ...args: unknown[]) => never) | undefined;
        __isFragment?: never | undefined;
        __isTeleport?: never | undefined;
        __isSuspense?: never | undefined;
        __defaults?: {} | undefined;
        compatConfig?: {
            GLOBAL_MOUNT?: boolean | "suppress-warning" | undefined;
            GLOBAL_MOUNT_CONTAINER?: boolean | "suppress-warning" | undefined;
            GLOBAL_EXTEND?: boolean | "suppress-warning" | undefined;
            GLOBAL_PROTOTYPE?: boolean | "suppress-warning" | undefined;
            GLOBAL_SET?: boolean | "suppress-warning" | undefined;
            GLOBAL_DELETE?: boolean | "suppress-warning" | undefined;
            GLOBAL_OBSERVABLE?: boolean | "suppress-warning" | undefined;
            GLOBAL_PRIVATE_UTIL?: boolean | "suppress-warning" | undefined;
            CONFIG_SILENT?: boolean | "suppress-warning" | undefined;
            CONFIG_DEVTOOLS?: boolean | "suppress-warning" | undefined;
            CONFIG_KEY_CODES?: boolean | "suppress-warning" | undefined;
            CONFIG_PRODUCTION_TIP?: boolean | "suppress-warning" | undefined;
            CONFIG_IGNORED_ELEMENTS?: boolean | "suppress-warning" | undefined;
            CONFIG_WHITESPACE?: boolean | "suppress-warning" | undefined;
            CONFIG_OPTION_MERGE_STRATS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SET?: boolean | "suppress-warning" | undefined;
            INSTANCE_DELETE?: boolean | "suppress-warning" | undefined;
            INSTANCE_DESTROY?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_EMITTER?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_HOOKS?: boolean | "suppress-warning" | undefined;
            INSTANCE_CHILDREN?: boolean | "suppress-warning" | undefined;
            INSTANCE_LISTENERS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SCOPED_SLOTS?: boolean | "suppress-warning" | undefined;
            INSTANCE_ATTRS_CLASS_STYLE?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_FN?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_MERGE?: boolean | "suppress-warning" | undefined;
            OPTIONS_BEFORE_DESTROY?: boolean | "suppress-warning" | undefined;
            OPTIONS_DESTROYED?: boolean | "suppress-warning" | undefined;
            WATCH_ARRAY?: boolean | "suppress-warning" | undefined;
            PROPS_DEFAULT_THIS?: boolean | "suppress-warning" | undefined;
            V_ON_KEYCODE_MODIFIER?: boolean | "suppress-warning" | undefined;
            CUSTOM_DIR?: boolean | "suppress-warning" | undefined;
            ATTR_FALSE_VALUE?: boolean | "suppress-warning" | undefined;
            ATTR_ENUMERATED_COERCION?: boolean | "suppress-warning" | undefined;
            TRANSITION_CLASSES?: boolean | "suppress-warning" | undefined;
            TRANSITION_GROUP_ROOT?: boolean | "suppress-warning" | undefined;
            COMPONENT_ASYNC?: boolean | "suppress-warning" | undefined;
            COMPONENT_FUNCTIONAL?: boolean | "suppress-warning" | undefined;
            COMPONENT_V_MODEL?: boolean | "suppress-warning" | undefined;
            RENDER_FUNCTION?: boolean | "suppress-warning" | undefined;
            FILTERS?: boolean | "suppress-warning" | undefined;
            PRIVATE_APIS?: boolean | "suppress-warning" | undefined;
            MODE?: (2 | 3 | ((comp: import("vue").Component | null) => 2 | 3)) | undefined;
        } | undefined;
        data?: ((this: any, vm: any) => any) | undefined;
        computed?: import("vue").ComputedOptions | undefined;
        methods?: import("vue").MethodOptions | undefined;
        watch?: {
            [x: string]: (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>)) | (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>))[];
        } | undefined;
        provide?: import("vue").ComponentProvideOptions | undefined;
        inject?: (string[] | {
            [x: string]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
            [x: symbol]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
        }) | undefined;
        filters?: Record<string, Function> | undefined;
        mixins?: any[] | undefined;
        extends?: any;
        beforeCreate?: (() => void) | undefined;
        created?: (() => void) | undefined;
        beforeMount?: (() => void) | undefined;
        mounted?: (() => void) | undefined;
        beforeUpdate?: (() => void) | undefined;
        updated?: (() => void) | undefined;
        activated?: (() => void) | undefined;
        deactivated?: (() => void) | undefined;
        beforeDestroy?: (() => void) | undefined;
        beforeUnmount?: (() => void) | undefined;
        destroyed?: (() => void) | undefined;
        unmounted?: (() => void) | undefined;
        renderTracked?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | undefined;
        delimiters?: [string, string] | undefined;
        __differentiator?: string | number | symbol | undefined;
        __isBuiltIn?: boolean | undefined;
        __file?: string | undefined;
        __name?: string | undefined;
        beforeRouteEnter?: import("vue-router").NavigationGuardWithThis<undefined> | undefined;
        beforeRouteUpdate?: import("vue-router").NavigationGuard | undefined;
        beforeRouteLeave?: import("vue-router").NavigationGuard | undefined;
    }>;
    closeIcon: import("vue").Ref<string | import("vue").FunctionalComponent<any, any> | {
        new (...args: any[]): any;
        __isFragment?: never;
        __isTeleport?: never;
        __isSuspense?: never;
    } | {
        [x: string]: any;
        setup?: ((this: void, props: Readonly<import("@vue/shared").LooseRequired<any>>, ctx: import("vue").SetupContext<any>) => any) | undefined;
        name?: string | undefined;
        template?: (string | object) | undefined;
        render?: Function | undefined;
        components?: Record<string, import("vue").Component> | undefined;
        directives?: Record<string, import("vue").Directive> | undefined;
        inheritAttrs?: boolean | undefined;
        emits?: any;
        expose?: string[] | undefined;
        serverPrefetch?: (() => Promise<any>) | undefined;
        compilerOptions?: {
            isCustomElement?: ((tag: string) => boolean) | undefined;
            whitespace?: ("preserve" | "condense") | undefined;
            comments?: boolean | undefined;
            delimiters?: [string, string] | undefined;
        } | undefined;
        call?: ((this: unknown, ...args: unknown[]) => never) | undefined;
        __isFragment?: never | undefined;
        __isTeleport?: never | undefined;
        __isSuspense?: never | undefined;
        __defaults?: {} | undefined;
        compatConfig?: {
            GLOBAL_MOUNT?: boolean | "suppress-warning" | undefined;
            GLOBAL_MOUNT_CONTAINER?: boolean | "suppress-warning" | undefined;
            GLOBAL_EXTEND?: boolean | "suppress-warning" | undefined;
            GLOBAL_PROTOTYPE?: boolean | "suppress-warning" | undefined;
            GLOBAL_SET?: boolean | "suppress-warning" | undefined;
            GLOBAL_DELETE?: boolean | "suppress-warning" | undefined;
            GLOBAL_OBSERVABLE?: boolean | "suppress-warning" | undefined;
            GLOBAL_PRIVATE_UTIL?: boolean | "suppress-warning" | undefined;
            CONFIG_SILENT?: boolean | "suppress-warning" | undefined;
            CONFIG_DEVTOOLS?: boolean | "suppress-warning" | undefined;
            CONFIG_KEY_CODES?: boolean | "suppress-warning" | undefined;
            CONFIG_PRODUCTION_TIP?: boolean | "suppress-warning" | undefined;
            CONFIG_IGNORED_ELEMENTS?: boolean | "suppress-warning" | undefined;
            CONFIG_WHITESPACE?: boolean | "suppress-warning" | undefined;
            CONFIG_OPTION_MERGE_STRATS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SET?: boolean | "suppress-warning" | undefined;
            INSTANCE_DELETE?: boolean | "suppress-warning" | undefined;
            INSTANCE_DESTROY?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_EMITTER?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_HOOKS?: boolean | "suppress-warning" | undefined;
            INSTANCE_CHILDREN?: boolean | "suppress-warning" | undefined;
            INSTANCE_LISTENERS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SCOPED_SLOTS?: boolean | "suppress-warning" | undefined;
            INSTANCE_ATTRS_CLASS_STYLE?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_FN?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_MERGE?: boolean | "suppress-warning" | undefined;
            OPTIONS_BEFORE_DESTROY?: boolean | "suppress-warning" | undefined;
            OPTIONS_DESTROYED?: boolean | "suppress-warning" | undefined;
            WATCH_ARRAY?: boolean | "suppress-warning" | undefined;
            PROPS_DEFAULT_THIS?: boolean | "suppress-warning" | undefined;
            V_ON_KEYCODE_MODIFIER?: boolean | "suppress-warning" | undefined;
            CUSTOM_DIR?: boolean | "suppress-warning" | undefined;
            ATTR_FALSE_VALUE?: boolean | "suppress-warning" | undefined;
            ATTR_ENUMERATED_COERCION?: boolean | "suppress-warning" | undefined;
            TRANSITION_CLASSES?: boolean | "suppress-warning" | undefined;
            TRANSITION_GROUP_ROOT?: boolean | "suppress-warning" | undefined;
            COMPONENT_ASYNC?: boolean | "suppress-warning" | undefined;
            COMPONENT_FUNCTIONAL?: boolean | "suppress-warning" | undefined;
            COMPONENT_V_MODEL?: boolean | "suppress-warning" | undefined;
            RENDER_FUNCTION?: boolean | "suppress-warning" | undefined;
            FILTERS?: boolean | "suppress-warning" | undefined;
            PRIVATE_APIS?: boolean | "suppress-warning" | undefined;
            MODE?: (2 | 3 | ((comp: import("vue").Component | null) => 2 | 3)) | undefined;
        } | undefined;
        data?: ((this: any, vm: any) => any) | undefined;
        computed?: import("vue").ComputedOptions | undefined;
        methods?: import("vue").MethodOptions | undefined;
        watch?: {
            [x: string]: (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>)) | (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>))[];
        } | undefined;
        provide?: import("vue").ComponentProvideOptions | undefined;
        inject?: (string[] | {
            [x: string]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
            [x: symbol]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
        }) | undefined;
        filters?: Record<string, Function> | undefined;
        mixins?: any[] | undefined;
        extends?: any;
        beforeCreate?: (() => void) | undefined;
        created?: (() => void) | undefined;
        beforeMount?: (() => void) | undefined;
        mounted?: (() => void) | undefined;
        beforeUpdate?: (() => void) | undefined;
        updated?: (() => void) | undefined;
        activated?: (() => void) | undefined;
        deactivated?: (() => void) | undefined;
        beforeDestroy?: (() => void) | undefined;
        beforeUnmount?: (() => void) | undefined;
        destroyed?: (() => void) | undefined;
        unmounted?: (() => void) | undefined;
        renderTracked?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | undefined;
        delimiters?: [string, string] | undefined;
        __differentiator?: string | number | symbol | undefined;
        __isBuiltIn?: boolean | undefined;
        __file?: string | undefined;
        __name?: string | undefined;
        beforeRouteEnter?: import("vue-router").NavigationGuardWithThis<undefined> | undefined;
        beforeRouteUpdate?: import("vue-router").NavigationGuard | undefined;
        beforeRouteLeave?: import("vue-router").NavigationGuard | undefined;
    }>;
    customClass: import("vue").Ref<string>;
    customStyle: import("vue").Ref<{
        [x: `--${string}`]: string | number | undefined;
        accentColor?: import("csstype").AccentColorProperty | undefined;
        alignContent?: import("csstype").AlignContentProperty | undefined;
        alignItems?: import("csstype").AlignItemsProperty | undefined;
        alignSelf?: import("csstype").AlignSelfProperty | undefined;
        alignTracks?: import("csstype").AlignTracksProperty | undefined;
        animationDelay?: import("csstype").GlobalsString | undefined;
        animationDirection?: import("csstype").AnimationDirectionProperty | undefined;
        animationDuration?: import("csstype").GlobalsString | undefined;
        animationFillMode?: import("csstype").AnimationFillModeProperty | undefined;
        animationIterationCount?: import("csstype").AnimationIterationCountProperty | undefined;
        animationName?: import("csstype").AnimationNameProperty | undefined;
        animationPlayState?: import("csstype").AnimationPlayStateProperty | undefined;
        animationTimeline?: import("csstype").AnimationTimelineProperty | undefined;
        animationTimingFunction?: import("csstype").AnimationTimingFunctionProperty | undefined;
        appearance?: import("csstype").AppearanceProperty | undefined;
        aspectRatio?: import("csstype").AspectRatioProperty | undefined;
        backdropFilter?: import("csstype").BackdropFilterProperty | undefined;
        backfaceVisibility?: import("csstype").BackfaceVisibilityProperty | undefined;
        backgroundAttachment?: import("csstype").BackgroundAttachmentProperty | undefined;
        backgroundBlendMode?: import("csstype").BackgroundBlendModeProperty | undefined;
        backgroundClip?: import("csstype").BackgroundClipProperty | undefined;
        backgroundColor?: import("csstype").BackgroundColorProperty | undefined;
        backgroundImage?: import("csstype").BackgroundImageProperty | undefined;
        backgroundOrigin?: import("csstype").BackgroundOriginProperty | undefined;
        backgroundPositionX?: import("csstype").BackgroundPositionXProperty<string | number> | undefined;
        backgroundPositionY?: import("csstype").BackgroundPositionYProperty<string | number> | undefined;
        backgroundRepeat?: import("csstype").BackgroundRepeatProperty | undefined;
        backgroundSize?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        blockOverflow?: import("csstype").BlockOverflowProperty | undefined;
        blockSize?: import("csstype").BlockSizeProperty<string | number> | undefined;
        borderBlockColor?: import("csstype").BorderBlockColorProperty | undefined;
        borderBlockEndColor?: import("csstype").BorderBlockEndColorProperty | undefined;
        borderBlockEndStyle?: import("csstype").BorderBlockEndStyleProperty | undefined;
        borderBlockEndWidth?: import("csstype").BorderBlockEndWidthProperty<string | number> | undefined;
        borderBlockStartColor?: import("csstype").BorderBlockStartColorProperty | undefined;
        borderBlockStartStyle?: import("csstype").BorderBlockStartStyleProperty | undefined;
        borderBlockStartWidth?: import("csstype").BorderBlockStartWidthProperty<string | number> | undefined;
        borderBlockStyle?: import("csstype").BorderBlockStyleProperty | undefined;
        borderBlockWidth?: import("csstype").BorderBlockWidthProperty<string | number> | undefined;
        borderBottomColor?: import("csstype").BorderBottomColorProperty | undefined;
        borderBottomLeftRadius?: import("csstype").BorderBottomLeftRadiusProperty<string | number> | undefined;
        borderBottomRightRadius?: import("csstype").BorderBottomRightRadiusProperty<string | number> | undefined;
        borderBottomStyle?: import("csstype").BorderBottomStyleProperty | undefined;
        borderBottomWidth?: import("csstype").BorderBottomWidthProperty<string | number> | undefined;
        borderCollapse?: import("csstype").BorderCollapseProperty | undefined;
        borderEndEndRadius?: import("csstype").BorderEndEndRadiusProperty<string | number> | undefined;
        borderEndStartRadius?: import("csstype").BorderEndStartRadiusProperty<string | number> | undefined;
        borderImageOutset?: import("csstype").BorderImageOutsetProperty<string | number> | undefined;
        borderImageRepeat?: import("csstype").BorderImageRepeatProperty | undefined;
        borderImageSlice?: import("csstype").BorderImageSliceProperty | undefined;
        borderImageSource?: import("csstype").BorderImageSourceProperty | undefined;
        borderImageWidth?: import("csstype").BorderImageWidthProperty<string | number> | undefined;
        borderInlineColor?: import("csstype").BorderInlineColorProperty | undefined;
        borderInlineEndColor?: import("csstype").BorderInlineEndColorProperty | undefined;
        borderInlineEndStyle?: import("csstype").BorderInlineEndStyleProperty | undefined;
        borderInlineEndWidth?: import("csstype").BorderInlineEndWidthProperty<string | number> | undefined;
        borderInlineStartColor?: import("csstype").BorderInlineStartColorProperty | undefined;
        borderInlineStartStyle?: import("csstype").BorderInlineStartStyleProperty | undefined;
        borderInlineStartWidth?: import("csstype").BorderInlineStartWidthProperty<string | number> | undefined;
        borderInlineStyle?: import("csstype").BorderInlineStyleProperty | undefined;
        borderInlineWidth?: import("csstype").BorderInlineWidthProperty<string | number> | undefined;
        borderLeftColor?: import("csstype").BorderLeftColorProperty | undefined;
        borderLeftStyle?: import("csstype").BorderLeftStyleProperty | undefined;
        borderLeftWidth?: import("csstype").BorderLeftWidthProperty<string | number> | undefined;
        borderRightColor?: import("csstype").BorderRightColorProperty | undefined;
        borderRightStyle?: import("csstype").BorderRightStyleProperty | undefined;
        borderRightWidth?: import("csstype").BorderRightWidthProperty<string | number> | undefined;
        borderSpacing?: import("csstype").BorderSpacingProperty<string | number> | undefined;
        borderStartEndRadius?: import("csstype").BorderStartEndRadiusProperty<string | number> | undefined;
        borderStartStartRadius?: import("csstype").BorderStartStartRadiusProperty<string | number> | undefined;
        borderTopColor?: import("csstype").BorderTopColorProperty | undefined;
        borderTopLeftRadius?: import("csstype").BorderTopLeftRadiusProperty<string | number> | undefined;
        borderTopRightRadius?: import("csstype").BorderTopRightRadiusProperty<string | number> | undefined;
        borderTopStyle?: import("csstype").BorderTopStyleProperty | undefined;
        borderTopWidth?: import("csstype").BorderTopWidthProperty<string | number> | undefined;
        bottom?: import("csstype").BottomProperty<string | number> | undefined;
        boxDecorationBreak?: import("csstype").BoxDecorationBreakProperty | undefined;
        boxShadow?: import("csstype").BoxShadowProperty | undefined;
        boxSizing?: import("csstype").BoxSizingProperty | undefined;
        breakAfter?: import("csstype").BreakAfterProperty | undefined;
        breakBefore?: import("csstype").BreakBeforeProperty | undefined;
        breakInside?: import("csstype").BreakInsideProperty | undefined;
        captionSide?: import("csstype").CaptionSideProperty | undefined;
        caretColor?: import("csstype").CaretColorProperty | undefined;
        clear?: import("csstype").ClearProperty | undefined;
        clipPath?: import("csstype").ClipPathProperty | undefined;
        color?: import("csstype").ColorProperty | undefined;
        colorAdjust?: import("csstype").PrintColorAdjustProperty | undefined;
        colorScheme?: import("csstype").ColorSchemeProperty | undefined;
        columnCount?: import("csstype").ColumnCountProperty | undefined;
        columnFill?: import("csstype").ColumnFillProperty | undefined;
        columnGap?: import("csstype").ColumnGapProperty<string | number> | undefined;
        columnRuleColor?: import("csstype").ColumnRuleColorProperty | undefined;
        columnRuleStyle?: import("csstype").ColumnRuleStyleProperty | undefined;
        columnRuleWidth?: import("csstype").ColumnRuleWidthProperty<string | number> | undefined;
        columnSpan?: import("csstype").ColumnSpanProperty | undefined;
        columnWidth?: import("csstype").ColumnWidthProperty<string | number> | undefined;
        contain?: import("csstype").ContainProperty | undefined;
        content?: import("csstype").ContentProperty | undefined;
        contentVisibility?: import("csstype").ContentVisibilityProperty | undefined;
        counterIncrement?: import("csstype").CounterIncrementProperty | undefined;
        counterReset?: import("csstype").CounterResetProperty | undefined;
        counterSet?: import("csstype").CounterSetProperty | undefined;
        cursor?: import("csstype").CursorProperty | undefined;
        direction?: import("csstype").DirectionProperty | undefined;
        display?: import("csstype").DisplayProperty | undefined;
        emptyCells?: import("csstype").EmptyCellsProperty | undefined;
        filter?: import("csstype").FilterProperty | undefined;
        flexBasis?: import("csstype").FlexBasisProperty<string | number> | undefined;
        flexDirection?: import("csstype").FlexDirectionProperty | undefined;
        flexGrow?: import("csstype").GlobalsNumber | undefined;
        flexShrink?: import("csstype").GlobalsNumber | undefined;
        flexWrap?: import("csstype").FlexWrapProperty | undefined;
        float?: import("csstype").FloatProperty | undefined;
        fontFamily?: import("csstype").FontFamilyProperty | undefined;
        fontFeatureSettings?: import("csstype").FontFeatureSettingsProperty | undefined;
        fontKerning?: import("csstype").FontKerningProperty | undefined;
        fontLanguageOverride?: import("csstype").FontLanguageOverrideProperty | undefined;
        fontOpticalSizing?: import("csstype").FontOpticalSizingProperty | undefined;
        fontSize?: import("csstype").FontSizeProperty<string | number> | undefined;
        fontSizeAdjust?: import("csstype").FontSizeAdjustProperty | undefined;
        fontSmooth?: import("csstype").FontSmoothProperty<string | number> | undefined;
        fontStretch?: import("csstype").FontStretchProperty | undefined;
        fontStyle?: import("csstype").FontStyleProperty | undefined;
        fontSynthesis?: import("csstype").FontSynthesisProperty | undefined;
        fontVariant?: import("csstype").FontVariantProperty | undefined;
        fontVariantAlternates?: import("csstype").FontVariantAlternatesProperty | undefined;
        fontVariantCaps?: import("csstype").FontVariantCapsProperty | undefined;
        fontVariantEastAsian?: import("csstype").FontVariantEastAsianProperty | undefined;
        fontVariantLigatures?: import("csstype").FontVariantLigaturesProperty | undefined;
        fontVariantNumeric?: import("csstype").FontVariantNumericProperty | undefined;
        fontVariantPosition?: import("csstype").FontVariantPositionProperty | undefined;
        fontVariationSettings?: import("csstype").FontVariationSettingsProperty | undefined;
        fontWeight?: import("csstype").FontWeightProperty | undefined;
        forcedColorAdjust?: import("csstype").ForcedColorAdjustProperty | undefined;
        gridAutoColumns?: import("csstype").GridAutoColumnsProperty<string | number> | undefined;
        gridAutoFlow?: import("csstype").GridAutoFlowProperty | undefined;
        gridAutoRows?: import("csstype").GridAutoRowsProperty<string | number> | undefined;
        gridColumnEnd?: import("csstype").GridColumnEndProperty | undefined;
        gridColumnStart?: import("csstype").GridColumnStartProperty | undefined;
        gridRowEnd?: import("csstype").GridRowEndProperty | undefined;
        gridRowStart?: import("csstype").GridRowStartProperty | undefined;
        gridTemplateAreas?: import("csstype").GridTemplateAreasProperty | undefined;
        gridTemplateColumns?: import("csstype").GridTemplateColumnsProperty<string | number> | undefined;
        gridTemplateRows?: import("csstype").GridTemplateRowsProperty<string | number> | undefined;
        hangingPunctuation?: import("csstype").HangingPunctuationProperty | undefined;
        height?: import("csstype").HeightProperty<string | number> | undefined;
        hyphenateCharacter?: import("csstype").HyphenateCharacterProperty | undefined;
        hyphens?: import("csstype").HyphensProperty | undefined;
        imageOrientation?: import("csstype").ImageOrientationProperty | undefined;
        imageRendering?: import("csstype").ImageRenderingProperty | undefined;
        imageResolution?: import("csstype").ImageResolutionProperty | undefined;
        initialLetter?: import("csstype").InitialLetterProperty | undefined;
        inlineSize?: import("csstype").InlineSizeProperty<string | number> | undefined;
        inputSecurity?: import("csstype").InputSecurityProperty | undefined;
        inset?: import("csstype").InsetProperty<string | number> | undefined;
        insetBlock?: import("csstype").InsetBlockProperty<string | number> | undefined;
        insetBlockEnd?: import("csstype").InsetBlockEndProperty<string | number> | undefined;
        insetBlockStart?: import("csstype").InsetBlockStartProperty<string | number> | undefined;
        insetInline?: import("csstype").InsetInlineProperty<string | number> | undefined;
        insetInlineEnd?: import("csstype").InsetInlineEndProperty<string | number> | undefined;
        insetInlineStart?: import("csstype").InsetInlineStartProperty<string | number> | undefined;
        isolation?: import("csstype").IsolationProperty | undefined;
        justifyContent?: import("csstype").JustifyContentProperty | undefined;
        justifyItems?: import("csstype").JustifyItemsProperty | undefined;
        justifySelf?: import("csstype").JustifySelfProperty | undefined;
        justifyTracks?: import("csstype").JustifyTracksProperty | undefined;
        left?: import("csstype").LeftProperty<string | number> | undefined;
        letterSpacing?: import("csstype").LetterSpacingProperty<string | number> | undefined;
        lineBreak?: import("csstype").LineBreakProperty | undefined;
        lineHeight?: import("csstype").LineHeightProperty<string | number> | undefined;
        lineHeightStep?: import("csstype").LineHeightStepProperty<string | number> | undefined;
        listStyleImage?: import("csstype").ListStyleImageProperty | undefined;
        listStylePosition?: import("csstype").ListStylePositionProperty | undefined;
        listStyleType?: import("csstype").ListStyleTypeProperty | undefined;
        marginBlock?: import("csstype").MarginBlockProperty<string | number> | undefined;
        marginBlockEnd?: import("csstype").MarginBlockEndProperty<string | number> | undefined;
        marginBlockStart?: import("csstype").MarginBlockStartProperty<string | number> | undefined;
        marginBottom?: import("csstype").MarginBottomProperty<string | number> | undefined;
        marginInline?: import("csstype").MarginInlineProperty<string | number> | undefined;
        marginInlineEnd?: import("csstype").MarginInlineEndProperty<string | number> | undefined;
        marginInlineStart?: import("csstype").MarginInlineStartProperty<string | number> | undefined;
        marginLeft?: import("csstype").MarginLeftProperty<string | number> | undefined;
        marginRight?: import("csstype").MarginRightProperty<string | number> | undefined;
        marginTop?: import("csstype").MarginTopProperty<string | number> | undefined;
        maskBorderMode?: import("csstype").MaskBorderModeProperty | undefined;
        maskBorderOutset?: import("csstype").MaskBorderOutsetProperty<string | number> | undefined;
        maskBorderRepeat?: import("csstype").MaskBorderRepeatProperty | undefined;
        maskBorderSlice?: import("csstype").MaskBorderSliceProperty | undefined;
        maskBorderSource?: import("csstype").MaskBorderSourceProperty | undefined;
        maskBorderWidth?: import("csstype").MaskBorderWidthProperty<string | number> | undefined;
        maskClip?: import("csstype").MaskClipProperty | undefined;
        maskComposite?: import("csstype").MaskCompositeProperty | undefined;
        maskImage?: import("csstype").MaskImageProperty | undefined;
        maskMode?: import("csstype").MaskModeProperty | undefined;
        maskOrigin?: import("csstype").MaskOriginProperty | undefined;
        maskPosition?: import("csstype").MaskPositionProperty<string | number> | undefined;
        maskRepeat?: import("csstype").MaskRepeatProperty | undefined;
        maskSize?: import("csstype").MaskSizeProperty<string | number> | undefined;
        maskType?: import("csstype").MaskTypeProperty | undefined;
        mathStyle?: import("csstype").MathStyleProperty | undefined;
        maxBlockSize?: import("csstype").MaxBlockSizeProperty<string | number> | undefined;
        maxHeight?: import("csstype").MaxHeightProperty<string | number> | undefined;
        maxInlineSize?: import("csstype").MaxInlineSizeProperty<string | number> | undefined;
        maxLines?: import("csstype").MaxLinesProperty | undefined;
        maxWidth?: import("csstype").MaxWidthProperty<string | number> | undefined;
        minBlockSize?: import("csstype").MinBlockSizeProperty<string | number> | undefined;
        minHeight?: import("csstype").MinHeightProperty<string | number> | undefined;
        minInlineSize?: import("csstype").MinInlineSizeProperty<string | number> | undefined;
        minWidth?: import("csstype").MinWidthProperty<string | number> | undefined;
        mixBlendMode?: import("csstype").MixBlendModeProperty | undefined;
        motionDistance?: import("csstype").OffsetDistanceProperty<string | number> | undefined;
        motionPath?: import("csstype").OffsetPathProperty | undefined;
        motionRotation?: import("csstype").OffsetRotateProperty | undefined;
        objectFit?: import("csstype").ObjectFitProperty | undefined;
        objectPosition?: import("csstype").ObjectPositionProperty<string | number> | undefined;
        offsetAnchor?: import("csstype").OffsetAnchorProperty<string | number> | undefined;
        offsetDistance?: import("csstype").OffsetDistanceProperty<string | number> | undefined;
        offsetPath?: import("csstype").OffsetPathProperty | undefined;
        offsetRotate?: import("csstype").OffsetRotateProperty | undefined;
        offsetRotation?: import("csstype").OffsetRotateProperty | undefined;
        opacity?: import("csstype").OpacityProperty | undefined;
        order?: import("csstype").GlobalsNumber | undefined;
        orphans?: import("csstype").GlobalsNumber | undefined;
        outlineColor?: import("csstype").OutlineColorProperty | undefined;
        outlineOffset?: import("csstype").OutlineOffsetProperty<string | number> | undefined;
        outlineStyle?: import("csstype").OutlineStyleProperty | undefined;
        outlineWidth?: import("csstype").OutlineWidthProperty<string | number> | undefined;
        overflowAnchor?: import("csstype").OverflowAnchorProperty | undefined;
        overflowBlock?: import("csstype").OverflowBlockProperty | undefined;
        overflowClipBox?: import("csstype").OverflowClipBoxProperty | undefined;
        overflowClipMargin?: import("csstype").OverflowClipMarginProperty<string | number> | undefined;
        overflowInline?: import("csstype").OverflowInlineProperty | undefined;
        overflowWrap?: import("csstype").OverflowWrapProperty | undefined;
        overflowX?: import("csstype").OverflowXProperty | undefined;
        overflowY?: import("csstype").OverflowYProperty | undefined;
        overscrollBehaviorBlock?: import("csstype").OverscrollBehaviorBlockProperty | undefined;
        overscrollBehaviorInline?: import("csstype").OverscrollBehaviorInlineProperty | undefined;
        overscrollBehaviorX?: import("csstype").OverscrollBehaviorXProperty | undefined;
        overscrollBehaviorY?: import("csstype").OverscrollBehaviorYProperty | undefined;
        paddingBlock?: import("csstype").PaddingBlockProperty<string | number> | undefined;
        paddingBlockEnd?: import("csstype").PaddingBlockEndProperty<string | number> | undefined;
        paddingBlockStart?: import("csstype").PaddingBlockStartProperty<string | number> | undefined;
        paddingBottom?: import("csstype").PaddingBottomProperty<string | number> | undefined;
        paddingInline?: import("csstype").PaddingInlineProperty<string | number> | undefined;
        paddingInlineEnd?: import("csstype").PaddingInlineEndProperty<string | number> | undefined;
        paddingInlineStart?: import("csstype").PaddingInlineStartProperty<string | number> | undefined;
        paddingLeft?: import("csstype").PaddingLeftProperty<string | number> | undefined;
        paddingRight?: import("csstype").PaddingRightProperty<string | number> | undefined;
        paddingTop?: import("csstype").PaddingTopProperty<string | number> | undefined;
        pageBreakAfter?: import("csstype").PageBreakAfterProperty | undefined;
        pageBreakBefore?: import("csstype").PageBreakBeforeProperty | undefined;
        pageBreakInside?: import("csstype").PageBreakInsideProperty | undefined;
        paintOrder?: import("csstype").PaintOrderProperty | undefined;
        perspective?: import("csstype").PerspectiveProperty<string | number> | undefined;
        perspectiveOrigin?: import("csstype").PerspectiveOriginProperty<string | number> | undefined;
        placeContent?: import("csstype").PlaceContentProperty | undefined;
        pointerEvents?: import("csstype").PointerEventsProperty | undefined;
        position?: import("csstype").PositionProperty | undefined;
        printColorAdjust?: import("csstype").PrintColorAdjustProperty | undefined;
        quotes?: import("csstype").QuotesProperty | undefined;
        resize?: import("csstype").ResizeProperty | undefined;
        right?: import("csstype").RightProperty<string | number> | undefined;
        rotate?: import("csstype").RotateProperty | undefined;
        rowGap?: import("csstype").RowGapProperty<string | number> | undefined;
        rubyAlign?: import("csstype").RubyAlignProperty | undefined;
        rubyMerge?: import("csstype").RubyMergeProperty | undefined;
        rubyPosition?: import("csstype").RubyPositionProperty | undefined;
        scale?: import("csstype").ScaleProperty | undefined;
        scrollBehavior?: import("csstype").ScrollBehaviorProperty | undefined;
        scrollMargin?: import("csstype").ScrollMarginProperty<string | number> | undefined;
        scrollMarginBlock?: import("csstype").ScrollMarginBlockProperty<string | number> | undefined;
        scrollMarginBlockEnd?: import("csstype").ScrollMarginBlockEndProperty<string | number> | undefined;
        scrollMarginBlockStart?: import("csstype").ScrollMarginBlockStartProperty<string | number> | undefined;
        scrollMarginBottom?: import("csstype").ScrollMarginBottomProperty<string | number> | undefined;
        scrollMarginInline?: import("csstype").ScrollMarginInlineProperty<string | number> | undefined;
        scrollMarginInlineEnd?: import("csstype").ScrollMarginInlineEndProperty<string | number> | undefined;
        scrollMarginInlineStart?: import("csstype").ScrollMarginInlineStartProperty<string | number> | undefined;
        scrollMarginLeft?: import("csstype").ScrollMarginLeftProperty<string | number> | undefined;
        scrollMarginRight?: import("csstype").ScrollMarginRightProperty<string | number> | undefined;
        scrollMarginTop?: import("csstype").ScrollMarginTopProperty<string | number> | undefined;
        scrollPadding?: import("csstype").ScrollPaddingProperty<string | number> | undefined;
        scrollPaddingBlock?: import("csstype").ScrollPaddingBlockProperty<string | number> | undefined;
        scrollPaddingBlockEnd?: import("csstype").ScrollPaddingBlockEndProperty<string | number> | undefined;
        scrollPaddingBlockStart?: import("csstype").ScrollPaddingBlockStartProperty<string | number> | undefined;
        scrollPaddingBottom?: import("csstype").ScrollPaddingBottomProperty<string | number> | undefined;
        scrollPaddingInline?: import("csstype").ScrollPaddingInlineProperty<string | number> | undefined;
        scrollPaddingInlineEnd?: import("csstype").ScrollPaddingInlineEndProperty<string | number> | undefined;
        scrollPaddingInlineStart?: import("csstype").ScrollPaddingInlineStartProperty<string | number> | undefined;
        scrollPaddingLeft?: import("csstype").ScrollPaddingLeftProperty<string | number> | undefined;
        scrollPaddingRight?: import("csstype").ScrollPaddingRightProperty<string | number> | undefined;
        scrollPaddingTop?: import("csstype").ScrollPaddingTopProperty<string | number> | undefined;
        scrollSnapAlign?: import("csstype").ScrollSnapAlignProperty | undefined;
        scrollSnapMargin?: import("csstype").ScrollMarginProperty<string | number> | undefined;
        scrollSnapMarginBottom?: import("csstype").ScrollMarginBottomProperty<string | number> | undefined;
        scrollSnapMarginLeft?: import("csstype").ScrollMarginLeftProperty<string | number> | undefined;
        scrollSnapMarginRight?: import("csstype").ScrollMarginRightProperty<string | number> | undefined;
        scrollSnapMarginTop?: import("csstype").ScrollMarginTopProperty<string | number> | undefined;
        scrollSnapStop?: import("csstype").ScrollSnapStopProperty | undefined;
        scrollSnapType?: import("csstype").ScrollSnapTypeProperty | undefined;
        scrollbarColor?: import("csstype").ScrollbarColorProperty | undefined;
        scrollbarGutter?: import("csstype").ScrollbarGutterProperty | undefined;
        scrollbarWidth?: import("csstype").ScrollbarWidthProperty | undefined;
        shapeImageThreshold?: import("csstype").ShapeImageThresholdProperty | undefined;
        shapeMargin?: import("csstype").ShapeMarginProperty<string | number> | undefined;
        shapeOutside?: import("csstype").ShapeOutsideProperty | undefined;
        tabSize?: import("csstype").TabSizeProperty<string | number> | undefined;
        tableLayout?: import("csstype").TableLayoutProperty | undefined;
        textAlign?: import("csstype").TextAlignProperty | undefined;
        textAlignLast?: import("csstype").TextAlignLastProperty | undefined;
        textCombineUpright?: import("csstype").TextCombineUprightProperty | undefined;
        textDecorationColor?: import("csstype").TextDecorationColorProperty | undefined;
        textDecorationLine?: import("csstype").TextDecorationLineProperty | undefined;
        textDecorationSkip?: import("csstype").TextDecorationSkipProperty | undefined;
        textDecorationSkipInk?: import("csstype").TextDecorationSkipInkProperty | undefined;
        textDecorationStyle?: import("csstype").TextDecorationStyleProperty | undefined;
        textDecorationThickness?: import("csstype").TextDecorationThicknessProperty<string | number> | undefined;
        textDecorationWidth?: import("csstype").TextDecorationThicknessProperty<string | number> | undefined;
        textEmphasisColor?: import("csstype").TextEmphasisColorProperty | undefined;
        textEmphasisPosition?: import("csstype").GlobalsString | undefined;
        textEmphasisStyle?: import("csstype").TextEmphasisStyleProperty | undefined;
        textIndent?: import("csstype").TextIndentProperty<string | number> | undefined;
        textJustify?: import("csstype").TextJustifyProperty | undefined;
        textOrientation?: import("csstype").TextOrientationProperty | undefined;
        textOverflow?: import("csstype").TextOverflowProperty | undefined;
        textRendering?: import("csstype").TextRenderingProperty | undefined;
        textShadow?: import("csstype").TextShadowProperty | undefined;
        textSizeAdjust?: import("csstype").TextSizeAdjustProperty | undefined;
        textTransform?: import("csstype").TextTransformProperty | undefined;
        textUnderlineOffset?: import("csstype").TextUnderlineOffsetProperty<string | number> | undefined;
        textUnderlinePosition?: import("csstype").TextUnderlinePositionProperty | undefined;
        top?: import("csstype").TopProperty<string | number> | undefined;
        touchAction?: import("csstype").TouchActionProperty | undefined;
        transform?: import("csstype").TransformProperty | undefined;
        transformBox?: import("csstype").TransformBoxProperty | undefined;
        transformOrigin?: import("csstype").TransformOriginProperty<string | number> | undefined;
        transformStyle?: import("csstype").TransformStyleProperty | undefined;
        transitionDelay?: import("csstype").GlobalsString | undefined;
        transitionDuration?: import("csstype").GlobalsString | undefined;
        transitionProperty?: import("csstype").TransitionPropertyProperty | undefined;
        transitionTimingFunction?: import("csstype").TransitionTimingFunctionProperty | undefined;
        translate?: import("csstype").TranslateProperty<string | number> | undefined;
        unicodeBidi?: import("csstype").UnicodeBidiProperty | undefined;
        userSelect?: import("csstype").UserSelectProperty | undefined;
        verticalAlign?: import("csstype").VerticalAlignProperty<string | number> | undefined;
        visibility?: import("csstype").VisibilityProperty | undefined;
        whiteSpace?: import("csstype").WhiteSpaceProperty | undefined;
        widows?: import("csstype").GlobalsNumber | undefined;
        width?: import("csstype").WidthProperty<string | number> | undefined;
        willChange?: import("csstype").WillChangeProperty | undefined;
        wordBreak?: import("csstype").WordBreakProperty | undefined;
        wordSpacing?: import("csstype").WordSpacingProperty<string | number> | undefined;
        wordWrap?: import("csstype").WordWrapProperty | undefined;
        writingMode?: import("csstype").WritingModeProperty | undefined;
        zIndex?: import("csstype").ZIndexProperty | undefined;
        zoom?: import("csstype").ZoomProperty | undefined;
        all?: import("csstype").Globals | undefined;
        animation?: import("csstype").AnimationProperty | undefined;
        background?: import("csstype").BackgroundProperty<string | number> | undefined;
        backgroundPosition?: import("csstype").BackgroundPositionProperty<string | number> | undefined;
        border?: import("csstype").BorderProperty<string | number> | undefined;
        borderBlock?: import("csstype").BorderBlockProperty<string | number> | undefined;
        borderBlockEnd?: import("csstype").BorderBlockEndProperty<string | number> | undefined;
        borderBlockStart?: import("csstype").BorderBlockStartProperty<string | number> | undefined;
        borderBottom?: import("csstype").BorderBottomProperty<string | number> | undefined;
        borderColor?: import("csstype").BorderColorProperty | undefined;
        borderImage?: import("csstype").BorderImageProperty | undefined;
        borderInline?: import("csstype").BorderInlineProperty<string | number> | undefined;
        borderInlineEnd?: import("csstype").BorderInlineEndProperty<string | number> | undefined;
        borderInlineStart?: import("csstype").BorderInlineStartProperty<string | number> | undefined;
        borderLeft?: import("csstype").BorderLeftProperty<string | number> | undefined;
        borderRadius?: import("csstype").BorderRadiusProperty<string | number> | undefined;
        borderRight?: import("csstype").BorderRightProperty<string | number> | undefined;
        borderStyle?: import("csstype").BorderStyleProperty | undefined;
        borderTop?: import("csstype").BorderTopProperty<string | number> | undefined;
        borderWidth?: import("csstype").BorderWidthProperty<string | number> | undefined;
        columnRule?: import("csstype").ColumnRuleProperty<string | number> | undefined;
        columns?: import("csstype").ColumnsProperty<string | number> | undefined;
        flex?: import("csstype").FlexProperty<string | number> | undefined;
        flexFlow?: import("csstype").FlexFlowProperty | undefined;
        font?: import("csstype").FontProperty | undefined;
        gap?: import("csstype").GapProperty<string | number> | undefined;
        grid?: import("csstype").GridProperty | undefined;
        gridArea?: import("csstype").GridAreaProperty | undefined;
        gridColumn?: import("csstype").GridColumnProperty | undefined;
        gridRow?: import("csstype").GridRowProperty | undefined;
        gridTemplate?: import("csstype").GridTemplateProperty | undefined;
        lineClamp?: import("csstype").LineClampProperty | undefined;
        listStyle?: import("csstype").ListStyleProperty | undefined;
        margin?: import("csstype").MarginProperty<string | number> | undefined;
        mask?: import("csstype").MaskProperty<string | number> | undefined;
        maskBorder?: import("csstype").MaskBorderProperty | undefined;
        motion?: import("csstype").OffsetProperty<string | number> | undefined;
        offset?: import("csstype").OffsetProperty<string | number> | undefined;
        outline?: import("csstype").OutlineProperty<string | number> | undefined;
        overflow?: import("csstype").OverflowProperty | undefined;
        overscrollBehavior?: import("csstype").OverscrollBehaviorProperty | undefined;
        padding?: import("csstype").PaddingProperty<string | number> | undefined;
        placeItems?: import("csstype").PlaceItemsProperty | undefined;
        placeSelf?: import("csstype").PlaceSelfProperty | undefined;
        textDecoration?: import("csstype").TextDecorationProperty<string | number> | undefined;
        textEmphasis?: import("csstype").TextEmphasisProperty | undefined;
        transition?: import("csstype").TransitionProperty | undefined;
        MozAnimationDelay?: import("csstype").GlobalsString | undefined;
        MozAnimationDirection?: import("csstype").AnimationDirectionProperty | undefined;
        MozAnimationDuration?: import("csstype").GlobalsString | undefined;
        MozAnimationFillMode?: import("csstype").AnimationFillModeProperty | undefined;
        MozAnimationIterationCount?: import("csstype").AnimationIterationCountProperty | undefined;
        MozAnimationName?: import("csstype").AnimationNameProperty | undefined;
        MozAnimationPlayState?: import("csstype").AnimationPlayStateProperty | undefined;
        MozAnimationTimingFunction?: import("csstype").AnimationTimingFunctionProperty | undefined;
        MozAppearance?: import("csstype").MozAppearanceProperty | undefined;
        MozBackfaceVisibility?: import("csstype").BackfaceVisibilityProperty | undefined;
        MozBorderBottomColors?: import("csstype").MozBorderBottomColorsProperty | undefined;
        MozBorderEndColor?: import("csstype").BorderInlineEndColorProperty | undefined;
        MozBorderEndStyle?: import("csstype").BorderInlineEndStyleProperty | undefined;
        MozBorderEndWidth?: import("csstype").BorderInlineEndWidthProperty<string | number> | undefined;
        MozBorderLeftColors?: import("csstype").MozBorderLeftColorsProperty | undefined;
        MozBorderRightColors?: import("csstype").MozBorderRightColorsProperty | undefined;
        MozBorderStartColor?: import("csstype").BorderInlineStartColorProperty | undefined;
        MozBorderStartStyle?: import("csstype").BorderInlineStartStyleProperty | undefined;
        MozBorderTopColors?: import("csstype").MozBorderTopColorsProperty | undefined;
        MozBoxSizing?: import("csstype").BoxSizingProperty | undefined;
        MozColumnCount?: import("csstype").ColumnCountProperty | undefined;
        MozColumnFill?: import("csstype").ColumnFillProperty | undefined;
        MozColumnGap?: import("csstype").ColumnGapProperty<string | number> | undefined;
        MozColumnRuleColor?: import("csstype").ColumnRuleColorProperty | undefined;
        MozColumnRuleStyle?: import("csstype").ColumnRuleStyleProperty | undefined;
        MozColumnRuleWidth?: import("csstype").ColumnRuleWidthProperty<string | number> | undefined;
        MozColumnWidth?: import("csstype").ColumnWidthProperty<string | number> | undefined;
        MozContextProperties?: import("csstype").MozContextPropertiesProperty | undefined;
        MozFontFeatureSettings?: import("csstype").FontFeatureSettingsProperty | undefined;
        MozFontLanguageOverride?: import("csstype").FontLanguageOverrideProperty | undefined;
        MozHyphens?: import("csstype").HyphensProperty | undefined;
        MozImageRegion?: import("csstype").MozImageRegionProperty | undefined;
        MozMarginEnd?: import("csstype").MarginInlineEndProperty<string | number> | undefined;
        MozMarginStart?: import("csstype").MarginInlineStartProperty<string | number> | undefined;
        MozOrient?: import("csstype").MozOrientProperty | undefined;
        MozOsxFontSmoothing?: import("csstype").FontSmoothProperty<string | number> | undefined;
        MozPaddingEnd?: import("csstype").PaddingInlineEndProperty<string | number> | undefined;
        MozPaddingStart?: import("csstype").PaddingInlineStartProperty<string | number> | undefined;
        MozPerspective?: import("csstype").PerspectiveProperty<string | number> | undefined;
        MozPerspectiveOrigin?: import("csstype").PerspectiveOriginProperty<string | number> | undefined;
        MozStackSizing?: import("csstype").MozStackSizingProperty | undefined;
        MozTabSize?: import("csstype").TabSizeProperty<string | number> | undefined;
        MozTextBlink?: import("csstype").MozTextBlinkProperty | undefined;
        MozTextSizeAdjust?: import("csstype").TextSizeAdjustProperty | undefined;
        MozTransformOrigin?: import("csstype").TransformOriginProperty<string | number> | undefined;
        MozTransformStyle?: import("csstype").TransformStyleProperty | undefined;
        MozTransitionDelay?: import("csstype").GlobalsString | undefined;
        MozTransitionDuration?: import("csstype").GlobalsString | undefined;
        MozTransitionProperty?: import("csstype").TransitionPropertyProperty | undefined;
        MozTransitionTimingFunction?: import("csstype").TransitionTimingFunctionProperty | undefined;
        MozUserFocus?: import("csstype").MozUserFocusProperty | undefined;
        MozUserModify?: import("csstype").MozUserModifyProperty | undefined;
        MozUserSelect?: import("csstype").UserSelectProperty | undefined;
        MozWindowDragging?: import("csstype").MozWindowDraggingProperty | undefined;
        MozWindowShadow?: import("csstype").MozWindowShadowProperty | undefined;
        msAccelerator?: import("csstype").MsAcceleratorProperty | undefined;
        msAlignSelf?: import("csstype").AlignSelfProperty | undefined;
        msBlockProgression?: import("csstype").MsBlockProgressionProperty | undefined;
        msContentZoomChaining?: import("csstype").MsContentZoomChainingProperty | undefined;
        msContentZoomLimitMax?: import("csstype").GlobalsString | undefined;
        msContentZoomLimitMin?: import("csstype").GlobalsString | undefined;
        msContentZoomSnapPoints?: import("csstype").GlobalsString | undefined;
        msContentZoomSnapType?: import("csstype").MsContentZoomSnapTypeProperty | undefined;
        msContentZooming?: import("csstype").MsContentZoomingProperty | undefined;
        msFilter?: import("csstype").GlobalsString | undefined;
        msFlexDirection?: import("csstype").FlexDirectionProperty | undefined;
        msFlexPositive?: import("csstype").GlobalsNumber | undefined;
        msFlowFrom?: import("csstype").MsFlowFromProperty | undefined;
        msFlowInto?: import("csstype").MsFlowIntoProperty | undefined;
        msGridColumns?: import("csstype").MsGridColumnsProperty<string | number> | undefined;
        msGridRows?: import("csstype").MsGridRowsProperty<string | number> | undefined;
        msHighContrastAdjust?: import("csstype").MsHighContrastAdjustProperty | undefined;
        msHyphenateLimitChars?: import("csstype").MsHyphenateLimitCharsProperty | undefined;
        msHyphenateLimitLines?: import("csstype").MsHyphenateLimitLinesProperty | undefined;
        msHyphenateLimitZone?: import("csstype").MsHyphenateLimitZoneProperty<string | number> | undefined;
        msHyphens?: import("csstype").HyphensProperty | undefined;
        msImeAlign?: import("csstype").MsImeAlignProperty | undefined;
        msJustifySelf?: import("csstype").JustifySelfProperty | undefined;
        msLineBreak?: import("csstype").LineBreakProperty | undefined;
        msOrder?: import("csstype").GlobalsNumber | undefined;
        msOverflowStyle?: import("csstype").MsOverflowStyleProperty | undefined;
        msOverflowX?: import("csstype").OverflowXProperty | undefined;
        msOverflowY?: import("csstype").OverflowYProperty | undefined;
        msScrollChaining?: import("csstype").MsScrollChainingProperty | undefined;
        msScrollLimitXMax?: import("csstype").MsScrollLimitXMaxProperty<string | number> | undefined;
        msScrollLimitXMin?: import("csstype").MsScrollLimitXMinProperty<string | number> | undefined;
        msScrollLimitYMax?: import("csstype").MsScrollLimitYMaxProperty<string | number> | undefined;
        msScrollLimitYMin?: import("csstype").MsScrollLimitYMinProperty<string | number> | undefined;
        msScrollRails?: import("csstype").MsScrollRailsProperty | undefined;
        msScrollSnapPointsX?: import("csstype").GlobalsString | undefined;
        msScrollSnapPointsY?: import("csstype").GlobalsString | undefined;
        msScrollSnapType?: import("csstype").MsScrollSnapTypeProperty | undefined;
        msScrollTranslation?: import("csstype").MsScrollTranslationProperty | undefined;
        msScrollbar3dlightColor?: import("csstype").MsScrollbar3dlightColorProperty | undefined;
        msScrollbarArrowColor?: import("csstype").MsScrollbarArrowColorProperty | undefined;
        msScrollbarBaseColor?: import("csstype").MsScrollbarBaseColorProperty | undefined;
        msScrollbarDarkshadowColor?: import("csstype").MsScrollbarDarkshadowColorProperty | undefined;
        msScrollbarFaceColor?: import("csstype").MsScrollbarFaceColorProperty | undefined;
        msScrollbarHighlightColor?: import("csstype").MsScrollbarHighlightColorProperty | undefined;
        msScrollbarShadowColor?: import("csstype").MsScrollbarShadowColorProperty | undefined;
        msTextAutospace?: import("csstype").MsTextAutospaceProperty | undefined;
        msTextCombineHorizontal?: import("csstype").TextCombineUprightProperty | undefined;
        msTextOverflow?: import("csstype").TextOverflowProperty | undefined;
        msTouchAction?: import("csstype").TouchActionProperty | undefined;
        msTouchSelect?: import("csstype").MsTouchSelectProperty | undefined;
        msTransform?: import("csstype").TransformProperty | undefined;
        msTransformOrigin?: import("csstype").TransformOriginProperty<string | number> | undefined;
        msTransitionDelay?: import("csstype").GlobalsString | undefined;
        msTransitionDuration?: import("csstype").GlobalsString | undefined;
        msTransitionProperty?: import("csstype").TransitionPropertyProperty | undefined;
        msTransitionTimingFunction?: import("csstype").TransitionTimingFunctionProperty | undefined;
        msUserSelect?: import("csstype").MsUserSelectProperty | undefined;
        msWordBreak?: import("csstype").WordBreakProperty | undefined;
        msWrapFlow?: import("csstype").MsWrapFlowProperty | undefined;
        msWrapMargin?: import("csstype").MsWrapMarginProperty<string | number> | undefined;
        msWrapThrough?: import("csstype").MsWrapThroughProperty | undefined;
        msWritingMode?: import("csstype").WritingModeProperty | undefined;
        WebkitAlignContent?: import("csstype").AlignContentProperty | undefined;
        WebkitAlignItems?: import("csstype").AlignItemsProperty | undefined;
        WebkitAlignSelf?: import("csstype").AlignSelfProperty | undefined;
        WebkitAnimationDelay?: import("csstype").GlobalsString | undefined;
        WebkitAnimationDirection?: import("csstype").AnimationDirectionProperty | undefined;
        WebkitAnimationDuration?: import("csstype").GlobalsString | undefined;
        WebkitAnimationFillMode?: import("csstype").AnimationFillModeProperty | undefined;
        WebkitAnimationIterationCount?: import("csstype").AnimationIterationCountProperty | undefined;
        WebkitAnimationName?: import("csstype").AnimationNameProperty | undefined;
        WebkitAnimationPlayState?: import("csstype").AnimationPlayStateProperty | undefined;
        WebkitAnimationTimingFunction?: import("csstype").AnimationTimingFunctionProperty | undefined;
        WebkitAppearance?: import("csstype").WebkitAppearanceProperty | undefined;
        WebkitBackdropFilter?: import("csstype").BackdropFilterProperty | undefined;
        WebkitBackfaceVisibility?: import("csstype").BackfaceVisibilityProperty | undefined;
        WebkitBackgroundClip?: import("csstype").BackgroundClipProperty | undefined;
        WebkitBackgroundOrigin?: import("csstype").BackgroundOriginProperty | undefined;
        WebkitBackgroundSize?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        WebkitBorderBeforeColor?: import("csstype").WebkitBorderBeforeColorProperty | undefined;
        WebkitBorderBeforeStyle?: import("csstype").WebkitBorderBeforeStyleProperty | undefined;
        WebkitBorderBeforeWidth?: import("csstype").WebkitBorderBeforeWidthProperty<string | number> | undefined;
        WebkitBorderBottomLeftRadius?: import("csstype").BorderBottomLeftRadiusProperty<string | number> | undefined;
        WebkitBorderBottomRightRadius?: import("csstype").BorderBottomRightRadiusProperty<string | number> | undefined;
        WebkitBorderImageSlice?: import("csstype").BorderImageSliceProperty | undefined;
        WebkitBorderTopLeftRadius?: import("csstype").BorderTopLeftRadiusProperty<string | number> | undefined;
        WebkitBorderTopRightRadius?: import("csstype").BorderTopRightRadiusProperty<string | number> | undefined;
        WebkitBoxDecorationBreak?: import("csstype").BoxDecorationBreakProperty | undefined;
        WebkitBoxReflect?: import("csstype").WebkitBoxReflectProperty<string | number> | undefined;
        WebkitBoxShadow?: import("csstype").BoxShadowProperty | undefined;
        WebkitBoxSizing?: import("csstype").BoxSizingProperty | undefined;
        WebkitClipPath?: import("csstype").ClipPathProperty | undefined;
        WebkitColumnCount?: import("csstype").ColumnCountProperty | undefined;
        WebkitColumnFill?: import("csstype").ColumnFillProperty | undefined;
        WebkitColumnGap?: import("csstype").ColumnGapProperty<string | number> | undefined;
        WebkitColumnRuleColor?: import("csstype").ColumnRuleColorProperty | undefined;
        WebkitColumnRuleStyle?: import("csstype").ColumnRuleStyleProperty | undefined;
        WebkitColumnRuleWidth?: import("csstype").ColumnRuleWidthProperty<string | number> | undefined;
        WebkitColumnSpan?: import("csstype").ColumnSpanProperty | undefined;
        WebkitColumnWidth?: import("csstype").ColumnWidthProperty<string | number> | undefined;
        WebkitFilter?: import("csstype").FilterProperty | undefined;
        WebkitFlexBasis?: import("csstype").FlexBasisProperty<string | number> | undefined;
        WebkitFlexDirection?: import("csstype").FlexDirectionProperty | undefined;
        WebkitFlexGrow?: import("csstype").GlobalsNumber | undefined;
        WebkitFlexShrink?: import("csstype").GlobalsNumber | undefined;
        WebkitFlexWrap?: import("csstype").FlexWrapProperty | undefined;
        WebkitFontFeatureSettings?: import("csstype").FontFeatureSettingsProperty | undefined;
        WebkitFontKerning?: import("csstype").FontKerningProperty | undefined;
        WebkitFontSmoothing?: import("csstype").FontSmoothProperty<string | number> | undefined;
        WebkitFontVariantLigatures?: import("csstype").FontVariantLigaturesProperty | undefined;
        WebkitHyphenateCharacter?: import("csstype").HyphenateCharacterProperty | undefined;
        WebkitHyphens?: import("csstype").HyphensProperty | undefined;
        WebkitInitialLetter?: import("csstype").InitialLetterProperty | undefined;
        WebkitJustifyContent?: import("csstype").JustifyContentProperty | undefined;
        WebkitLineBreak?: import("csstype").LineBreakProperty | undefined;
        WebkitLineClamp?: import("csstype").WebkitLineClampProperty | undefined;
        WebkitMarginEnd?: import("csstype").MarginInlineEndProperty<string | number> | undefined;
        WebkitMarginStart?: import("csstype").MarginInlineStartProperty<string | number> | undefined;
        WebkitMaskAttachment?: import("csstype").WebkitMaskAttachmentProperty | undefined;
        WebkitMaskBoxImageOutset?: import("csstype").MaskBorderOutsetProperty<string | number> | undefined;
        WebkitMaskBoxImageRepeat?: import("csstype").MaskBorderRepeatProperty | undefined;
        WebkitMaskBoxImageSlice?: import("csstype").MaskBorderSliceProperty | undefined;
        WebkitMaskBoxImageSource?: import("csstype").MaskBorderSourceProperty | undefined;
        WebkitMaskBoxImageWidth?: import("csstype").MaskBorderWidthProperty<string | number> | undefined;
        WebkitMaskClip?: import("csstype").WebkitMaskClipProperty | undefined;
        WebkitMaskComposite?: import("csstype").WebkitMaskCompositeProperty | undefined;
        WebkitMaskImage?: import("csstype").WebkitMaskImageProperty | undefined;
        WebkitMaskOrigin?: import("csstype").WebkitMaskOriginProperty | undefined;
        WebkitMaskPosition?: import("csstype").WebkitMaskPositionProperty<string | number> | undefined;
        WebkitMaskPositionX?: import("csstype").WebkitMaskPositionXProperty<string | number> | undefined;
        WebkitMaskPositionY?: import("csstype").WebkitMaskPositionYProperty<string | number> | undefined;
        WebkitMaskRepeat?: import("csstype").WebkitMaskRepeatProperty | undefined;
        WebkitMaskRepeatX?: import("csstype").WebkitMaskRepeatXProperty | undefined;
        WebkitMaskRepeatY?: import("csstype").WebkitMaskRepeatYProperty | undefined;
        WebkitMaskSize?: import("csstype").WebkitMaskSizeProperty<string | number> | undefined;
        WebkitMaxInlineSize?: import("csstype").MaxInlineSizeProperty<string | number> | undefined;
        WebkitOrder?: import("csstype").GlobalsNumber | undefined;
        WebkitOverflowScrolling?: import("csstype").WebkitOverflowScrollingProperty | undefined;
        WebkitPaddingEnd?: import("csstype").PaddingInlineEndProperty<string | number> | undefined;
        WebkitPaddingStart?: import("csstype").PaddingInlineStartProperty<string | number> | undefined;
        WebkitPerspective?: import("csstype").PerspectiveProperty<string | number> | undefined;
        WebkitPerspectiveOrigin?: import("csstype").PerspectiveOriginProperty<string | number> | undefined;
        WebkitPrintColorAdjust?: import("csstype").PrintColorAdjustProperty | undefined;
        WebkitRubyPosition?: import("csstype").RubyPositionProperty | undefined;
        WebkitScrollSnapType?: import("csstype").ScrollSnapTypeProperty | undefined;
        WebkitShapeMargin?: import("csstype").ShapeMarginProperty<string | number> | undefined;
        WebkitTapHighlightColor?: import("csstype").WebkitTapHighlightColorProperty | undefined;
        WebkitTextCombine?: import("csstype").TextCombineUprightProperty | undefined;
        WebkitTextDecorationColor?: import("csstype").TextDecorationColorProperty | undefined;
        WebkitTextDecorationLine?: import("csstype").TextDecorationLineProperty | undefined;
        WebkitTextDecorationSkip?: import("csstype").TextDecorationSkipProperty | undefined;
        WebkitTextDecorationStyle?: import("csstype").TextDecorationStyleProperty | undefined;
        WebkitTextEmphasisColor?: import("csstype").TextEmphasisColorProperty | undefined;
        WebkitTextEmphasisPosition?: import("csstype").GlobalsString | undefined;
        WebkitTextEmphasisStyle?: import("csstype").TextEmphasisStyleProperty | undefined;
        WebkitTextFillColor?: import("csstype").WebkitTextFillColorProperty | undefined;
        WebkitTextOrientation?: import("csstype").TextOrientationProperty | undefined;
        WebkitTextSizeAdjust?: import("csstype").TextSizeAdjustProperty | undefined;
        WebkitTextStrokeColor?: import("csstype").WebkitTextStrokeColorProperty | undefined;
        WebkitTextStrokeWidth?: import("csstype").WebkitTextStrokeWidthProperty<string | number> | undefined;
        WebkitTextUnderlinePosition?: import("csstype").TextUnderlinePositionProperty | undefined;
        WebkitTouchCallout?: import("csstype").WebkitTouchCalloutProperty | undefined;
        WebkitTransform?: import("csstype").TransformProperty | undefined;
        WebkitTransformOrigin?: import("csstype").TransformOriginProperty<string | number> | undefined;
        WebkitTransformStyle?: import("csstype").TransformStyleProperty | undefined;
        WebkitTransitionDelay?: import("csstype").GlobalsString | undefined;
        WebkitTransitionDuration?: import("csstype").GlobalsString | undefined;
        WebkitTransitionProperty?: import("csstype").TransitionPropertyProperty | undefined;
        WebkitTransitionTimingFunction?: import("csstype").TransitionTimingFunctionProperty | undefined;
        WebkitUserModify?: import("csstype").WebkitUserModifyProperty | undefined;
        WebkitUserSelect?: import("csstype").UserSelectProperty | undefined;
        WebkitWritingMode?: import("csstype").WritingModeProperty | undefined;
        MozAnimation?: import("csstype").AnimationProperty | undefined;
        MozBorderImage?: import("csstype").BorderImageProperty | undefined;
        MozColumnRule?: import("csstype").ColumnRuleProperty<string | number> | undefined;
        MozColumns?: import("csstype").ColumnsProperty<string | number> | undefined;
        MozTransition?: import("csstype").TransitionProperty | undefined;
        msContentZoomLimit?: import("csstype").GlobalsString | undefined;
        msContentZoomSnap?: import("csstype").MsContentZoomSnapProperty | undefined;
        msFlex?: import("csstype").FlexProperty<string | number> | undefined;
        msScrollLimit?: import("csstype").GlobalsString | undefined;
        msScrollSnapX?: import("csstype").GlobalsString | undefined;
        msScrollSnapY?: import("csstype").GlobalsString | undefined;
        msTransition?: import("csstype").TransitionProperty | undefined;
        WebkitAnimation?: import("csstype").AnimationProperty | undefined;
        WebkitBorderBefore?: import("csstype").WebkitBorderBeforeProperty<string | number> | undefined;
        WebkitBorderImage?: import("csstype").BorderImageProperty | undefined;
        WebkitBorderRadius?: import("csstype").BorderRadiusProperty<string | number> | undefined;
        WebkitColumnRule?: import("csstype").ColumnRuleProperty<string | number> | undefined;
        WebkitColumns?: import("csstype").ColumnsProperty<string | number> | undefined;
        WebkitFlex?: import("csstype").FlexProperty<string | number> | undefined;
        WebkitFlexFlow?: import("csstype").FlexFlowProperty | undefined;
        WebkitMask?: import("csstype").WebkitMaskProperty<string | number> | undefined;
        WebkitMaskBoxImage?: import("csstype").MaskBorderProperty | undefined;
        WebkitTextEmphasis?: import("csstype").TextEmphasisProperty | undefined;
        WebkitTextStroke?: import("csstype").WebkitTextStrokeProperty<string | number> | undefined;
        WebkitTransition?: import("csstype").TransitionProperty | undefined;
        azimuth?: import("csstype").AzimuthProperty | undefined;
        boxAlign?: import("csstype").BoxAlignProperty | undefined;
        boxDirection?: import("csstype").BoxDirectionProperty | undefined;
        boxFlex?: import("csstype").GlobalsNumber | undefined;
        boxFlexGroup?: import("csstype").GlobalsNumber | undefined;
        boxLines?: import("csstype").BoxLinesProperty | undefined;
        boxOrdinalGroup?: import("csstype").GlobalsNumber | undefined;
        boxOrient?: import("csstype").BoxOrientProperty | undefined;
        boxPack?: import("csstype").BoxPackProperty | undefined;
        clip?: import("csstype").ClipProperty | undefined;
        gridColumnGap?: import("csstype").GridColumnGapProperty<string | number> | undefined;
        gridGap?: import("csstype").GridGapProperty<string | number> | undefined;
        gridRowGap?: import("csstype").GridRowGapProperty<string | number> | undefined;
        imeMode?: import("csstype").ImeModeProperty | undefined;
        offsetBlock?: import("csstype").InsetBlockProperty<string | number> | undefined;
        offsetBlockEnd?: import("csstype").InsetBlockEndProperty<string | number> | undefined;
        offsetBlockStart?: import("csstype").InsetBlockStartProperty<string | number> | undefined;
        offsetInline?: import("csstype").InsetInlineProperty<string | number> | undefined;
        offsetInlineEnd?: import("csstype").InsetInlineEndProperty<string | number> | undefined;
        offsetInlineStart?: import("csstype").InsetInlineStartProperty<string | number> | undefined;
        scrollSnapCoordinate?: import("csstype").ScrollSnapCoordinateProperty<string | number> | undefined;
        scrollSnapDestination?: import("csstype").ScrollSnapDestinationProperty<string | number> | undefined;
        scrollSnapPointsX?: import("csstype").ScrollSnapPointsXProperty | undefined;
        scrollSnapPointsY?: import("csstype").ScrollSnapPointsYProperty | undefined;
        scrollSnapTypeX?: import("csstype").ScrollSnapTypeXProperty | undefined;
        scrollSnapTypeY?: import("csstype").ScrollSnapTypeYProperty | undefined;
        scrollbarTrackColor?: import("csstype").MsScrollbarTrackColorProperty | undefined;
        KhtmlBoxAlign?: import("csstype").BoxAlignProperty | undefined;
        KhtmlBoxDirection?: import("csstype").BoxDirectionProperty | undefined;
        KhtmlBoxFlex?: import("csstype").GlobalsNumber | undefined;
        KhtmlBoxFlexGroup?: import("csstype").GlobalsNumber | undefined;
        KhtmlBoxLines?: import("csstype").BoxLinesProperty | undefined;
        KhtmlBoxOrdinalGroup?: import("csstype").GlobalsNumber | undefined;
        KhtmlBoxOrient?: import("csstype").BoxOrientProperty | undefined;
        KhtmlBoxPack?: import("csstype").BoxPackProperty | undefined;
        KhtmlLineBreak?: import("csstype").LineBreakProperty | undefined;
        KhtmlOpacity?: import("csstype").OpacityProperty | undefined;
        KhtmlUserSelect?: import("csstype").UserSelectProperty | undefined;
        MozBackgroundClip?: import("csstype").BackgroundClipProperty | undefined;
        MozBackgroundInlinePolicy?: import("csstype").BoxDecorationBreakProperty | undefined;
        MozBackgroundOrigin?: import("csstype").BackgroundOriginProperty | undefined;
        MozBackgroundSize?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        MozBinding?: import("csstype").MozBindingProperty | undefined;
        MozBorderRadius?: import("csstype").BorderRadiusProperty<string | number> | undefined;
        MozBorderRadiusBottomleft?: import("csstype").BorderBottomLeftRadiusProperty<string | number> | undefined;
        MozBorderRadiusBottomright?: import("csstype").BorderBottomRightRadiusProperty<string | number> | undefined;
        MozBorderRadiusTopleft?: import("csstype").BorderTopLeftRadiusProperty<string | number> | undefined;
        MozBorderRadiusTopright?: import("csstype").BorderTopRightRadiusProperty<string | number> | undefined;
        MozBoxAlign?: import("csstype").BoxAlignProperty | undefined;
        MozBoxDirection?: import("csstype").BoxDirectionProperty | undefined;
        MozBoxFlex?: import("csstype").GlobalsNumber | undefined;
        MozBoxOrdinalGroup?: import("csstype").GlobalsNumber | undefined;
        MozBoxOrient?: import("csstype").BoxOrientProperty | undefined;
        MozBoxPack?: import("csstype").BoxPackProperty | undefined;
        MozBoxShadow?: import("csstype").BoxShadowProperty | undefined;
        MozFloatEdge?: import("csstype").MozFloatEdgeProperty | undefined;
        MozForceBrokenImageIcon?: import("csstype").MozForceBrokenImageIconProperty | undefined;
        MozOpacity?: import("csstype").OpacityProperty | undefined;
        MozOutline?: import("csstype").OutlineProperty<string | number> | undefined;
        MozOutlineColor?: import("csstype").OutlineColorProperty | undefined;
        MozOutlineRadius?: import("csstype").MozOutlineRadiusProperty<string | number> | undefined;
        MozOutlineRadiusBottomleft?: import("csstype").MozOutlineRadiusBottomleftProperty<string | number> | undefined;
        MozOutlineRadiusBottomright?: import("csstype").MozOutlineRadiusBottomrightProperty<string | number> | undefined;
        MozOutlineRadiusTopleft?: import("csstype").MozOutlineRadiusTopleftProperty<string | number> | undefined;
        MozOutlineRadiusTopright?: import("csstype").MozOutlineRadiusToprightProperty<string | number> | undefined;
        MozOutlineStyle?: import("csstype").OutlineStyleProperty | undefined;
        MozOutlineWidth?: import("csstype").OutlineWidthProperty<string | number> | undefined;
        MozTextAlignLast?: import("csstype").TextAlignLastProperty | undefined;
        MozTextDecorationColor?: import("csstype").TextDecorationColorProperty | undefined;
        MozTextDecorationLine?: import("csstype").TextDecorationLineProperty | undefined;
        MozTextDecorationStyle?: import("csstype").TextDecorationStyleProperty | undefined;
        MozUserInput?: import("csstype").MozUserInputProperty | undefined;
        msImeMode?: import("csstype").ImeModeProperty | undefined;
        msScrollbarTrackColor?: import("csstype").MsScrollbarTrackColorProperty | undefined;
        OAnimation?: import("csstype").AnimationProperty | undefined;
        OAnimationDelay?: import("csstype").GlobalsString | undefined;
        OAnimationDirection?: import("csstype").AnimationDirectionProperty | undefined;
        OAnimationDuration?: import("csstype").GlobalsString | undefined;
        OAnimationFillMode?: import("csstype").AnimationFillModeProperty | undefined;
        OAnimationIterationCount?: import("csstype").AnimationIterationCountProperty | undefined;
        OAnimationName?: import("csstype").AnimationNameProperty | undefined;
        OAnimationPlayState?: import("csstype").AnimationPlayStateProperty | undefined;
        OAnimationTimingFunction?: import("csstype").AnimationTimingFunctionProperty | undefined;
        OBackgroundSize?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        OBorderImage?: import("csstype").BorderImageProperty | undefined;
        OObjectFit?: import("csstype").ObjectFitProperty | undefined;
        OObjectPosition?: import("csstype").ObjectPositionProperty<string | number> | undefined;
        OTabSize?: import("csstype").TabSizeProperty<string | number> | undefined;
        OTextOverflow?: import("csstype").TextOverflowProperty | undefined;
        OTransform?: import("csstype").TransformProperty | undefined;
        OTransformOrigin?: import("csstype").TransformOriginProperty<string | number> | undefined;
        OTransition?: import("csstype").TransitionProperty | undefined;
        OTransitionDelay?: import("csstype").GlobalsString | undefined;
        OTransitionDuration?: import("csstype").GlobalsString | undefined;
        OTransitionProperty?: import("csstype").TransitionPropertyProperty | undefined;
        OTransitionTimingFunction?: import("csstype").TransitionTimingFunctionProperty | undefined;
        WebkitBoxAlign?: import("csstype").BoxAlignProperty | undefined;
        WebkitBoxDirection?: import("csstype").BoxDirectionProperty | undefined;
        WebkitBoxFlex?: import("csstype").GlobalsNumber | undefined;
        WebkitBoxFlexGroup?: import("csstype").GlobalsNumber | undefined;
        WebkitBoxLines?: import("csstype").BoxLinesProperty | undefined;
        WebkitBoxOrdinalGroup?: import("csstype").GlobalsNumber | undefined;
        WebkitBoxOrient?: import("csstype").BoxOrientProperty | undefined;
        WebkitBoxPack?: import("csstype").BoxPackProperty | undefined;
        WebkitScrollSnapPointsX?: import("csstype").ScrollSnapPointsXProperty | undefined;
        WebkitScrollSnapPointsY?: import("csstype").ScrollSnapPointsYProperty | undefined;
        alignmentBaseline?: import("csstype").AlignmentBaselineProperty | undefined;
        baselineShift?: import("csstype").BaselineShiftProperty<string | number> | undefined;
        clipRule?: import("csstype").ClipRuleProperty | undefined;
        colorInterpolation?: import("csstype").ColorInterpolationProperty | undefined;
        colorRendering?: import("csstype").ColorRenderingProperty | undefined;
        dominantBaseline?: import("csstype").DominantBaselineProperty | undefined;
        fill?: import("csstype").FillProperty | undefined;
        fillOpacity?: import("csstype").GlobalsNumber | undefined;
        fillRule?: import("csstype").FillRuleProperty | undefined;
        floodColor?: import("csstype").FloodColorProperty | undefined;
        floodOpacity?: import("csstype").GlobalsNumber | undefined;
        glyphOrientationVertical?: import("csstype").GlyphOrientationVerticalProperty | undefined;
        lightingColor?: import("csstype").LightingColorProperty | undefined;
        marker?: import("csstype").MarkerProperty | undefined;
        markerEnd?: import("csstype").MarkerEndProperty | undefined;
        markerMid?: import("csstype").MarkerMidProperty | undefined;
        markerStart?: import("csstype").MarkerStartProperty | undefined;
        shapeRendering?: import("csstype").ShapeRenderingProperty | undefined;
        stopColor?: import("csstype").StopColorProperty | undefined;
        stopOpacity?: import("csstype").GlobalsNumber | undefined;
        stroke?: import("csstype").StrokeProperty | undefined;
        strokeDasharray?: import("csstype").StrokeDasharrayProperty<string | number> | undefined;
        strokeDashoffset?: import("csstype").StrokeDashoffsetProperty<string | number> | undefined;
        strokeLinecap?: import("csstype").StrokeLinecapProperty | undefined;
        strokeLinejoin?: import("csstype").StrokeLinejoinProperty | undefined;
        strokeMiterlimit?: import("csstype").GlobalsNumber | undefined;
        strokeOpacity?: import("csstype").GlobalsNumber | undefined;
        strokeWidth?: import("csstype").StrokeWidthProperty<string | number> | undefined;
        textAnchor?: import("csstype").TextAnchorProperty | undefined;
        vectorEffect?: import("csstype").VectorEffectProperty | undefined;
        "accent-color"?: import("csstype").AccentColorProperty | undefined;
        "align-content"?: import("csstype").AlignContentProperty | undefined;
        "align-items"?: import("csstype").AlignItemsProperty | undefined;
        "align-self"?: import("csstype").AlignSelfProperty | undefined;
        "align-tracks"?: import("csstype").AlignTracksProperty | undefined;
        "animation-delay"?: import("csstype").GlobalsString | undefined;
        "animation-direction"?: import("csstype").AnimationDirectionProperty | undefined;
        "animation-duration"?: import("csstype").GlobalsString | undefined;
        "animation-fill-mode"?: import("csstype").AnimationFillModeProperty | undefined;
        "animation-iteration-count"?: import("csstype").AnimationIterationCountProperty | undefined;
        "animation-name"?: import("csstype").AnimationNameProperty | undefined;
        "animation-play-state"?: import("csstype").AnimationPlayStateProperty | undefined;
        "animation-timeline"?: import("csstype").AnimationTimelineProperty | undefined;
        "animation-timing-function"?: import("csstype").AnimationTimingFunctionProperty | undefined;
        "aspect-ratio"?: import("csstype").AspectRatioProperty | undefined;
        "backdrop-filter"?: import("csstype").BackdropFilterProperty | undefined;
        "backface-visibility"?: import("csstype").BackfaceVisibilityProperty | undefined;
        "background-attachment"?: import("csstype").BackgroundAttachmentProperty | undefined;
        "background-blend-mode"?: import("csstype").BackgroundBlendModeProperty | undefined;
        "background-clip"?: import("csstype").BackgroundClipProperty | undefined;
        "background-color"?: import("csstype").BackgroundColorProperty | undefined;
        "background-image"?: import("csstype").BackgroundImageProperty | undefined;
        "background-origin"?: import("csstype").BackgroundOriginProperty | undefined;
        "background-position-x"?: import("csstype").BackgroundPositionXProperty<string | number> | undefined;
        "background-position-y"?: import("csstype").BackgroundPositionYProperty<string | number> | undefined;
        "background-repeat"?: import("csstype").BackgroundRepeatProperty | undefined;
        "background-size"?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        "block-overflow"?: import("csstype").BlockOverflowProperty | undefined;
        "block-size"?: import("csstype").BlockSizeProperty<string | number> | undefined;
        "border-block-color"?: import("csstype").BorderBlockColorProperty | undefined;
        "border-block-end-color"?: import("csstype").BorderBlockEndColorProperty | undefined;
        "border-block-end-style"?: import("csstype").BorderBlockEndStyleProperty | undefined;
        "border-block-end-width"?: import("csstype").BorderBlockEndWidthProperty<string | number> | undefined;
        "border-block-start-color"?: import("csstype").BorderBlockStartColorProperty | undefined;
        "border-block-start-style"?: import("csstype").BorderBlockStartStyleProperty | undefined;
        "border-block-start-width"?: import("csstype").BorderBlockStartWidthProperty<string | number> | undefined;
        "border-block-style"?: import("csstype").BorderBlockStyleProperty | undefined;
        "border-block-width"?: import("csstype").BorderBlockWidthProperty<string | number> | undefined;
        "border-bottom-color"?: import("csstype").BorderBottomColorProperty | undefined;
        "border-bottom-left-radius"?: import("csstype").BorderBottomLeftRadiusProperty<string | number> | undefined;
        "border-bottom-right-radius"?: import("csstype").BorderBottomRightRadiusProperty<string | number> | undefined;
        "border-bottom-style"?: import("csstype").BorderBottomStyleProperty | undefined;
        "border-bottom-width"?: import("csstype").BorderBottomWidthProperty<string | number> | undefined;
        "border-collapse"?: import("csstype").BorderCollapseProperty | undefined;
        "border-end-end-radius"?: import("csstype").BorderEndEndRadiusProperty<string | number> | undefined;
        "border-end-start-radius"?: import("csstype").BorderEndStartRadiusProperty<string | number> | undefined;
        "border-image-outset"?: import("csstype").BorderImageOutsetProperty<string | number> | undefined;
        "border-image-repeat"?: import("csstype").BorderImageRepeatProperty | undefined;
        "border-image-slice"?: import("csstype").BorderImageSliceProperty | undefined;
        "border-image-source"?: import("csstype").BorderImageSourceProperty | undefined;
        "border-image-width"?: import("csstype").BorderImageWidthProperty<string | number> | undefined;
        "border-inline-color"?: import("csstype").BorderInlineColorProperty | undefined;
        "border-inline-end-color"?: import("csstype").BorderInlineEndColorProperty | undefined;
        "border-inline-end-style"?: import("csstype").BorderInlineEndStyleProperty | undefined;
        "border-inline-end-width"?: import("csstype").BorderInlineEndWidthProperty<string | number> | undefined;
        "border-inline-start-color"?: import("csstype").BorderInlineStartColorProperty | undefined;
        "border-inline-start-style"?: import("csstype").BorderInlineStartStyleProperty | undefined;
        "border-inline-start-width"?: import("csstype").BorderInlineStartWidthProperty<string | number> | undefined;
        "border-inline-style"?: import("csstype").BorderInlineStyleProperty | undefined;
        "border-inline-width"?: import("csstype").BorderInlineWidthProperty<string | number> | undefined;
        "border-left-color"?: import("csstype").BorderLeftColorProperty | undefined;
        "border-left-style"?: import("csstype").BorderLeftStyleProperty | undefined;
        "border-left-width"?: import("csstype").BorderLeftWidthProperty<string | number> | undefined;
        "border-right-color"?: import("csstype").BorderRightColorProperty | undefined;
        "border-right-style"?: import("csstype").BorderRightStyleProperty | undefined;
        "border-right-width"?: import("csstype").BorderRightWidthProperty<string | number> | undefined;
        "border-spacing"?: import("csstype").BorderSpacingProperty<string | number> | undefined;
        "border-start-end-radius"?: import("csstype").BorderStartEndRadiusProperty<string | number> | undefined;
        "border-start-start-radius"?: import("csstype").BorderStartStartRadiusProperty<string | number> | undefined;
        "border-top-color"?: import("csstype").BorderTopColorProperty | undefined;
        "border-top-left-radius"?: import("csstype").BorderTopLeftRadiusProperty<string | number> | undefined;
        "border-top-right-radius"?: import("csstype").BorderTopRightRadiusProperty<string | number> | undefined;
        "border-top-style"?: import("csstype").BorderTopStyleProperty | undefined;
        "border-top-width"?: import("csstype").BorderTopWidthProperty<string | number> | undefined;
        "box-decoration-break"?: import("csstype").BoxDecorationBreakProperty | undefined;
        "box-shadow"?: import("csstype").BoxShadowProperty | undefined;
        "box-sizing"?: import("csstype").BoxSizingProperty | undefined;
        "break-after"?: import("csstype").BreakAfterProperty | undefined;
        "break-before"?: import("csstype").BreakBeforeProperty | undefined;
        "break-inside"?: import("csstype").BreakInsideProperty | undefined;
        "caption-side"?: import("csstype").CaptionSideProperty | undefined;
        "caret-color"?: import("csstype").CaretColorProperty | undefined;
        "clip-path"?: import("csstype").ClipPathProperty | undefined;
        "color-adjust"?: import("csstype").PrintColorAdjustProperty | undefined;
        "color-scheme"?: import("csstype").ColorSchemeProperty | undefined;
        "column-count"?: import("csstype").ColumnCountProperty | undefined;
        "column-fill"?: import("csstype").ColumnFillProperty | undefined;
        "column-gap"?: import("csstype").ColumnGapProperty<string | number> | undefined;
        "column-rule-color"?: import("csstype").ColumnRuleColorProperty | undefined;
        "column-rule-style"?: import("csstype").ColumnRuleStyleProperty | undefined;
        "column-rule-width"?: import("csstype").ColumnRuleWidthProperty<string | number> | undefined;
        "column-span"?: import("csstype").ColumnSpanProperty | undefined;
        "column-width"?: import("csstype").ColumnWidthProperty<string | number> | undefined;
        "content-visibility"?: import("csstype").ContentVisibilityProperty | undefined;
        "counter-increment"?: import("csstype").CounterIncrementProperty | undefined;
        "counter-reset"?: import("csstype").CounterResetProperty | undefined;
        "counter-set"?: import("csstype").CounterSetProperty | undefined;
        "empty-cells"?: import("csstype").EmptyCellsProperty | undefined;
        "flex-basis"?: import("csstype").FlexBasisProperty<string | number> | undefined;
        "flex-direction"?: import("csstype").FlexDirectionProperty | undefined;
        "flex-grow"?: import("csstype").GlobalsNumber | undefined;
        "flex-shrink"?: import("csstype").GlobalsNumber | undefined;
        "flex-wrap"?: import("csstype").FlexWrapProperty | undefined;
        "font-family"?: import("csstype").FontFamilyProperty | undefined;
        "font-feature-settings"?: import("csstype").FontFeatureSettingsProperty | undefined;
        "font-kerning"?: import("csstype").FontKerningProperty | undefined;
        "font-language-override"?: import("csstype").FontLanguageOverrideProperty | undefined;
        "font-optical-sizing"?: import("csstype").FontOpticalSizingProperty | undefined;
        "font-size"?: import("csstype").FontSizeProperty<string | number> | undefined;
        "font-size-adjust"?: import("csstype").FontSizeAdjustProperty | undefined;
        "font-smooth"?: import("csstype").FontSmoothProperty<string | number> | undefined;
        "font-stretch"?: import("csstype").FontStretchProperty | undefined;
        "font-style"?: import("csstype").FontStyleProperty | undefined;
        "font-synthesis"?: import("csstype").FontSynthesisProperty | undefined;
        "font-variant"?: import("csstype").FontVariantProperty | undefined;
        "font-variant-alternates"?: import("csstype").FontVariantAlternatesProperty | undefined;
        "font-variant-caps"?: import("csstype").FontVariantCapsProperty | undefined;
        "font-variant-east-asian"?: import("csstype").FontVariantEastAsianProperty | undefined;
        "font-variant-ligatures"?: import("csstype").FontVariantLigaturesProperty | undefined;
        "font-variant-numeric"?: import("csstype").FontVariantNumericProperty | undefined;
        "font-variant-position"?: import("csstype").FontVariantPositionProperty | undefined;
        "font-variation-settings"?: import("csstype").FontVariationSettingsProperty | undefined;
        "font-weight"?: import("csstype").FontWeightProperty | undefined;
        "forced-color-adjust"?: import("csstype").ForcedColorAdjustProperty | undefined;
        "grid-auto-columns"?: import("csstype").GridAutoColumnsProperty<string | number> | undefined;
        "grid-auto-flow"?: import("csstype").GridAutoFlowProperty | undefined;
        "grid-auto-rows"?: import("csstype").GridAutoRowsProperty<string | number> | undefined;
        "grid-column-end"?: import("csstype").GridColumnEndProperty | undefined;
        "grid-column-start"?: import("csstype").GridColumnStartProperty | undefined;
        "grid-row-end"?: import("csstype").GridRowEndProperty | undefined;
        "grid-row-start"?: import("csstype").GridRowStartProperty | undefined;
        "grid-template-areas"?: import("csstype").GridTemplateAreasProperty | undefined;
        "grid-template-columns"?: import("csstype").GridTemplateColumnsProperty<string | number> | undefined;
        "grid-template-rows"?: import("csstype").GridTemplateRowsProperty<string | number> | undefined;
        "hanging-punctuation"?: import("csstype").HangingPunctuationProperty | undefined;
        "hyphenate-character"?: import("csstype").HyphenateCharacterProperty | undefined;
        "image-orientation"?: import("csstype").ImageOrientationProperty | undefined;
        "image-rendering"?: import("csstype").ImageRenderingProperty | undefined;
        "image-resolution"?: import("csstype").ImageResolutionProperty | undefined;
        "initial-letter"?: import("csstype").InitialLetterProperty | undefined;
        "inline-size"?: import("csstype").InlineSizeProperty<string | number> | undefined;
        "input-security"?: import("csstype").InputSecurityProperty | undefined;
        "inset-block"?: import("csstype").InsetBlockProperty<string | number> | undefined;
        "inset-block-end"?: import("csstype").InsetBlockEndProperty<string | number> | undefined;
        "inset-block-start"?: import("csstype").InsetBlockStartProperty<string | number> | undefined;
        "inset-inline"?: import("csstype").InsetInlineProperty<string | number> | undefined;
        "inset-inline-end"?: import("csstype").InsetInlineEndProperty<string | number> | undefined;
        "inset-inline-start"?: import("csstype").InsetInlineStartProperty<string | number> | undefined;
        "justify-content"?: import("csstype").JustifyContentProperty | undefined;
        "justify-items"?: import("csstype").JustifyItemsProperty | undefined;
        "justify-self"?: import("csstype").JustifySelfProperty | undefined;
        "justify-tracks"?: import("csstype").JustifyTracksProperty | undefined;
        "letter-spacing"?: import("csstype").LetterSpacingProperty<string | number> | undefined;
        "line-break"?: import("csstype").LineBreakProperty | undefined;
        "line-height"?: import("csstype").LineHeightProperty<string | number> | undefined;
        "line-height-step"?: import("csstype").LineHeightStepProperty<string | number> | undefined;
        "list-style-image"?: import("csstype").ListStyleImageProperty | undefined;
        "list-style-position"?: import("csstype").ListStylePositionProperty | undefined;
        "list-style-type"?: import("csstype").ListStyleTypeProperty | undefined;
        "margin-block"?: import("csstype").MarginBlockProperty<string | number> | undefined;
        "margin-block-end"?: import("csstype").MarginBlockEndProperty<string | number> | undefined;
        "margin-block-start"?: import("csstype").MarginBlockStartProperty<string | number> | undefined;
        "margin-bottom"?: import("csstype").MarginBottomProperty<string | number> | undefined;
        "margin-inline"?: import("csstype").MarginInlineProperty<string | number> | undefined;
        "margin-inline-end"?: import("csstype").MarginInlineEndProperty<string | number> | undefined;
        "margin-inline-start"?: import("csstype").MarginInlineStartProperty<string | number> | undefined;
        "margin-left"?: import("csstype").MarginLeftProperty<string | number> | undefined;
        "margin-right"?: import("csstype").MarginRightProperty<string | number> | undefined;
        "margin-top"?: import("csstype").MarginTopProperty<string | number> | undefined;
        "mask-border-mode"?: import("csstype").MaskBorderModeProperty | undefined;
        "mask-border-outset"?: import("csstype").MaskBorderOutsetProperty<string | number> | undefined;
        "mask-border-repeat"?: import("csstype").MaskBorderRepeatProperty | undefined;
        "mask-border-slice"?: import("csstype").MaskBorderSliceProperty | undefined;
        "mask-border-source"?: import("csstype").MaskBorderSourceProperty | undefined;
        "mask-border-width"?: import("csstype").MaskBorderWidthProperty<string | number> | undefined;
        "mask-clip"?: import("csstype").MaskClipProperty | undefined;
        "mask-composite"?: import("csstype").MaskCompositeProperty | undefined;
        "mask-image"?: import("csstype").MaskImageProperty | undefined;
        "mask-mode"?: import("csstype").MaskModeProperty | undefined;
        "mask-origin"?: import("csstype").MaskOriginProperty | undefined;
        "mask-position"?: import("csstype").MaskPositionProperty<string | number> | undefined;
        "mask-repeat"?: import("csstype").MaskRepeatProperty | undefined;
        "mask-size"?: import("csstype").MaskSizeProperty<string | number> | undefined;
        "mask-type"?: import("csstype").MaskTypeProperty | undefined;
        "math-style"?: import("csstype").MathStyleProperty | undefined;
        "max-block-size"?: import("csstype").MaxBlockSizeProperty<string | number> | undefined;
        "max-height"?: import("csstype").MaxHeightProperty<string | number> | undefined;
        "max-inline-size"?: import("csstype").MaxInlineSizeProperty<string | number> | undefined;
        "max-lines"?: import("csstype").MaxLinesProperty | undefined;
        "max-width"?: import("csstype").MaxWidthProperty<string | number> | undefined;
        "min-block-size"?: import("csstype").MinBlockSizeProperty<string | number> | undefined;
        "min-height"?: import("csstype").MinHeightProperty<string | number> | undefined;
        "min-inline-size"?: import("csstype").MinInlineSizeProperty<string | number> | undefined;
        "min-width"?: import("csstype").MinWidthProperty<string | number> | undefined;
        "mix-blend-mode"?: import("csstype").MixBlendModeProperty | undefined;
        "motion-distance"?: import("csstype").OffsetDistanceProperty<string | number> | undefined;
        "motion-path"?: import("csstype").OffsetPathProperty | undefined;
        "motion-rotation"?: import("csstype").OffsetRotateProperty | undefined;
        "object-fit"?: import("csstype").ObjectFitProperty | undefined;
        "object-position"?: import("csstype").ObjectPositionProperty<string | number> | undefined;
        "offset-anchor"?: import("csstype").OffsetAnchorProperty<string | number> | undefined;
        "offset-distance"?: import("csstype").OffsetDistanceProperty<string | number> | undefined;
        "offset-path"?: import("csstype").OffsetPathProperty | undefined;
        "offset-rotate"?: import("csstype").OffsetRotateProperty | undefined;
        "offset-rotation"?: import("csstype").OffsetRotateProperty | undefined;
        "outline-color"?: import("csstype").OutlineColorProperty | undefined;
        "outline-offset"?: import("csstype").OutlineOffsetProperty<string | number> | undefined;
        "outline-style"?: import("csstype").OutlineStyleProperty | undefined;
        "outline-width"?: import("csstype").OutlineWidthProperty<string | number> | undefined;
        "overflow-anchor"?: import("csstype").OverflowAnchorProperty | undefined;
        "overflow-block"?: import("csstype").OverflowBlockProperty | undefined;
        "overflow-clip-box"?: import("csstype").OverflowClipBoxProperty | undefined;
        "overflow-clip-margin"?: import("csstype").OverflowClipMarginProperty<string | number> | undefined;
        "overflow-inline"?: import("csstype").OverflowInlineProperty | undefined;
        "overflow-wrap"?: import("csstype").OverflowWrapProperty | undefined;
        "overflow-x"?: import("csstype").OverflowXProperty | undefined;
        "overflow-y"?: import("csstype").OverflowYProperty | undefined;
        "overscroll-behavior-block"?: import("csstype").OverscrollBehaviorBlockProperty | undefined;
        "overscroll-behavior-inline"?: import("csstype").OverscrollBehaviorInlineProperty | undefined;
        "overscroll-behavior-x"?: import("csstype").OverscrollBehaviorXProperty | undefined;
        "overscroll-behavior-y"?: import("csstype").OverscrollBehaviorYProperty | undefined;
        "padding-block"?: import("csstype").PaddingBlockProperty<string | number> | undefined;
        "padding-block-end"?: import("csstype").PaddingBlockEndProperty<string | number> | undefined;
        "padding-block-start"?: import("csstype").PaddingBlockStartProperty<string | number> | undefined;
        "padding-bottom"?: import("csstype").PaddingBottomProperty<string | number> | undefined;
        "padding-inline"?: import("csstype").PaddingInlineProperty<string | number> | undefined;
        "padding-inline-end"?: import("csstype").PaddingInlineEndProperty<string | number> | undefined;
        "padding-inline-start"?: import("csstype").PaddingInlineStartProperty<string | number> | undefined;
        "padding-left"?: import("csstype").PaddingLeftProperty<string | number> | undefined;
        "padding-right"?: import("csstype").PaddingRightProperty<string | number> | undefined;
        "padding-top"?: import("csstype").PaddingTopProperty<string | number> | undefined;
        "page-break-after"?: import("csstype").PageBreakAfterProperty | undefined;
        "page-break-before"?: import("csstype").PageBreakBeforeProperty | undefined;
        "page-break-inside"?: import("csstype").PageBreakInsideProperty | undefined;
        "paint-order"?: import("csstype").PaintOrderProperty | undefined;
        "perspective-origin"?: import("csstype").PerspectiveOriginProperty<string | number> | undefined;
        "place-content"?: import("csstype").PlaceContentProperty | undefined;
        "pointer-events"?: import("csstype").PointerEventsProperty | undefined;
        "print-color-adjust"?: import("csstype").PrintColorAdjustProperty | undefined;
        "row-gap"?: import("csstype").RowGapProperty<string | number> | undefined;
        "ruby-align"?: import("csstype").RubyAlignProperty | undefined;
        "ruby-merge"?: import("csstype").RubyMergeProperty | undefined;
        "ruby-position"?: import("csstype").RubyPositionProperty | undefined;
        "scroll-behavior"?: import("csstype").ScrollBehaviorProperty | undefined;
        "scroll-margin"?: import("csstype").ScrollMarginProperty<string | number> | undefined;
        "scroll-margin-block"?: import("csstype").ScrollMarginBlockProperty<string | number> | undefined;
        "scroll-margin-block-end"?: import("csstype").ScrollMarginBlockEndProperty<string | number> | undefined;
        "scroll-margin-block-start"?: import("csstype").ScrollMarginBlockStartProperty<string | number> | undefined;
        "scroll-margin-bottom"?: import("csstype").ScrollMarginBottomProperty<string | number> | undefined;
        "scroll-margin-inline"?: import("csstype").ScrollMarginInlineProperty<string | number> | undefined;
        "scroll-margin-inline-end"?: import("csstype").ScrollMarginInlineEndProperty<string | number> | undefined;
        "scroll-margin-inline-start"?: import("csstype").ScrollMarginInlineStartProperty<string | number> | undefined;
        "scroll-margin-left"?: import("csstype").ScrollMarginLeftProperty<string | number> | undefined;
        "scroll-margin-right"?: import("csstype").ScrollMarginRightProperty<string | number> | undefined;
        "scroll-margin-top"?: import("csstype").ScrollMarginTopProperty<string | number> | undefined;
        "scroll-padding"?: import("csstype").ScrollPaddingProperty<string | number> | undefined;
        "scroll-padding-block"?: import("csstype").ScrollPaddingBlockProperty<string | number> | undefined;
        "scroll-padding-block-end"?: import("csstype").ScrollPaddingBlockEndProperty<string | number> | undefined;
        "scroll-padding-block-start"?: import("csstype").ScrollPaddingBlockStartProperty<string | number> | undefined;
        "scroll-padding-bottom"?: import("csstype").ScrollPaddingBottomProperty<string | number> | undefined;
        "scroll-padding-inline"?: import("csstype").ScrollPaddingInlineProperty<string | number> | undefined;
        "scroll-padding-inline-end"?: import("csstype").ScrollPaddingInlineEndProperty<string | number> | undefined;
        "scroll-padding-inline-start"?: import("csstype").ScrollPaddingInlineStartProperty<string | number> | undefined;
        "scroll-padding-left"?: import("csstype").ScrollPaddingLeftProperty<string | number> | undefined;
        "scroll-padding-right"?: import("csstype").ScrollPaddingRightProperty<string | number> | undefined;
        "scroll-padding-top"?: import("csstype").ScrollPaddingTopProperty<string | number> | undefined;
        "scroll-snap-align"?: import("csstype").ScrollSnapAlignProperty | undefined;
        "scroll-snap-margin"?: import("csstype").ScrollMarginProperty<string | number> | undefined;
        "scroll-snap-margin-bottom"?: import("csstype").ScrollMarginBottomProperty<string | number> | undefined;
        "scroll-snap-margin-left"?: import("csstype").ScrollMarginLeftProperty<string | number> | undefined;
        "scroll-snap-margin-right"?: import("csstype").ScrollMarginRightProperty<string | number> | undefined;
        "scroll-snap-margin-top"?: import("csstype").ScrollMarginTopProperty<string | number> | undefined;
        "scroll-snap-stop"?: import("csstype").ScrollSnapStopProperty | undefined;
        "scroll-snap-type"?: import("csstype").ScrollSnapTypeProperty | undefined;
        "scrollbar-color"?: import("csstype").ScrollbarColorProperty | undefined;
        "scrollbar-gutter"?: import("csstype").ScrollbarGutterProperty | undefined;
        "scrollbar-width"?: import("csstype").ScrollbarWidthProperty | undefined;
        "shape-image-threshold"?: import("csstype").ShapeImageThresholdProperty | undefined;
        "shape-margin"?: import("csstype").ShapeMarginProperty<string | number> | undefined;
        "shape-outside"?: import("csstype").ShapeOutsideProperty | undefined;
        "tab-size"?: import("csstype").TabSizeProperty<string | number> | undefined;
        "table-layout"?: import("csstype").TableLayoutProperty | undefined;
        "text-align"?: import("csstype").TextAlignProperty | undefined;
        "text-align-last"?: import("csstype").TextAlignLastProperty | undefined;
        "text-combine-upright"?: import("csstype").TextCombineUprightProperty | undefined;
        "text-decoration-color"?: import("csstype").TextDecorationColorProperty | undefined;
        "text-decoration-line"?: import("csstype").TextDecorationLineProperty | undefined;
        "text-decoration-skip"?: import("csstype").TextDecorationSkipProperty | undefined;
        "text-decoration-skip-ink"?: import("csstype").TextDecorationSkipInkProperty | undefined;
        "text-decoration-style"?: import("csstype").TextDecorationStyleProperty | undefined;
        "text-decoration-thickness"?: import("csstype").TextDecorationThicknessProperty<string | number> | undefined;
        "text-decoration-width"?: import("csstype").TextDecorationThicknessProperty<string | number> | undefined;
        "text-emphasis-color"?: import("csstype").TextEmphasisColorProperty | undefined;
        "text-emphasis-position"?: import("csstype").GlobalsString | undefined;
        "text-emphasis-style"?: import("csstype").TextEmphasisStyleProperty | undefined;
        "text-indent"?: import("csstype").TextIndentProperty<string | number> | undefined;
        "text-justify"?: import("csstype").TextJustifyProperty | undefined;
        "text-orientation"?: import("csstype").TextOrientationProperty | undefined;
        "text-overflow"?: import("csstype").TextOverflowProperty | undefined;
        "text-rendering"?: import("csstype").TextRenderingProperty | undefined;
        "text-shadow"?: import("csstype").TextShadowProperty | undefined;
        "text-size-adjust"?: import("csstype").TextSizeAdjustProperty | undefined;
        "text-transform"?: import("csstype").TextTransformProperty | undefined;
        "text-underline-offset"?: import("csstype").TextUnderlineOffsetProperty<string | number> | undefined;
        "text-underline-position"?: import("csstype").TextUnderlinePositionProperty | undefined;
        "touch-action"?: import("csstype").TouchActionProperty | undefined;
        "transform-box"?: import("csstype").TransformBoxProperty | undefined;
        "transform-origin"?: import("csstype").TransformOriginProperty<string | number> | undefined;
        "transform-style"?: import("csstype").TransformStyleProperty | undefined;
        "transition-delay"?: import("csstype").GlobalsString | undefined;
        "transition-duration"?: import("csstype").GlobalsString | undefined;
        "transition-property"?: import("csstype").TransitionPropertyProperty | undefined;
        "transition-timing-function"?: import("csstype").TransitionTimingFunctionProperty | undefined;
        "unicode-bidi"?: import("csstype").UnicodeBidiProperty | undefined;
        "user-select"?: import("csstype").UserSelectProperty | undefined;
        "vertical-align"?: import("csstype").VerticalAlignProperty<string | number> | undefined;
        "white-space"?: import("csstype").WhiteSpaceProperty | undefined;
        "will-change"?: import("csstype").WillChangeProperty | undefined;
        "word-break"?: import("csstype").WordBreakProperty | undefined;
        "word-spacing"?: import("csstype").WordSpacingProperty<string | number> | undefined;
        "word-wrap"?: import("csstype").WordWrapProperty | undefined;
        "writing-mode"?: import("csstype").WritingModeProperty | undefined;
        "z-index"?: import("csstype").ZIndexProperty | undefined;
        "background-position"?: import("csstype").BackgroundPositionProperty<string | number> | undefined;
        "border-block"?: import("csstype").BorderBlockProperty<string | number> | undefined;
        "border-block-end"?: import("csstype").BorderBlockEndProperty<string | number> | undefined;
        "border-block-start"?: import("csstype").BorderBlockStartProperty<string | number> | undefined;
        "border-bottom"?: import("csstype").BorderBottomProperty<string | number> | undefined;
        "border-color"?: import("csstype").BorderColorProperty | undefined;
        "border-image"?: import("csstype").BorderImageProperty | undefined;
        "border-inline"?: import("csstype").BorderInlineProperty<string | number> | undefined;
        "border-inline-end"?: import("csstype").BorderInlineEndProperty<string | number> | undefined;
        "border-inline-start"?: import("csstype").BorderInlineStartProperty<string | number> | undefined;
        "border-left"?: import("csstype").BorderLeftProperty<string | number> | undefined;
        "border-radius"?: import("csstype").BorderRadiusProperty<string | number> | undefined;
        "border-right"?: import("csstype").BorderRightProperty<string | number> | undefined;
        "border-style"?: import("csstype").BorderStyleProperty | undefined;
        "border-top"?: import("csstype").BorderTopProperty<string | number> | undefined;
        "border-width"?: import("csstype").BorderWidthProperty<string | number> | undefined;
        "column-rule"?: import("csstype").ColumnRuleProperty<string | number> | undefined;
        "flex-flow"?: import("csstype").FlexFlowProperty | undefined;
        "grid-area"?: import("csstype").GridAreaProperty | undefined;
        "grid-column"?: import("csstype").GridColumnProperty | undefined;
        "grid-row"?: import("csstype").GridRowProperty | undefined;
        "grid-template"?: import("csstype").GridTemplateProperty | undefined;
        "line-clamp"?: import("csstype").LineClampProperty | undefined;
        "list-style"?: import("csstype").ListStyleProperty | undefined;
        "mask-border"?: import("csstype").MaskBorderProperty | undefined;
        "overscroll-behavior"?: import("csstype").OverscrollBehaviorProperty | undefined;
        "place-items"?: import("csstype").PlaceItemsProperty | undefined;
        "place-self"?: import("csstype").PlaceSelfProperty | undefined;
        "text-decoration"?: import("csstype").TextDecorationProperty<string | number> | undefined;
        "text-emphasis"?: import("csstype").TextEmphasisProperty | undefined;
        "-moz-animation-delay"?: import("csstype").GlobalsString | undefined;
        "-moz-animation-direction"?: import("csstype").AnimationDirectionProperty | undefined;
        "-moz-animation-duration"?: import("csstype").GlobalsString | undefined;
        "-moz-animation-fill-mode"?: import("csstype").AnimationFillModeProperty | undefined;
        "-moz-animation-iteration-count"?: import("csstype").AnimationIterationCountProperty | undefined;
        "-moz-animation-name"?: import("csstype").AnimationNameProperty | undefined;
        "-moz-animation-play-state"?: import("csstype").AnimationPlayStateProperty | undefined;
        "-moz-animation-timing-function"?: import("csstype").AnimationTimingFunctionProperty | undefined;
        "-moz-appearance"?: import("csstype").MozAppearanceProperty | undefined;
        "-moz-backface-visibility"?: import("csstype").BackfaceVisibilityProperty | undefined;
        "-moz-border-bottom-colors"?: import("csstype").MozBorderBottomColorsProperty | undefined;
        "-moz-border-end-color"?: import("csstype").BorderInlineEndColorProperty | undefined;
        "-moz-border-end-style"?: import("csstype").BorderInlineEndStyleProperty | undefined;
        "-moz-border-end-width"?: import("csstype").BorderInlineEndWidthProperty<string | number> | undefined;
        "-moz-border-left-colors"?: import("csstype").MozBorderLeftColorsProperty | undefined;
        "-moz-border-right-colors"?: import("csstype").MozBorderRightColorsProperty | undefined;
        "-moz-border-start-color"?: import("csstype").BorderInlineStartColorProperty | undefined;
        "-moz-border-start-style"?: import("csstype").BorderInlineStartStyleProperty | undefined;
        "-moz-border-top-colors"?: import("csstype").MozBorderTopColorsProperty | undefined;
        "-moz-box-sizing"?: import("csstype").BoxSizingProperty | undefined;
        "-moz-column-count"?: import("csstype").ColumnCountProperty | undefined;
        "-moz-column-fill"?: import("csstype").ColumnFillProperty | undefined;
        "-moz-column-gap"?: import("csstype").ColumnGapProperty<string | number> | undefined;
        "-moz-column-rule-color"?: import("csstype").ColumnRuleColorProperty | undefined;
        "-moz-column-rule-style"?: import("csstype").ColumnRuleStyleProperty | undefined;
        "-moz-column-rule-width"?: import("csstype").ColumnRuleWidthProperty<string | number> | undefined;
        "-moz-column-width"?: import("csstype").ColumnWidthProperty<string | number> | undefined;
        "-moz-context-properties"?: import("csstype").MozContextPropertiesProperty | undefined;
        "-moz-font-feature-settings"?: import("csstype").FontFeatureSettingsProperty | undefined;
        "-moz-font-language-override"?: import("csstype").FontLanguageOverrideProperty | undefined;
        "-moz-hyphens"?: import("csstype").HyphensProperty | undefined;
        "-moz-image-region"?: import("csstype").MozImageRegionProperty | undefined;
        "-moz-margin-end"?: import("csstype").MarginInlineEndProperty<string | number> | undefined;
        "-moz-margin-start"?: import("csstype").MarginInlineStartProperty<string | number> | undefined;
        "-moz-orient"?: import("csstype").MozOrientProperty | undefined;
        "-moz-osx-font-smoothing"?: import("csstype").FontSmoothProperty<string | number> | undefined;
        "-moz-padding-end"?: import("csstype").PaddingInlineEndProperty<string | number> | undefined;
        "-moz-padding-start"?: import("csstype").PaddingInlineStartProperty<string | number> | undefined;
        "-moz-perspective"?: import("csstype").PerspectiveProperty<string | number> | undefined;
        "-moz-perspective-origin"?: import("csstype").PerspectiveOriginProperty<string | number> | undefined;
        "-moz-stack-sizing"?: import("csstype").MozStackSizingProperty | undefined;
        "-moz-tab-size"?: import("csstype").TabSizeProperty<string | number> | undefined;
        "-moz-text-blink"?: import("csstype").MozTextBlinkProperty | undefined;
        "-moz-text-size-adjust"?: import("csstype").TextSizeAdjustProperty | undefined;
        "-moz-transform-origin"?: import("csstype").TransformOriginProperty<string | number> | undefined;
        "-moz-transform-style"?: import("csstype").TransformStyleProperty | undefined;
        "-moz-transition-delay"?: import("csstype").GlobalsString | undefined;
        "-moz-transition-duration"?: import("csstype").GlobalsString | undefined;
        "-moz-transition-property"?: import("csstype").TransitionPropertyProperty | undefined;
        "-moz-transition-timing-function"?: import("csstype").TransitionTimingFunctionProperty | undefined;
        "-moz-user-focus"?: import("csstype").MozUserFocusProperty | undefined;
        "-moz-user-modify"?: import("csstype").MozUserModifyProperty | undefined;
        "-moz-user-select"?: import("csstype").UserSelectProperty | undefined;
        "-moz-window-dragging"?: import("csstype").MozWindowDraggingProperty | undefined;
        "-moz-window-shadow"?: import("csstype").MozWindowShadowProperty | undefined;
        "-ms-accelerator"?: import("csstype").MsAcceleratorProperty | undefined;
        "-ms-align-self"?: import("csstype").AlignSelfProperty | undefined;
        "-ms-block-progression"?: import("csstype").MsBlockProgressionProperty | undefined;
        "-ms-content-zoom-chaining"?: import("csstype").MsContentZoomChainingProperty | undefined;
        "-ms-content-zoom-limit-max"?: import("csstype").GlobalsString | undefined;
        "-ms-content-zoom-limit-min"?: import("csstype").GlobalsString | undefined;
        "-ms-content-zoom-snap-points"?: import("csstype").GlobalsString | undefined;
        "-ms-content-zoom-snap-type"?: import("csstype").MsContentZoomSnapTypeProperty | undefined;
        "-ms-content-zooming"?: import("csstype").MsContentZoomingProperty | undefined;
        "-ms-filter"?: import("csstype").GlobalsString | undefined;
        "-ms-flex-direction"?: import("csstype").FlexDirectionProperty | undefined;
        "-ms-flex-positive"?: import("csstype").GlobalsNumber | undefined;
        "-ms-flow-from"?: import("csstype").MsFlowFromProperty | undefined;
        "-ms-flow-into"?: import("csstype").MsFlowIntoProperty | undefined;
        "-ms-grid-columns"?: import("csstype").MsGridColumnsProperty<string | number> | undefined;
        "-ms-grid-rows"?: import("csstype").MsGridRowsProperty<string | number> | undefined;
        "-ms-high-contrast-adjust"?: import("csstype").MsHighContrastAdjustProperty | undefined;
        "-ms-hyphenate-limit-chars"?: import("csstype").MsHyphenateLimitCharsProperty | undefined;
        "-ms-hyphenate-limit-lines"?: import("csstype").MsHyphenateLimitLinesProperty | undefined;
        "-ms-hyphenate-limit-zone"?: import("csstype").MsHyphenateLimitZoneProperty<string | number> | undefined;
        "-ms-hyphens"?: import("csstype").HyphensProperty | undefined;
        "-ms-ime-align"?: import("csstype").MsImeAlignProperty | undefined;
        "-ms-justify-self"?: import("csstype").JustifySelfProperty | undefined;
        "-ms-line-break"?: import("csstype").LineBreakProperty | undefined;
        "-ms-order"?: import("csstype").GlobalsNumber | undefined;
        "-ms-overflow-style"?: import("csstype").MsOverflowStyleProperty | undefined;
        "-ms-overflow-x"?: import("csstype").OverflowXProperty | undefined;
        "-ms-overflow-y"?: import("csstype").OverflowYProperty | undefined;
        "-ms-scroll-chaining"?: import("csstype").MsScrollChainingProperty | undefined;
        "-ms-scroll-limit-x-max"?: import("csstype").MsScrollLimitXMaxProperty<string | number> | undefined;
        "-ms-scroll-limit-x-min"?: import("csstype").MsScrollLimitXMinProperty<string | number> | undefined;
        "-ms-scroll-limit-y-max"?: import("csstype").MsScrollLimitYMaxProperty<string | number> | undefined;
        "-ms-scroll-limit-y-min"?: import("csstype").MsScrollLimitYMinProperty<string | number> | undefined;
        "-ms-scroll-rails"?: import("csstype").MsScrollRailsProperty | undefined;
        "-ms-scroll-snap-points-x"?: import("csstype").GlobalsString | undefined;
        "-ms-scroll-snap-points-y"?: import("csstype").GlobalsString | undefined;
        "-ms-scroll-snap-type"?: import("csstype").MsScrollSnapTypeProperty | undefined;
        "-ms-scroll-translation"?: import("csstype").MsScrollTranslationProperty | undefined;
        "-ms-scrollbar-3dlight-color"?: import("csstype").MsScrollbar3dlightColorProperty | undefined;
        "-ms-scrollbar-arrow-color"?: import("csstype").MsScrollbarArrowColorProperty | undefined;
        "-ms-scrollbar-base-color"?: import("csstype").MsScrollbarBaseColorProperty | undefined;
        "-ms-scrollbar-darkshadow-color"?: import("csstype").MsScrollbarDarkshadowColorProperty | undefined;
        "-ms-scrollbar-face-color"?: import("csstype").MsScrollbarFaceColorProperty | undefined;
        "-ms-scrollbar-highlight-color"?: import("csstype").MsScrollbarHighlightColorProperty | undefined;
        "-ms-scrollbar-shadow-color"?: import("csstype").MsScrollbarShadowColorProperty | undefined;
        "-ms-text-autospace"?: import("csstype").MsTextAutospaceProperty | undefined;
        "-ms-text-combine-horizontal"?: import("csstype").TextCombineUprightProperty | undefined;
        "-ms-text-overflow"?: import("csstype").TextOverflowProperty | undefined;
        "-ms-touch-action"?: import("csstype").TouchActionProperty | undefined;
        "-ms-touch-select"?: import("csstype").MsTouchSelectProperty | undefined;
        "-ms-transform"?: import("csstype").TransformProperty | undefined;
        "-ms-transform-origin"?: import("csstype").TransformOriginProperty<string | number> | undefined;
        "-ms-transition-delay"?: import("csstype").GlobalsString | undefined;
        "-ms-transition-duration"?: import("csstype").GlobalsString | undefined;
        "-ms-transition-property"?: import("csstype").TransitionPropertyProperty | undefined;
        "-ms-transition-timing-function"?: import("csstype").TransitionTimingFunctionProperty | undefined;
        "-ms-user-select"?: import("csstype").MsUserSelectProperty | undefined;
        "-ms-word-break"?: import("csstype").WordBreakProperty | undefined;
        "-ms-wrap-flow"?: import("csstype").MsWrapFlowProperty | undefined;
        "-ms-wrap-margin"?: import("csstype").MsWrapMarginProperty<string | number> | undefined;
        "-ms-wrap-through"?: import("csstype").MsWrapThroughProperty | undefined;
        "-ms-writing-mode"?: import("csstype").WritingModeProperty | undefined;
        "-webkit-align-content"?: import("csstype").AlignContentProperty | undefined;
        "-webkit-align-items"?: import("csstype").AlignItemsProperty | undefined;
        "-webkit-align-self"?: import("csstype").AlignSelfProperty | undefined;
        "-webkit-animation-delay"?: import("csstype").GlobalsString | undefined;
        "-webkit-animation-direction"?: import("csstype").AnimationDirectionProperty | undefined;
        "-webkit-animation-duration"?: import("csstype").GlobalsString | undefined;
        "-webkit-animation-fill-mode"?: import("csstype").AnimationFillModeProperty | undefined;
        "-webkit-animation-iteration-count"?: import("csstype").AnimationIterationCountProperty | undefined;
        "-webkit-animation-name"?: import("csstype").AnimationNameProperty | undefined;
        "-webkit-animation-play-state"?: import("csstype").AnimationPlayStateProperty | undefined;
        "-webkit-animation-timing-function"?: import("csstype").AnimationTimingFunctionProperty | undefined;
        "-webkit-appearance"?: import("csstype").WebkitAppearanceProperty | undefined;
        "-webkit-backdrop-filter"?: import("csstype").BackdropFilterProperty | undefined;
        "-webkit-backface-visibility"?: import("csstype").BackfaceVisibilityProperty | undefined;
        "-webkit-background-clip"?: import("csstype").BackgroundClipProperty | undefined;
        "-webkit-background-origin"?: import("csstype").BackgroundOriginProperty | undefined;
        "-webkit-background-size"?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        "-webkit-border-before-color"?: import("csstype").WebkitBorderBeforeColorProperty | undefined;
        "-webkit-border-before-style"?: import("csstype").WebkitBorderBeforeStyleProperty | undefined;
        "-webkit-border-before-width"?: import("csstype").WebkitBorderBeforeWidthProperty<string | number> | undefined;
        "-webkit-border-bottom-left-radius"?: import("csstype").BorderBottomLeftRadiusProperty<string | number> | undefined;
        "-webkit-border-bottom-right-radius"?: import("csstype").BorderBottomRightRadiusProperty<string | number> | undefined;
        "-webkit-border-image-slice"?: import("csstype").BorderImageSliceProperty | undefined;
        "-webkit-border-top-left-radius"?: import("csstype").BorderTopLeftRadiusProperty<string | number> | undefined;
        "-webkit-border-top-right-radius"?: import("csstype").BorderTopRightRadiusProperty<string | number> | undefined;
        "-webkit-box-decoration-break"?: import("csstype").BoxDecorationBreakProperty | undefined;
        "-webkit-box-reflect"?: import("csstype").WebkitBoxReflectProperty<string | number> | undefined;
        "-webkit-box-shadow"?: import("csstype").BoxShadowProperty | undefined;
        "-webkit-box-sizing"?: import("csstype").BoxSizingProperty | undefined;
        "-webkit-clip-path"?: import("csstype").ClipPathProperty | undefined;
        "-webkit-column-count"?: import("csstype").ColumnCountProperty | undefined;
        "-webkit-column-fill"?: import("csstype").ColumnFillProperty | undefined;
        "-webkit-column-gap"?: import("csstype").ColumnGapProperty<string | number> | undefined;
        "-webkit-column-rule-color"?: import("csstype").ColumnRuleColorProperty | undefined;
        "-webkit-column-rule-style"?: import("csstype").ColumnRuleStyleProperty | undefined;
        "-webkit-column-rule-width"?: import("csstype").ColumnRuleWidthProperty<string | number> | undefined;
        "-webkit-column-span"?: import("csstype").ColumnSpanProperty | undefined;
        "-webkit-column-width"?: import("csstype").ColumnWidthProperty<string | number> | undefined;
        "-webkit-filter"?: import("csstype").FilterProperty | undefined;
        "-webkit-flex-basis"?: import("csstype").FlexBasisProperty<string | number> | undefined;
        "-webkit-flex-direction"?: import("csstype").FlexDirectionProperty | undefined;
        "-webkit-flex-grow"?: import("csstype").GlobalsNumber | undefined;
        "-webkit-flex-shrink"?: import("csstype").GlobalsNumber | undefined;
        "-webkit-flex-wrap"?: import("csstype").FlexWrapProperty | undefined;
        "-webkit-font-feature-settings"?: import("csstype").FontFeatureSettingsProperty | undefined;
        "-webkit-font-kerning"?: import("csstype").FontKerningProperty | undefined;
        "-webkit-font-smoothing"?: import("csstype").FontSmoothProperty<string | number> | undefined;
        "-webkit-font-variant-ligatures"?: import("csstype").FontVariantLigaturesProperty | undefined;
        "-webkit-hyphenate-character"?: import("csstype").HyphenateCharacterProperty | undefined;
        "-webkit-hyphens"?: import("csstype").HyphensProperty | undefined;
        "-webkit-initial-letter"?: import("csstype").InitialLetterProperty | undefined;
        "-webkit-justify-content"?: import("csstype").JustifyContentProperty | undefined;
        "-webkit-line-break"?: import("csstype").LineBreakProperty | undefined;
        "-webkit-line-clamp"?: import("csstype").WebkitLineClampProperty | undefined;
        "-webkit-margin-end"?: import("csstype").MarginInlineEndProperty<string | number> | undefined;
        "-webkit-margin-start"?: import("csstype").MarginInlineStartProperty<string | number> | undefined;
        "-webkit-mask-attachment"?: import("csstype").WebkitMaskAttachmentProperty | undefined;
        "-webkit-mask-box-image-outset"?: import("csstype").MaskBorderOutsetProperty<string | number> | undefined;
        "-webkit-mask-box-image-repeat"?: import("csstype").MaskBorderRepeatProperty | undefined;
        "-webkit-mask-box-image-slice"?: import("csstype").MaskBorderSliceProperty | undefined;
        "-webkit-mask-box-image-source"?: import("csstype").MaskBorderSourceProperty | undefined;
        "-webkit-mask-box-image-width"?: import("csstype").MaskBorderWidthProperty<string | number> | undefined;
        "-webkit-mask-clip"?: import("csstype").WebkitMaskClipProperty | undefined;
        "-webkit-mask-composite"?: import("csstype").WebkitMaskCompositeProperty | undefined;
        "-webkit-mask-image"?: import("csstype").WebkitMaskImageProperty | undefined;
        "-webkit-mask-origin"?: import("csstype").WebkitMaskOriginProperty | undefined;
        "-webkit-mask-position"?: import("csstype").WebkitMaskPositionProperty<string | number> | undefined;
        "-webkit-mask-position-x"?: import("csstype").WebkitMaskPositionXProperty<string | number> | undefined;
        "-webkit-mask-position-y"?: import("csstype").WebkitMaskPositionYProperty<string | number> | undefined;
        "-webkit-mask-repeat"?: import("csstype").WebkitMaskRepeatProperty | undefined;
        "-webkit-mask-repeat-x"?: import("csstype").WebkitMaskRepeatXProperty | undefined;
        "-webkit-mask-repeat-y"?: import("csstype").WebkitMaskRepeatYProperty | undefined;
        "-webkit-mask-size"?: import("csstype").WebkitMaskSizeProperty<string | number> | undefined;
        "-webkit-max-inline-size"?: import("csstype").MaxInlineSizeProperty<string | number> | undefined;
        "-webkit-order"?: import("csstype").GlobalsNumber | undefined;
        "-webkit-overflow-scrolling"?: import("csstype").WebkitOverflowScrollingProperty | undefined;
        "-webkit-padding-end"?: import("csstype").PaddingInlineEndProperty<string | number> | undefined;
        "-webkit-padding-start"?: import("csstype").PaddingInlineStartProperty<string | number> | undefined;
        "-webkit-perspective"?: import("csstype").PerspectiveProperty<string | number> | undefined;
        "-webkit-perspective-origin"?: import("csstype").PerspectiveOriginProperty<string | number> | undefined;
        "-webkit-print-color-adjust"?: import("csstype").PrintColorAdjustProperty | undefined;
        "-webkit-ruby-position"?: import("csstype").RubyPositionProperty | undefined;
        "-webkit-scroll-snap-type"?: import("csstype").ScrollSnapTypeProperty | undefined;
        "-webkit-shape-margin"?: import("csstype").ShapeMarginProperty<string | number> | undefined;
        "-webkit-tap-highlight-color"?: import("csstype").WebkitTapHighlightColorProperty | undefined;
        "-webkit-text-combine"?: import("csstype").TextCombineUprightProperty | undefined;
        "-webkit-text-decoration-color"?: import("csstype").TextDecorationColorProperty | undefined;
        "-webkit-text-decoration-line"?: import("csstype").TextDecorationLineProperty | undefined;
        "-webkit-text-decoration-skip"?: import("csstype").TextDecorationSkipProperty | undefined;
        "-webkit-text-decoration-style"?: import("csstype").TextDecorationStyleProperty | undefined;
        "-webkit-text-emphasis-color"?: import("csstype").TextEmphasisColorProperty | undefined;
        "-webkit-text-emphasis-position"?: import("csstype").GlobalsString | undefined;
        "-webkit-text-emphasis-style"?: import("csstype").TextEmphasisStyleProperty | undefined;
        "-webkit-text-fill-color"?: import("csstype").WebkitTextFillColorProperty | undefined;
        "-webkit-text-orientation"?: import("csstype").TextOrientationProperty | undefined;
        "-webkit-text-size-adjust"?: import("csstype").TextSizeAdjustProperty | undefined;
        "-webkit-text-stroke-color"?: import("csstype").WebkitTextStrokeColorProperty | undefined;
        "-webkit-text-stroke-width"?: import("csstype").WebkitTextStrokeWidthProperty<string | number> | undefined;
        "-webkit-text-underline-position"?: import("csstype").TextUnderlinePositionProperty | undefined;
        "-webkit-touch-callout"?: import("csstype").WebkitTouchCalloutProperty | undefined;
        "-webkit-transform"?: import("csstype").TransformProperty | undefined;
        "-webkit-transform-origin"?: import("csstype").TransformOriginProperty<string | number> | undefined;
        "-webkit-transform-style"?: import("csstype").TransformStyleProperty | undefined;
        "-webkit-transition-delay"?: import("csstype").GlobalsString | undefined;
        "-webkit-transition-duration"?: import("csstype").GlobalsString | undefined;
        "-webkit-transition-property"?: import("csstype").TransitionPropertyProperty | undefined;
        "-webkit-transition-timing-function"?: import("csstype").TransitionTimingFunctionProperty | undefined;
        "-webkit-user-modify"?: import("csstype").WebkitUserModifyProperty | undefined;
        "-webkit-user-select"?: import("csstype").UserSelectProperty | undefined;
        "-webkit-writing-mode"?: import("csstype").WritingModeProperty | undefined;
        "-moz-animation"?: import("csstype").AnimationProperty | undefined;
        "-moz-border-image"?: import("csstype").BorderImageProperty | undefined;
        "-moz-column-rule"?: import("csstype").ColumnRuleProperty<string | number> | undefined;
        "-moz-columns"?: import("csstype").ColumnsProperty<string | number> | undefined;
        "-moz-transition"?: import("csstype").TransitionProperty | undefined;
        "-ms-content-zoom-limit"?: import("csstype").GlobalsString | undefined;
        "-ms-content-zoom-snap"?: import("csstype").MsContentZoomSnapProperty | undefined;
        "-ms-flex"?: import("csstype").FlexProperty<string | number> | undefined;
        "-ms-scroll-limit"?: import("csstype").GlobalsString | undefined;
        "-ms-scroll-snap-x"?: import("csstype").GlobalsString | undefined;
        "-ms-scroll-snap-y"?: import("csstype").GlobalsString | undefined;
        "-ms-transition"?: import("csstype").TransitionProperty | undefined;
        "-webkit-animation"?: import("csstype").AnimationProperty | undefined;
        "-webkit-border-before"?: import("csstype").WebkitBorderBeforeProperty<string | number> | undefined;
        "-webkit-border-image"?: import("csstype").BorderImageProperty | undefined;
        "-webkit-border-radius"?: import("csstype").BorderRadiusProperty<string | number> | undefined;
        "-webkit-column-rule"?: import("csstype").ColumnRuleProperty<string | number> | undefined;
        "-webkit-columns"?: import("csstype").ColumnsProperty<string | number> | undefined;
        "-webkit-flex"?: import("csstype").FlexProperty<string | number> | undefined;
        "-webkit-flex-flow"?: import("csstype").FlexFlowProperty | undefined;
        "-webkit-mask"?: import("csstype").WebkitMaskProperty<string | number> | undefined;
        "-webkit-mask-box-image"?: import("csstype").MaskBorderProperty | undefined;
        "-webkit-text-emphasis"?: import("csstype").TextEmphasisProperty | undefined;
        "-webkit-text-stroke"?: import("csstype").WebkitTextStrokeProperty<string | number> | undefined;
        "-webkit-transition"?: import("csstype").TransitionProperty | undefined;
        "box-align"?: import("csstype").BoxAlignProperty | undefined;
        "box-direction"?: import("csstype").BoxDirectionProperty | undefined;
        "box-flex"?: import("csstype").GlobalsNumber | undefined;
        "box-flex-group"?: import("csstype").GlobalsNumber | undefined;
        "box-lines"?: import("csstype").BoxLinesProperty | undefined;
        "box-ordinal-group"?: import("csstype").GlobalsNumber | undefined;
        "box-orient"?: import("csstype").BoxOrientProperty | undefined;
        "box-pack"?: import("csstype").BoxPackProperty | undefined;
        "grid-column-gap"?: import("csstype").GridColumnGapProperty<string | number> | undefined;
        "grid-gap"?: import("csstype").GridGapProperty<string | number> | undefined;
        "grid-row-gap"?: import("csstype").GridRowGapProperty<string | number> | undefined;
        "ime-mode"?: import("csstype").ImeModeProperty | undefined;
        "offset-block"?: import("csstype").InsetBlockProperty<string | number> | undefined;
        "offset-block-end"?: import("csstype").InsetBlockEndProperty<string | number> | undefined;
        "offset-block-start"?: import("csstype").InsetBlockStartProperty<string | number> | undefined;
        "offset-inline"?: import("csstype").InsetInlineProperty<string | number> | undefined;
        "offset-inline-end"?: import("csstype").InsetInlineEndProperty<string | number> | undefined;
        "offset-inline-start"?: import("csstype").InsetInlineStartProperty<string | number> | undefined;
        "scroll-snap-coordinate"?: import("csstype").ScrollSnapCoordinateProperty<string | number> | undefined;
        "scroll-snap-destination"?: import("csstype").ScrollSnapDestinationProperty<string | number> | undefined;
        "scroll-snap-points-x"?: import("csstype").ScrollSnapPointsXProperty | undefined;
        "scroll-snap-points-y"?: import("csstype").ScrollSnapPointsYProperty | undefined;
        "scroll-snap-type-x"?: import("csstype").ScrollSnapTypeXProperty | undefined;
        "scroll-snap-type-y"?: import("csstype").ScrollSnapTypeYProperty | undefined;
        "scrollbar-track-color"?: import("csstype").MsScrollbarTrackColorProperty | undefined;
        "-khtml-box-align"?: import("csstype").BoxAlignProperty | undefined;
        "-khtml-box-direction"?: import("csstype").BoxDirectionProperty | undefined;
        "-khtml-box-flex"?: import("csstype").GlobalsNumber | undefined;
        "-khtml-box-flex-group"?: import("csstype").GlobalsNumber | undefined;
        "-khtml-box-lines"?: import("csstype").BoxLinesProperty | undefined;
        "-khtml-box-ordinal-group"?: import("csstype").GlobalsNumber | undefined;
        "-khtml-box-orient"?: import("csstype").BoxOrientProperty | undefined;
        "-khtml-box-pack"?: import("csstype").BoxPackProperty | undefined;
        "-khtml-line-break"?: import("csstype").LineBreakProperty | undefined;
        "-khtml-opacity"?: import("csstype").OpacityProperty | undefined;
        "-khtml-user-select"?: import("csstype").UserSelectProperty | undefined;
        "-moz-background-clip"?: import("csstype").BackgroundClipProperty | undefined;
        "-moz-background-inline-policy"?: import("csstype").BoxDecorationBreakProperty | undefined;
        "-moz-background-origin"?: import("csstype").BackgroundOriginProperty | undefined;
        "-moz-background-size"?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        "-moz-binding"?: import("csstype").MozBindingProperty | undefined;
        "-moz-border-radius"?: import("csstype").BorderRadiusProperty<string | number> | undefined;
        "-moz-border-radius-bottomleft"?: import("csstype").BorderBottomLeftRadiusProperty<string | number> | undefined;
        "-moz-border-radius-bottomright"?: import("csstype").BorderBottomRightRadiusProperty<string | number> | undefined;
        "-moz-border-radius-topleft"?: import("csstype").BorderTopLeftRadiusProperty<string | number> | undefined;
        "-moz-border-radius-topright"?: import("csstype").BorderTopRightRadiusProperty<string | number> | undefined;
        "-moz-box-align"?: import("csstype").BoxAlignProperty | undefined;
        "-moz-box-direction"?: import("csstype").BoxDirectionProperty | undefined;
        "-moz-box-flex"?: import("csstype").GlobalsNumber | undefined;
        "-moz-box-ordinal-group"?: import("csstype").GlobalsNumber | undefined;
        "-moz-box-orient"?: import("csstype").BoxOrientProperty | undefined;
        "-moz-box-pack"?: import("csstype").BoxPackProperty | undefined;
        "-moz-box-shadow"?: import("csstype").BoxShadowProperty | undefined;
        "-moz-float-edge"?: import("csstype").MozFloatEdgeProperty | undefined;
        "-moz-force-broken-image-icon"?: import("csstype").MozForceBrokenImageIconProperty | undefined;
        "-moz-opacity"?: import("csstype").OpacityProperty | undefined;
        "-moz-outline"?: import("csstype").OutlineProperty<string | number> | undefined;
        "-moz-outline-color"?: import("csstype").OutlineColorProperty | undefined;
        "-moz-outline-radius"?: import("csstype").MozOutlineRadiusProperty<string | number> | undefined;
        "-moz-outline-radius-bottomleft"?: import("csstype").MozOutlineRadiusBottomleftProperty<string | number> | undefined;
        "-moz-outline-radius-bottomright"?: import("csstype").MozOutlineRadiusBottomrightProperty<string | number> | undefined;
        "-moz-outline-radius-topleft"?: import("csstype").MozOutlineRadiusTopleftProperty<string | number> | undefined;
        "-moz-outline-radius-topright"?: import("csstype").MozOutlineRadiusToprightProperty<string | number> | undefined;
        "-moz-outline-style"?: import("csstype").OutlineStyleProperty | undefined;
        "-moz-outline-width"?: import("csstype").OutlineWidthProperty<string | number> | undefined;
        "-moz-text-align-last"?: import("csstype").TextAlignLastProperty | undefined;
        "-moz-text-decoration-color"?: import("csstype").TextDecorationColorProperty | undefined;
        "-moz-text-decoration-line"?: import("csstype").TextDecorationLineProperty | undefined;
        "-moz-text-decoration-style"?: import("csstype").TextDecorationStyleProperty | undefined;
        "-moz-user-input"?: import("csstype").MozUserInputProperty | undefined;
        "-ms-ime-mode"?: import("csstype").ImeModeProperty | undefined;
        "-ms-scrollbar-track-color"?: import("csstype").MsScrollbarTrackColorProperty | undefined;
        "-o-animation"?: import("csstype").AnimationProperty | undefined;
        "-o-animation-delay"?: import("csstype").GlobalsString | undefined;
        "-o-animation-direction"?: import("csstype").AnimationDirectionProperty | undefined;
        "-o-animation-duration"?: import("csstype").GlobalsString | undefined;
        "-o-animation-fill-mode"?: import("csstype").AnimationFillModeProperty | undefined;
        "-o-animation-iteration-count"?: import("csstype").AnimationIterationCountProperty | undefined;
        "-o-animation-name"?: import("csstype").AnimationNameProperty | undefined;
        "-o-animation-play-state"?: import("csstype").AnimationPlayStateProperty | undefined;
        "-o-animation-timing-function"?: import("csstype").AnimationTimingFunctionProperty | undefined;
        "-o-background-size"?: import("csstype").BackgroundSizeProperty<string | number> | undefined;
        "-o-border-image"?: import("csstype").BorderImageProperty | undefined;
        "-o-object-fit"?: import("csstype").ObjectFitProperty | undefined;
        "-o-object-position"?: import("csstype").ObjectPositionProperty<string | number> | undefined;
        "-o-tab-size"?: import("csstype").TabSizeProperty<string | number> | undefined;
        "-o-text-overflow"?: import("csstype").TextOverflowProperty | undefined;
        "-o-transform"?: import("csstype").TransformProperty | undefined;
        "-o-transform-origin"?: import("csstype").TransformOriginProperty<string | number> | undefined;
        "-o-transition"?: import("csstype").TransitionProperty | undefined;
        "-o-transition-delay"?: import("csstype").GlobalsString | undefined;
        "-o-transition-duration"?: import("csstype").GlobalsString | undefined;
        "-o-transition-property"?: import("csstype").TransitionPropertyProperty | undefined;
        "-o-transition-timing-function"?: import("csstype").TransitionTimingFunctionProperty | undefined;
        "-webkit-box-align"?: import("csstype").BoxAlignProperty | undefined;
        "-webkit-box-direction"?: import("csstype").BoxDirectionProperty | undefined;
        "-webkit-box-flex"?: import("csstype").GlobalsNumber | undefined;
        "-webkit-box-flex-group"?: import("csstype").GlobalsNumber | undefined;
        "-webkit-box-lines"?: import("csstype").BoxLinesProperty | undefined;
        "-webkit-box-ordinal-group"?: import("csstype").GlobalsNumber | undefined;
        "-webkit-box-orient"?: import("csstype").BoxOrientProperty | undefined;
        "-webkit-box-pack"?: import("csstype").BoxPackProperty | undefined;
        "-webkit-scroll-snap-points-x"?: import("csstype").ScrollSnapPointsXProperty | undefined;
        "-webkit-scroll-snap-points-y"?: import("csstype").ScrollSnapPointsYProperty | undefined;
        "alignment-baseline"?: import("csstype").AlignmentBaselineProperty | undefined;
        "baseline-shift"?: import("csstype").BaselineShiftProperty<string | number> | undefined;
        "clip-rule"?: import("csstype").ClipRuleProperty | undefined;
        "color-interpolation"?: import("csstype").ColorInterpolationProperty | undefined;
        "color-rendering"?: import("csstype").ColorRenderingProperty | undefined;
        "dominant-baseline"?: import("csstype").DominantBaselineProperty | undefined;
        "fill-opacity"?: import("csstype").GlobalsNumber | undefined;
        "fill-rule"?: import("csstype").FillRuleProperty | undefined;
        "flood-color"?: import("csstype").FloodColorProperty | undefined;
        "flood-opacity"?: import("csstype").GlobalsNumber | undefined;
        "glyph-orientation-vertical"?: import("csstype").GlyphOrientationVerticalProperty | undefined;
        "lighting-color"?: import("csstype").LightingColorProperty | undefined;
        "marker-end"?: import("csstype").MarkerEndProperty | undefined;
        "marker-mid"?: import("csstype").MarkerMidProperty | undefined;
        "marker-start"?: import("csstype").MarkerStartProperty | undefined;
        "shape-rendering"?: import("csstype").ShapeRenderingProperty | undefined;
        "stop-color"?: import("csstype").StopColorProperty | undefined;
        "stop-opacity"?: import("csstype").GlobalsNumber | undefined;
        "stroke-dasharray"?: import("csstype").StrokeDasharrayProperty<string | number> | undefined;
        "stroke-dashoffset"?: import("csstype").StrokeDashoffsetProperty<string | number> | undefined;
        "stroke-linecap"?: import("csstype").StrokeLinecapProperty | undefined;
        "stroke-linejoin"?: import("csstype").StrokeLinejoinProperty | undefined;
        "stroke-miterlimit"?: import("csstype").GlobalsNumber | undefined;
        "stroke-opacity"?: import("csstype").GlobalsNumber | undefined;
        "stroke-width"?: import("csstype").StrokeWidthProperty<string | number> | undefined;
        "text-anchor"?: import("csstype").TextAnchorProperty | undefined;
        "vector-effect"?: import("csstype").VectorEffectProperty | undefined;
    }>;
    showInput: import("vue").Ref<boolean>;
    inputValue: import("vue").Ref<string>;
    inputPlaceholder: import("vue").Ref<string>;
    inputType: import("vue").Ref<string>;
    inputPattern: import("vue").Ref<{
        exec: (string: string) => RegExpExecArray | null;
        test: (string: string) => boolean;
        readonly source: string;
        readonly global: boolean;
        readonly ignoreCase: boolean;
        readonly multiline: boolean;
        lastIndex: number;
        compile: (pattern: string, flags?: string) => RegExp;
        readonly flags: string;
        readonly sticky: boolean;
        readonly unicode: boolean;
        readonly dotAll: boolean;
        [Symbol.match]: (string: string) => RegExpMatchArray | null;
        [Symbol.replace]: {
            (string: string, replaceValue: string): string;
            (string: string, replacer: (substring: string, ...args: any[]) => string): string;
        };
        [Symbol.search]: (string: string) => number;
        [Symbol.split]: (string: string, limit?: number) => string[];
    } | null>;
    inputValidator: import("vue").Ref<import("./message-box.type").MessageBoxInputValidator>;
    inputErrorMessage: import("vue").Ref<string>;
    showConfirmButton: import("vue").Ref<boolean>;
    showCancelButton: import("vue").Ref<boolean>;
    action: import("vue").Ref<Action>;
    dangerouslyUseHTMLString: import("vue").Ref<boolean>;
    confirmButtonText: import("vue").Ref<string>;
    cancelButtonText: import("vue").Ref<string>;
    confirmButtonLoading: import("vue").Ref<boolean>;
    cancelButtonLoading: import("vue").Ref<boolean>;
    confirmButtonLoadingIcon: import("vue").Ref<string | import("vue").FunctionalComponent<any, any> | {
        new (...args: any[]): any;
        __isFragment?: never;
        __isTeleport?: never;
        __isSuspense?: never;
    } | {
        [x: string]: any;
        setup?: ((this: void, props: Readonly<import("@vue/shared").LooseRequired<any>>, ctx: import("vue").SetupContext<any>) => any) | undefined;
        name?: string | undefined;
        template?: (string | object) | undefined;
        render?: Function | undefined;
        components?: Record<string, import("vue").Component> | undefined;
        directives?: Record<string, import("vue").Directive> | undefined;
        inheritAttrs?: boolean | undefined;
        emits?: any;
        expose?: string[] | undefined;
        serverPrefetch?: (() => Promise<any>) | undefined;
        compilerOptions?: {
            isCustomElement?: ((tag: string) => boolean) | undefined;
            whitespace?: ("preserve" | "condense") | undefined;
            comments?: boolean | undefined;
            delimiters?: [string, string] | undefined;
        } | undefined;
        call?: ((this: unknown, ...args: unknown[]) => never) | undefined;
        __isFragment?: never | undefined;
        __isTeleport?: never | undefined;
        __isSuspense?: never | undefined;
        __defaults?: {} | undefined;
        compatConfig?: {
            GLOBAL_MOUNT?: boolean | "suppress-warning" | undefined;
            GLOBAL_MOUNT_CONTAINER?: boolean | "suppress-warning" | undefined;
            GLOBAL_EXTEND?: boolean | "suppress-warning" | undefined;
            GLOBAL_PROTOTYPE?: boolean | "suppress-warning" | undefined;
            GLOBAL_SET?: boolean | "suppress-warning" | undefined;
            GLOBAL_DELETE?: boolean | "suppress-warning" | undefined;
            GLOBAL_OBSERVABLE?: boolean | "suppress-warning" | undefined;
            GLOBAL_PRIVATE_UTIL?: boolean | "suppress-warning" | undefined;
            CONFIG_SILENT?: boolean | "suppress-warning" | undefined;
            CONFIG_DEVTOOLS?: boolean | "suppress-warning" | undefined;
            CONFIG_KEY_CODES?: boolean | "suppress-warning" | undefined;
            CONFIG_PRODUCTION_TIP?: boolean | "suppress-warning" | undefined;
            CONFIG_IGNORED_ELEMENTS?: boolean | "suppress-warning" | undefined;
            CONFIG_WHITESPACE?: boolean | "suppress-warning" | undefined;
            CONFIG_OPTION_MERGE_STRATS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SET?: boolean | "suppress-warning" | undefined;
            INSTANCE_DELETE?: boolean | "suppress-warning" | undefined;
            INSTANCE_DESTROY?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_EMITTER?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_HOOKS?: boolean | "suppress-warning" | undefined;
            INSTANCE_CHILDREN?: boolean | "suppress-warning" | undefined;
            INSTANCE_LISTENERS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SCOPED_SLOTS?: boolean | "suppress-warning" | undefined;
            INSTANCE_ATTRS_CLASS_STYLE?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_FN?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_MERGE?: boolean | "suppress-warning" | undefined;
            OPTIONS_BEFORE_DESTROY?: boolean | "suppress-warning" | undefined;
            OPTIONS_DESTROYED?: boolean | "suppress-warning" | undefined;
            WATCH_ARRAY?: boolean | "suppress-warning" | undefined;
            PROPS_DEFAULT_THIS?: boolean | "suppress-warning" | undefined;
            V_ON_KEYCODE_MODIFIER?: boolean | "suppress-warning" | undefined;
            CUSTOM_DIR?: boolean | "suppress-warning" | undefined;
            ATTR_FALSE_VALUE?: boolean | "suppress-warning" | undefined;
            ATTR_ENUMERATED_COERCION?: boolean | "suppress-warning" | undefined;
            TRANSITION_CLASSES?: boolean | "suppress-warning" | undefined;
            TRANSITION_GROUP_ROOT?: boolean | "suppress-warning" | undefined;
            COMPONENT_ASYNC?: boolean | "suppress-warning" | undefined;
            COMPONENT_FUNCTIONAL?: boolean | "suppress-warning" | undefined;
            COMPONENT_V_MODEL?: boolean | "suppress-warning" | undefined;
            RENDER_FUNCTION?: boolean | "suppress-warning" | undefined;
            FILTERS?: boolean | "suppress-warning" | undefined;
            PRIVATE_APIS?: boolean | "suppress-warning" | undefined;
            MODE?: (2 | 3 | ((comp: import("vue").Component | null) => 2 | 3)) | undefined;
        } | undefined;
        data?: ((this: any, vm: any) => any) | undefined;
        computed?: import("vue").ComputedOptions | undefined;
        methods?: import("vue").MethodOptions | undefined;
        watch?: {
            [x: string]: (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>)) | (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>))[];
        } | undefined;
        provide?: import("vue").ComponentProvideOptions | undefined;
        inject?: (string[] | {
            [x: string]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
            [x: symbol]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
        }) | undefined;
        filters?: Record<string, Function> | undefined;
        mixins?: any[] | undefined;
        extends?: any;
        beforeCreate?: (() => void) | undefined;
        created?: (() => void) | undefined;
        beforeMount?: (() => void) | undefined;
        mounted?: (() => void) | undefined;
        beforeUpdate?: (() => void) | undefined;
        updated?: (() => void) | undefined;
        activated?: (() => void) | undefined;
        deactivated?: (() => void) | undefined;
        beforeDestroy?: (() => void) | undefined;
        beforeUnmount?: (() => void) | undefined;
        destroyed?: (() => void) | undefined;
        unmounted?: (() => void) | undefined;
        renderTracked?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | undefined;
        delimiters?: [string, string] | undefined;
        __differentiator?: string | number | symbol | undefined;
        __isBuiltIn?: boolean | undefined;
        __file?: string | undefined;
        __name?: string | undefined;
        beforeRouteEnter?: import("vue-router").NavigationGuardWithThis<undefined> | undefined;
        beforeRouteUpdate?: import("vue-router").NavigationGuard | undefined;
        beforeRouteLeave?: import("vue-router").NavigationGuard | undefined;
    }>;
    cancelButtonLoadingIcon: import("vue").Ref<string | import("vue").FunctionalComponent<any, any> | {
        new (...args: any[]): any;
        __isFragment?: never;
        __isTeleport?: never;
        __isSuspense?: never;
    } | {
        [x: string]: any;
        setup?: ((this: void, props: Readonly<import("@vue/shared").LooseRequired<any>>, ctx: import("vue").SetupContext<any>) => any) | undefined;
        name?: string | undefined;
        template?: (string | object) | undefined;
        render?: Function | undefined;
        components?: Record<string, import("vue").Component> | undefined;
        directives?: Record<string, import("vue").Directive> | undefined;
        inheritAttrs?: boolean | undefined;
        emits?: any;
        expose?: string[] | undefined;
        serverPrefetch?: (() => Promise<any>) | undefined;
        compilerOptions?: {
            isCustomElement?: ((tag: string) => boolean) | undefined;
            whitespace?: ("preserve" | "condense") | undefined;
            comments?: boolean | undefined;
            delimiters?: [string, string] | undefined;
        } | undefined;
        call?: ((this: unknown, ...args: unknown[]) => never) | undefined;
        __isFragment?: never | undefined;
        __isTeleport?: never | undefined;
        __isSuspense?: never | undefined;
        __defaults?: {} | undefined;
        compatConfig?: {
            GLOBAL_MOUNT?: boolean | "suppress-warning" | undefined;
            GLOBAL_MOUNT_CONTAINER?: boolean | "suppress-warning" | undefined;
            GLOBAL_EXTEND?: boolean | "suppress-warning" | undefined;
            GLOBAL_PROTOTYPE?: boolean | "suppress-warning" | undefined;
            GLOBAL_SET?: boolean | "suppress-warning" | undefined;
            GLOBAL_DELETE?: boolean | "suppress-warning" | undefined;
            GLOBAL_OBSERVABLE?: boolean | "suppress-warning" | undefined;
            GLOBAL_PRIVATE_UTIL?: boolean | "suppress-warning" | undefined;
            CONFIG_SILENT?: boolean | "suppress-warning" | undefined;
            CONFIG_DEVTOOLS?: boolean | "suppress-warning" | undefined;
            CONFIG_KEY_CODES?: boolean | "suppress-warning" | undefined;
            CONFIG_PRODUCTION_TIP?: boolean | "suppress-warning" | undefined;
            CONFIG_IGNORED_ELEMENTS?: boolean | "suppress-warning" | undefined;
            CONFIG_WHITESPACE?: boolean | "suppress-warning" | undefined;
            CONFIG_OPTION_MERGE_STRATS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SET?: boolean | "suppress-warning" | undefined;
            INSTANCE_DELETE?: boolean | "suppress-warning" | undefined;
            INSTANCE_DESTROY?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_EMITTER?: boolean | "suppress-warning" | undefined;
            INSTANCE_EVENT_HOOKS?: boolean | "suppress-warning" | undefined;
            INSTANCE_CHILDREN?: boolean | "suppress-warning" | undefined;
            INSTANCE_LISTENERS?: boolean | "suppress-warning" | undefined;
            INSTANCE_SCOPED_SLOTS?: boolean | "suppress-warning" | undefined;
            INSTANCE_ATTRS_CLASS_STYLE?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_FN?: boolean | "suppress-warning" | undefined;
            OPTIONS_DATA_MERGE?: boolean | "suppress-warning" | undefined;
            OPTIONS_BEFORE_DESTROY?: boolean | "suppress-warning" | undefined;
            OPTIONS_DESTROYED?: boolean | "suppress-warning" | undefined;
            WATCH_ARRAY?: boolean | "suppress-warning" | undefined;
            PROPS_DEFAULT_THIS?: boolean | "suppress-warning" | undefined;
            V_ON_KEYCODE_MODIFIER?: boolean | "suppress-warning" | undefined;
            CUSTOM_DIR?: boolean | "suppress-warning" | undefined;
            ATTR_FALSE_VALUE?: boolean | "suppress-warning" | undefined;
            ATTR_ENUMERATED_COERCION?: boolean | "suppress-warning" | undefined;
            TRANSITION_CLASSES?: boolean | "suppress-warning" | undefined;
            TRANSITION_GROUP_ROOT?: boolean | "suppress-warning" | undefined;
            COMPONENT_ASYNC?: boolean | "suppress-warning" | undefined;
            COMPONENT_FUNCTIONAL?: boolean | "suppress-warning" | undefined;
            COMPONENT_V_MODEL?: boolean | "suppress-warning" | undefined;
            RENDER_FUNCTION?: boolean | "suppress-warning" | undefined;
            FILTERS?: boolean | "suppress-warning" | undefined;
            PRIVATE_APIS?: boolean | "suppress-warning" | undefined;
            MODE?: (2 | 3 | ((comp: import("vue").Component | null) => 2 | 3)) | undefined;
        } | undefined;
        data?: ((this: any, vm: any) => any) | undefined;
        computed?: import("vue").ComputedOptions | undefined;
        methods?: import("vue").MethodOptions | undefined;
        watch?: {
            [x: string]: (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>)) | (string | import("vue").WatchCallback<any, any> | ({
                handler: import("vue").WatchCallback | string;
            } & import("vue").WatchOptions<boolean>))[];
        } | undefined;
        provide?: import("vue").ComponentProvideOptions | undefined;
        inject?: (string[] | {
            [x: string]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
            [x: symbol]: string | symbol | {
                from?: string | symbol;
                default?: unknown;
            };
        }) | undefined;
        filters?: Record<string, Function> | undefined;
        mixins?: any[] | undefined;
        extends?: any;
        beforeCreate?: (() => void) | undefined;
        created?: (() => void) | undefined;
        beforeMount?: (() => void) | undefined;
        mounted?: (() => void) | undefined;
        beforeUpdate?: (() => void) | undefined;
        updated?: (() => void) | undefined;
        activated?: (() => void) | undefined;
        deactivated?: (() => void) | undefined;
        beforeDestroy?: (() => void) | undefined;
        beforeUnmount?: (() => void) | undefined;
        destroyed?: (() => void) | undefined;
        unmounted?: (() => void) | undefined;
        renderTracked?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | undefined;
        errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | undefined;
        delimiters?: [string, string] | undefined;
        __differentiator?: string | number | symbol | undefined;
        __isBuiltIn?: boolean | undefined;
        __file?: string | undefined;
        __name?: string | undefined;
        beforeRouteEnter?: import("vue-router").NavigationGuardWithThis<undefined> | undefined;
        beforeRouteUpdate?: import("vue-router").NavigationGuard | undefined;
        beforeRouteLeave?: import("vue-router").NavigationGuard | undefined;
    }>;
    confirmButtonClass: import("vue").Ref<string>;
    confirmButtonDisabled: import("vue").Ref<boolean>;
    cancelButtonClass: import("vue").Ref<string>;
    editorErrorMessage: import("vue").Ref<string>;
    beforeClose: import("vue").Ref<((action: Action, instance: MessageBoxState, done: () => void) => void) | null>;
    callback: import("vue").Ref<import("./message-box.type").Callback | null>;
    distinguishCancelAndClose: import("vue").Ref<boolean>;
    modalFade: import("vue").Ref<boolean>;
    modalClass: import("vue").Ref<string>;
    validateError: import("vue").Ref<boolean>;
    zIndex: import("vue").Ref<number>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("action" | "vanish")[], "action" | "vanish", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    buttonSize: {
        type: PropType<ComponentSize>;
        validator: (val: string) => val is ComponentSize | "";
    };
    modal: {
        type: BooleanConstructor;
        default: boolean;
    };
    lockScroll: {
        type: BooleanConstructor;
        default: boolean;
    };
    showClose: {
        type: BooleanConstructor;
        default: boolean;
    };
    closeOnClickModal: {
        type: BooleanConstructor;
        default: boolean;
    };
    closeOnPressEscape: {
        type: BooleanConstructor;
        default: boolean;
    };
    closeOnHashChange: {
        type: BooleanConstructor;
        default: boolean;
    };
    center: BooleanConstructor;
    draggable: BooleanConstructor;
    overflow: BooleanConstructor;
    roundButton: {
        default: boolean;
        type: BooleanConstructor;
    };
    container: {
        type: StringConstructor;
        default: string;
    };
    boxType: {
        type: PropType<MessageBoxType>;
        default: string;
    };
}>> & {
    onAction?: ((...args: any[]) => any) | undefined;
    onVanish?: ((...args: any[]) => any) | undefined;
}, {
    center: boolean;
    overflow: boolean;
    container: string;
    showClose: boolean;
    draggable: boolean;
    closeOnClickModal: boolean;
    closeOnPressEscape: boolean;
    lockScroll: boolean;
    modal: boolean;
    closeOnHashChange: boolean;
    roundButton: boolean;
    boxType: MessageBoxType;
}>;
export default _default;
