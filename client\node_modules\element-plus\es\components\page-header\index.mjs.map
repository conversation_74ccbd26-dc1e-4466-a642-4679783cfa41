{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/page-header/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport PageHeader from './src/page-header.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPageHeader: SFCWithInstall<typeof PageHeader> =\n  withInstall(PageHeader)\nexport default ElPageHeader\n\nexport * from './src/page-header'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}