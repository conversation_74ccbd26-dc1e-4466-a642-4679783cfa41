import type { ExtractPropTypes } from 'vue';
export declare const carouselItemProps: {
    readonly name: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly label: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
};
export type CarouselItemProps = ExtractPropTypes<typeof carouselItemProps>;
