<template>
  <div class="categories-manage-container">
    <!-- 工具栏 -->
    <el-card class="toolbar-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索分类名称"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch" style="margin-left: 10px;">重置</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="openAddDialog">添加分类</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 分类统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ categoriesStats.total }}</div>
            <div class="stat-label">总分类数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ categoriesStats.active }}</div>
            <div class="stat-label">启用分类</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ categoriesStats.inactive }}</div>
            <div class="stat-label">禁用分类</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ categoriesStats.totalDishes }}</div>
            <div class="stat-label">菜品总数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分类列表 -->
    <el-card class="categories-list-card">
      <div class="view-toggle">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="card">卡片视图</el-radio-button>
          <el-radio-button label="table">表格视图</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="categories-grid">
        <el-card 
          v-for="category in filteredCategories" 
          :key="category.id" 
          class="category-card"
          :class="{ 'inactive': category.status === '禁用' }"
        >
          <div class="category-header">
            <div class="category-icon">
              <el-icon><Food /></el-icon>
            </div>
            <div class="category-status" :class="category.status === '启用' ? 'active' : 'inactive'">
              {{ category.status }}
            </div>
          </div>
          <div class="category-info">
            <h3 class="category-name">{{ category.name }}</h3>
            <p class="category-description">{{ category.description }}</p>
            <div class="category-stats">
              <div class="stat-item">
                <span class="stat-label">菜品数量:</span>
                <span class="stat-value">{{ category.dishCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">排序:</span>
                <span class="stat-value">{{ category.sort }}</span>
              </div>
            </div>
          </div>
          <div class="category-actions">
            <el-button size="small" @click="openEditDialog(category)">编辑</el-button>
            <el-button 
              size="small" 
              :type="category.status === '启用' ? 'warning' : 'success'"
              @click="toggleStatus(category)"
            >
              {{ category.status === '启用' ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="removeCategory(category.id)"
              :disabled="category.dishCount > 0"
            >
              删除
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 表格视图 -->
      <el-table 
        v-else 
        :data="filteredCategories" 
        style="width: 100%" 
        border 
        stripe
        class="categories-table"
      >
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="dishCount" label="菜品数量" width="100" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '启用' ? 'success' : 'info'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              :type="scope.row.status === '启用' ? 'warning' : 'success'"
              @click="toggleStatus(scope.row)"
            >
              {{ scope.row.status === '启用' ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="removeCategory(scope.row.id)"
              :disabled="scope.row.dishCount > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="totalCategories"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑分类弹窗 -->
    <el-dialog
      :title="editMode ? '编辑分类' : '添加分类'"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number 
            v-model="form.sort" 
            :min="0" 
            :precision="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="启用">启用</el-radio>
            <el-radio label="禁用">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCategory">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Food } from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const dialogVisible = ref(false)
const editMode = ref(false)
const viewMode = ref('card')
const formRef = ref(null)

// 表单数据
const form = ref({
  id: null,
  name: '',
  description: '',
  sort: 0,
  status: '启用'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ]
}

// 模拟分类数据
const categories = ref([
  {
    id: 1,
    name: '熟食',
    description: '各种熟制驴肉制品，包括五香、酱制、卤制等',
    dishCount: 4,
    sort: 1,
    status: '启用',
    createTime: '2023-12-01 10:30:00'
  },
  {
    id: 2,
    name: '主食',
    description: '以驴肉为主要原料的主食类产品',
    dishCount: 1,
    sort: 2,
    status: '启用',
    createTime: '2023-12-01 11:15:00'
  },
  {
    id: 3,
    name: '小吃',
    description: '休闲零食类驴肉制品',
    dishCount: 1,
    sort: 3,
    status: '启用',
    createTime: '2023-12-01 14:20:00'
  },
  {
    id: 4,
    name: '礼盒',
    description: '精美包装的礼品类驴肉制品',
    dishCount: 1,
    sort: 4,
    status: '启用',
    createTime: '2023-12-01 16:45:00'
  },
  {
    id: 5,
    name: '其他',
    description: '其他类型的驴肉制品',
    dishCount: 0,
    sort: 5,
    status: '禁用',
    createTime: '2023-12-01 18:30:00'
  }
])

// 计算属性
const filteredCategories = computed(() => {
  let result = categories.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(category => 
      category.name.includes(searchQuery.value) ||
      category.description.includes(searchQuery.value)
    )
  }

  return result
})

const totalCategories = computed(() => filteredCategories.value.length)

const categoriesStats = computed(() => {
  const total = categories.value.length
  const active = categories.value.filter(c => c.status === '启用').length
  const inactive = categories.value.filter(c => c.status === '禁用').length
  const totalDishes = categories.value.reduce((sum, c) => sum + c.dishCount, 0)
  
  return {
    total,
    active,
    inactive,
    totalDishes
  }
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const resetSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

const openAddDialog = () => {
  editMode.value = false
  form.value = {
    id: null,
    name: '',
    description: '',
    sort: 0,
    status: '启用'
  }
  dialogVisible.value = true
}

const openEditDialog = (category) => {
  editMode.value = true
  form.value = { ...category }
  dialogVisible.value = true
}

const saveCategory = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (editMode.value) {
      const idx = categories.value.findIndex(c => c.id === form.value.id)
      if (idx !== -1) {
        categories.value[idx] = { ...form.value }
        ElMessage.success('编辑成功')
      }
    } else {
      form.value.id = Date.now()
      form.value.createTime = new Date().toLocaleString()
      form.value.dishCount = 0
      categories.value.push({ ...form.value })
      ElMessage.success('添加成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    // 表单验证失败
  }
}

const toggleStatus = async (category) => {
  const newStatus = category.status === '启用' ? '禁用' : '启用'
  const action = category.status === '启用' ? '禁用' : '启用'
  
  try {
    await ElMessageBox.confirm(
      `确认要${action}该分类吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    category.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

const removeCategory = async (id) => {
  const category = categories.value.find(c => c.id === id)
  if (category && category.dishCount > 0) {
    ElMessage.warning('该分类下还有菜品，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确认要删除该分类吗？删除后无法恢复！',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const idx = categories.value.findIndex(c => c.id === id)
    if (idx !== -1) {
      categories.value.splice(idx, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

onMounted(() => {
  // 组件挂载后的初始化操作
})
</script>

<style scoped>
.categories-manage-container {
  padding: 20px;
}

.toolbar-card {
  margin-bottom: 20px;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.categories-list-card {
  margin-bottom: 20px;
}

.view-toggle {
  margin-bottom: 20px;
  text-align: right;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.category-card {
  transition: all 0.3s;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-card.inactive {
  opacity: 0.6;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.category-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.category-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  color: white;
}

.category-status.active {
  background-color: #67c23a;
}

.category-status.inactive {
  background-color: #909399;
}

.category-info {
  margin-bottom: 15px;
}

.category-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.category-description {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.category-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.stat-item .stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.category-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.categories-table {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style> 