{"version": 3, "file": "ajax.mjs", "sources": ["../../../../../../packages/components/upload/src/ajax.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { isArray, throwError } from '@element-plus/utils'\nimport type {\n  UploadProgressEvent,\n  UploadRequestHandler,\n  UploadRequestOptions,\n} from './upload'\n\nconst SCOPE = 'ElUpload'\n\nexport class UploadAjaxError extends Error {\n  name = 'UploadAjaxError'\n  status: number\n  method: string\n  url: string\n\n  constructor(message: string, status: number, method: string, url: string) {\n    super(message)\n    this.status = status\n    this.method = method\n    this.url = url\n  }\n}\n\nfunction getError(\n  action: string,\n  option: UploadRequestOptions,\n  xhr: XMLHttpRequest\n) {\n  let msg: string\n  if (xhr.response) {\n    msg = `${xhr.response.error || xhr.response}`\n  } else if (xhr.responseText) {\n    msg = `${xhr.responseText}`\n  } else {\n    msg = `fail to ${option.method} ${action} ${xhr.status}`\n  }\n\n  return new UploadAjaxError(msg, xhr.status, option.method, action)\n}\n\nfunction getBody(xhr: XMLHttpRequest): XMLHttpRequestResponseType {\n  const text = xhr.responseText || xhr.response\n  if (!text) {\n    return text\n  }\n\n  try {\n    return JSON.parse(text)\n  } catch {\n    return text\n  }\n}\n\nexport const ajaxUpload: UploadRequestHandler = (option) => {\n  if (typeof XMLHttpRequest === 'undefined')\n    throwError(SCOPE, 'XMLHttpRequest is undefined')\n\n  const xhr = new XMLHttpRequest()\n  const action = option.action\n\n  if (xhr.upload) {\n    xhr.upload.addEventListener('progress', (evt) => {\n      const progressEvt = evt as UploadProgressEvent\n      progressEvt.percent = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0\n      option.onProgress(progressEvt)\n    })\n  }\n\n  const formData = new FormData()\n  if (option.data) {\n    for (const [key, value] of Object.entries(option.data)) {\n      if (isArray(value) && value.length) formData.append(key, ...value)\n      else formData.append(key, value)\n    }\n  }\n  formData.append(option.filename, option.file, option.file.name)\n\n  xhr.addEventListener('error', () => {\n    option.onError(getError(action, option, xhr))\n  })\n\n  xhr.addEventListener('load', () => {\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(action, option, xhr))\n    }\n    option.onSuccess(getBody(xhr))\n  })\n\n  xhr.open(option.method, action, true)\n\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true\n  }\n\n  const headers = option.headers || {}\n  if (headers instanceof Headers) {\n    headers.forEach((value, key) => xhr.setRequestHeader(key, value))\n  } else {\n    for (const [key, value] of Object.entries(headers)) {\n      if (isNil(value)) continue\n      xhr.setRequestHeader(key, String(value))\n    }\n  }\n\n  xhr.send(formData)\n  return xhr\n}\n"], "names": [], "mappings": ";;;;AAEA,MAAM,KAAK,GAAG,UAAU,CAAC;AAClB,MAAM,eAAe,SAAS,KAAK,CAAC;AAC3C,EAAE,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE;AAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAClC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,GAAG;AACH,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE;AACvC,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE;AACpB,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClD,GAAG,MAAM,IAAI,GAAG,CAAC,YAAY,EAAE;AAC/B,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;AAChC,GAAG,MAAM;AACT,IAAI,GAAG,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACrE,CAAC;AACD,SAAS,OAAO,CAAC,GAAG,EAAE;AACtB,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,QAAQ,CAAC;AAChD,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI;AACN,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC5B,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACW,MAAC,UAAU,GAAG,CAAC,MAAM,KAAK;AACtC,EAAE,IAAI,OAAO,cAAc,KAAK,WAAW;AAC3C,IAAI,UAAU,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;AACrD,EAAE,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;AACnC,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC/B,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE;AAClB,IAAI,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK;AACrD,MAAM,MAAM,WAAW,GAAG,GAAG,CAAC;AAC9B,MAAM,WAAW,CAAC,OAAO,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;AAC7E,MAAM,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;AAClC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;AACnB,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAC5D,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM;AACxC,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;AACvC;AACA,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClE,EAAE,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AACtC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM;AACrC,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE;AAC/C,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,EAAE,IAAI,MAAM,CAAC,eAAe,IAAI,iBAAiB,IAAI,GAAG,EAAE;AAC1D,IAAI,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;AACvC,EAAE,IAAI,OAAO,YAAY,OAAO,EAAE;AAClC,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACtE,GAAG,MAAM;AACT,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACxD,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;AACtB,QAAQ,SAAS;AACjB,MAAM,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrB,EAAE,OAAO,GAAG,CAAC;AACb;;;;"}