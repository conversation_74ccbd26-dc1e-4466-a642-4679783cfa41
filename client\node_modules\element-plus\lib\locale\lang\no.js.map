{"version": 3, "file": "no.js", "sources": ["../../../../../packages/locale/lang/no.ts"], "sourcesContent": ["export default {\n  name: 'no',\n  el: {\n    breadcrumb: {\n      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    colorpicker: {\n      confirm: 'Bekreft',\n      clear: 'Tøm',\n      defaultLabel: 'Fargevelger',\n      description: 'Nåværende farge {color}, velg ny farge med Enter-tasten',\n      alphaLabel: 'Velg verdi for gjennomsiktighet',\n    },\n    datepicker: {\n      now: 'Nå',\n      today: 'I dag',\n      cancel: 'Avbryt',\n      clear: 'Tøm',\n      confirm: 'Bekreft',\n      dateTablePrompt: 'Bruk piltastene og Enter-tasten for å velge dato',\n      monthTablePrompt: 'Bruk piltastene og Enter-tasten for å velge måned',\n      yearTablePrompt: 'Bruk piltastene og Enter-tasten for å velge år',\n      selectedDate: 'Valgt dato',\n      selectDate: 'Velg dato',\n      selectTime: 'Velg tid',\n      startDate: 'Startdato',\n      startTime: 'Starttid',\n      endDate: 'Sluttdato',\n      endTime: 'Sluttid',\n      prevYear: '<PERSON>rige år',\n      nextYear: 'Neste år',\n      prevMonth: 'Forrige måned',\n      nextMonth: 'Neste måned',\n      year: 'År',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'Mars',\n      month4: 'April',\n      month5: 'Mai',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Desember',\n      weeks: {\n        sun: 'Søn',\n        mon: 'Man',\n        tue: 'Tir',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lør',\n      },\n      weeksFull: {\n        sun: 'Søndag',\n        mon: 'Mandag',\n        tue: 'Tirsdag',\n        wed: 'Onsdag',\n        thu: 'Torsdag',\n        fri: 'Fredag',\n        sat: 'Lørdag',\n      },\n      months: {\n        jan: 'Januar',\n        feb: 'Februar',\n        mar: 'Mars',\n        apr: 'April',\n        may: 'Mai',\n        jun: 'Juni',\n        jul: 'Juli',\n        aug: 'August',\n        sep: 'September',\n        oct: 'Oktober',\n        nov: 'November',\n        dec: 'Desember',\n      },\n    },\n    inputNumber: {\n      decrease: 'Minsk verdi',\n      increase: 'Øk verdi',\n    },\n    select: {\n      loading: 'Laster',\n      noMatch: 'Ingen treff',\n      noData: 'Ingen data',\n      placeholder: 'Velg',\n    },\n    dropdown: {\n      toggleDropdown: 'Vis/skjul nedtrekksmeny',\n    },\n    mention: {\n      loading: 'Laster',\n    },\n    cascader: {\n      noMatch: 'Ingen treff',\n      loading: 'Laster',\n      placeholder: 'Velg',\n      noData: 'Ingen data',\n    },\n    pagination: {\n      goto: 'Gå til',\n      pagesize: 'per side',\n      total: 'Totalt {total} elementer',\n      pageClassifier: 'side',\n      page: 'Side',\n      prev: 'Forrige side',\n      next: 'Neste side',\n      currentPage: 'Side {pager}',\n      prevPages: 'Forrige {pager} sider',\n      nextPages: 'Neste {pager} sider',\n      deprecationWarning:\n        'Du bruker noen foreldede metoder, se den offisielle dokumentasjonen for el-pagination',\n    },\n    dialog: {\n      close: 'Lukk denne dialogboksen',\n    },\n    drawer: {\n      close: 'Lukk denne dialogboksen',\n    },\n    messagebox: {\n      title: 'Varsel',\n      confirm: 'Bekreft',\n      cancel: 'Avbryt',\n      error: 'Ugyldig inndata!',\n      close: 'Lukk denne dialogboksen',\n    },\n    upload: {\n      deleteTip: 'Trykk delete for å slette',\n      delete: 'Slett',\n      preview: 'Vis bilde',\n      continue: 'Fortsett opplasting',\n    },\n    slider: {\n      defaultLabel: 'Glidebryter mellom {min} og {max}',\n      defaultRangeStartLabel: 'Velg startverdi',\n      defaultRangeEndLabel: 'Velg sluttverdi',\n    },\n    table: {\n      emptyText: 'Ingen data',\n      confirmFilter: 'Filtrer',\n      resetFilter: 'Tilbakestill',\n      clearFilter: 'Alle',\n      sumText: 'Sum',\n    },\n    tour: {\n      next: 'Neste',\n      previous: 'Forrige',\n      finish: 'Avslutt omvisning',\n    },\n    tree: {\n      emptyText: 'Ingen data',\n    },\n    transfer: {\n      noMatch: 'Ingen treff',\n      noData: 'Ingen data',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Skriv inn søkeinnhold',\n      noCheckedFormat: 'Totalt {total} elementer',\n      hasCheckedFormat: 'Valgt {checked}/{total} elementer',\n    },\n    image: {\n      error: 'Lasting mislyktes',\n    },\n    pageHeader: {\n      title: 'Tilbake',\n    },\n    popconfirm: {\n      confirmButtonText: 'Bekreft',\n      cancelButtonText: 'Avbryt',\n    },\n    carousel: {\n      leftArrow: 'Forrige bilde',\n      rightArrow: 'Neste bilde',\n      indicator: 'Bytt bilde til indeks {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,YAAY,EAAE,aAAa;AACjC,MAAM,WAAW,EAAE,+DAA+D;AAClF,MAAM,UAAU,EAAE,iCAAiC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,eAAe,EAAE,qDAAqD;AAC5E,MAAM,gBAAgB,EAAE,yDAAyD;AACjF,MAAM,eAAe,EAAE,sDAAsD;AAC7E,MAAM,YAAY,EAAE,YAAY;AAChC,MAAM,UAAU,EAAE,WAAW;AAC7B,MAAM,UAAU,EAAE,UAAU;AAC5B,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,WAAW;AACxB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,WAAW;AACxB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,WAAW;AACxB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,QAAQ,EAAE,aAAa;AAC7B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,MAAM;AACzB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,yBAAyB;AAC/C,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,cAAc,EAAE,MAAM;AAC5B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,uBAAuB;AACxC,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,kBAAkB,EAAE,uFAAuF;AACjH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,yBAAyB;AACtC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,yBAAyB;AACtC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,KAAK,EAAE,yBAAyB;AACtC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,qBAAqB;AACrC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,mCAAmC;AACvD,MAAM,sBAAsB,EAAE,iBAAiB;AAC/C,MAAM,oBAAoB,EAAE,iBAAiB;AAC7C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,SAAS;AAC9B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,MAAM,EAAE,mBAAmB;AACjC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,eAAe,EAAE,0BAA0B;AACjD,MAAM,gBAAgB,EAAE,mCAAmC;AAC3D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,SAAS;AAClC,MAAM,gBAAgB,EAAE,QAAQ;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,+BAA+B;AAChD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}