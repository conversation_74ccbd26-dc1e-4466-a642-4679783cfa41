{"version": 3, "file": "check-tag2.js", "sources": ["../../../../../../packages/components/check-tag/src/check-tag.vue"], "sourcesContent": ["<template>\n  <span :class=\"containerKls\" @click=\"handleChange\">\n    <slot />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\nimport { checkTagEmits, checkTagProps } from './check-tag'\n\ndefineOptions({\n  name: 'ElCheckTag',\n})\nconst props = defineProps(checkTagProps)\nconst emit = defineEmits(checkTagEmits)\n\nconst ns = useNamespace('check-tag')\nconst isDisabled = computed(() => props.disabled)\nconst containerKls = computed(() => [\n  ns.b(),\n  ns.is('checked', props.checked),\n  ns.is('disabled', isDisabled.value),\n  ns.m(props.type || 'primary'),\n])\n\nconst handleChange = () => {\n  if (isDisabled.value) return\n\n  const checked = !props.checked\n  emit(CHANGE_EVENT, checked)\n  emit('update:checked', checked)\n}\n</script>\n"], "names": ["useNamespace", "computed", "CHANGE_EVENT"], "mappings": ";;;;;;;;;;uCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,WAAW,CAAA,CAAA;AACnC,IAAA,MAAM,UAAa,GAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,QAAQ,CAAA,CAAA;AAChD,IAAM,MAAA,YAAA,GAAeA,aAAS,MAAM;AAAA,MAClC,GAAG,CAAE,EAAA;AAAA,MACL,EAAG,CAAA,EAAA,CAAG,SAAW,EAAA,KAAA,CAAM,OAAO,CAAA;AAAA,MAC9B,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,UAAA,CAAW,KAAK,CAAA;AAAA,MAClC,EAAG,CAAA,CAAA,CAAE,KAAM,CAAA,IAAA,IAAQ,SAAS,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,IAAI,WAAW,KAAO;AAEtB,QAAM,OAAA;AACN,MAAA,sBAA0B,CAAA,OAAA,CAAA;AAC1B,MAAA,IAAA,CAAKC,2BAAyB,CAAA,CAAA;AAAA,MAChC,IAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;;;;"}