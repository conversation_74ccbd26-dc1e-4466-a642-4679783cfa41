{"version": 3, "file": "use-slide.js", "sources": ["../../../../../../../packages/components/slider/src/composables/use-slide.ts"], "sourcesContent": ["import { computed, nextTick, ref, shallowRef } from 'vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useFormItem } from '@element-plus/components/form'\nimport type { CSSProperties, Ref, SetupContext } from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { SliderEmits, SliderInitData, SliderProps } from '../slider'\nimport type { ButtonRefs, SliderButtonInstance } from '../button'\n\nexport const useSlide = (\n  props: SliderProps,\n  initData: SliderInitData,\n  emit: SetupContext<SliderEmits>['emit']\n) => {\n  const { form: elForm, formItem: elFormItem } = useFormItem()\n\n  const slider = shallowRef<HTMLElement>()\n\n  const firstButton = ref<SliderButtonInstance>()\n\n  const secondButton = ref<SliderButtonInstance>()\n\n  const buttonRefs: ButtonRefs = {\n    firstButton,\n    secondButton,\n  }\n\n  const sliderDisabled = computed(() => {\n    return props.disabled || elForm?.disabled || false\n  })\n\n  const minValue = computed(() => {\n    return Math.min(initData.firstValue, initData.secondValue)\n  })\n\n  const maxValue = computed(() => {\n    return Math.max(initData.firstValue, initData.secondValue)\n  })\n\n  const barSize = computed(() => {\n    return props.range\n      ? `${\n          (100 * (maxValue.value - minValue.value)) / (props.max - props.min)\n        }%`\n      : `${\n          (100 * (initData.firstValue - props.min)) / (props.max - props.min)\n        }%`\n  })\n\n  const barStart = computed(() => {\n    return props.range\n      ? `${(100 * (minValue.value - props.min)) / (props.max - props.min)}%`\n      : '0%'\n  })\n\n  const runwayStyle = computed<CSSProperties>(() => {\n    return props.vertical ? { height: props.height } : {}\n  })\n\n  const barStyle = computed<CSSProperties>(() => {\n    return props.vertical\n      ? {\n          height: barSize.value,\n          bottom: barStart.value,\n        }\n      : {\n          width: barSize.value,\n          left: barStart.value,\n        }\n  })\n\n  const resetSize = () => {\n    if (slider.value) {\n      initData.sliderSize =\n        slider.value[`client${props.vertical ? 'Height' : 'Width'}`]\n    }\n  }\n\n  const getButtonRefByPercent = (\n    percent: number\n  ): Ref<SliderButtonInstance | undefined> => {\n    const targetValue = props.min + (percent * (props.max - props.min)) / 100\n    if (!props.range) {\n      return firstButton\n    }\n    let buttonRefName: 'firstButton' | 'secondButton'\n    if (\n      Math.abs(minValue.value - targetValue) <\n      Math.abs(maxValue.value - targetValue)\n    ) {\n      buttonRefName =\n        initData.firstValue < initData.secondValue\n          ? 'firstButton'\n          : 'secondButton'\n    } else {\n      buttonRefName =\n        initData.firstValue > initData.secondValue\n          ? 'firstButton'\n          : 'secondButton'\n    }\n    return buttonRefs[buttonRefName]\n  }\n\n  const setPosition = (\n    percent: number\n  ): Ref<SliderButtonInstance | undefined> => {\n    const buttonRef = getButtonRefByPercent(percent)\n    buttonRef.value!.setPosition(percent)\n    return buttonRef\n  }\n\n  const setFirstValue = (firstValue: number | undefined) => {\n    initData.firstValue = firstValue ?? props.min\n    _emit(\n      props.range ? [minValue.value, maxValue.value] : firstValue ?? props.min\n    )\n  }\n\n  const setSecondValue = (secondValue: number) => {\n    initData.secondValue = secondValue\n\n    if (props.range) {\n      _emit([minValue.value, maxValue.value])\n    }\n  }\n\n  const _emit = (val: Arrayable<number>) => {\n    emit(UPDATE_MODEL_EVENT, val)\n    emit(INPUT_EVENT, val)\n  }\n\n  const emitChange = async () => {\n    await nextTick()\n    emit(\n      CHANGE_EVENT,\n      props.range ? [minValue.value, maxValue.value] : props.modelValue\n    )\n  }\n\n  const handleSliderPointerEvent = (\n    event: MouseEvent | TouchEvent\n  ): Ref<SliderButtonInstance | undefined> | undefined => {\n    if (sliderDisabled.value || initData.dragging) return\n    resetSize()\n    let newPercent = 0\n    if (props.vertical) {\n      const clientY =\n        (event as TouchEvent).touches?.item(0)?.clientY ??\n        (event as MouseEvent).clientY\n      const sliderOffsetBottom = slider.value!.getBoundingClientRect().bottom\n      newPercent = ((sliderOffsetBottom - clientY) / initData.sliderSize) * 100\n    } else {\n      const clientX =\n        (event as TouchEvent).touches?.item(0)?.clientX ??\n        (event as MouseEvent).clientX\n      const sliderOffsetLeft = slider.value!.getBoundingClientRect().left\n      newPercent = ((clientX - sliderOffsetLeft) / initData.sliderSize) * 100\n    }\n    if (newPercent < 0 || newPercent > 100) return\n    return setPosition(newPercent)\n  }\n\n  const onSliderWrapperPrevent = (event: TouchEvent) => {\n    if (\n      buttonRefs['firstButton'].value?.dragging ||\n      buttonRefs['secondButton'].value?.dragging\n    ) {\n      event.preventDefault()\n    }\n  }\n\n  const onSliderDown = async (event: MouseEvent | TouchEvent) => {\n    const buttonRef = handleSliderPointerEvent(event)\n    if (buttonRef) {\n      await nextTick()\n      buttonRef.value!.onButtonDown(event)\n    }\n  }\n\n  const onSliderClick = (event: MouseEvent | TouchEvent) => {\n    const buttonRef = handleSliderPointerEvent(event)\n    if (buttonRef) {\n      emitChange()\n    }\n  }\n\n  const onSliderMarkerDown = (position: number) => {\n    if (sliderDisabled.value || initData.dragging) return\n    const buttonRef = setPosition(position)\n    if (buttonRef) {\n      emitChange()\n    }\n  }\n\n  return {\n    elFormItem,\n    slider,\n    firstButton,\n    secondButton,\n    sliderDisabled,\n    minValue,\n    maxValue,\n    runwayStyle,\n    barStyle,\n    resetSize,\n    setPosition,\n    emitChange,\n    onSliderWrapperPrevent,\n    onSliderClick,\n    onSliderDown,\n    onSliderMarkerDown,\n    setFirstValue,\n    setSecondValue,\n  }\n}\n"], "names": ["useFormItem", "shallowRef", "ref", "computed", "UPDATE_MODEL_EVENT", "INPUT_EVENT", "nextTick", "CHANGE_EVENT"], "mappings": ";;;;;;;;AAOY,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,KAAK;AACnD,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAGA,uBAAW,EAAE,CAAC;AAC/D,EAAE,MAAM,MAAM,GAAGC,cAAU,EAAE,CAAC;AAC9B,EAAE,MAAM,WAAW,GAAGC,OAAG,EAAE,CAAC;AAC5B,EAAE,MAAM,YAAY,GAAGA,OAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAGC,YAAQ,CAAC,MAAM;AACxC,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/D,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/D,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAGA,YAAQ,CAAC,MAAM;AACjC,IAAI,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3K,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACnG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM;AACrC,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG;AAC5B,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK;AAC3B,MAAM,MAAM,EAAE,QAAQ,CAAC,KAAK;AAC5B,KAAK,GAAG;AACR,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK;AAC1B,MAAM,IAAI,EAAE,QAAQ,CAAC,KAAK;AAC1B,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;AACtB,MAAM,QAAQ,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACzF,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,qBAAqB,GAAG,CAAC,OAAO,KAAK;AAC7C,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5E,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AACtB,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,aAAa,CAAC;AACtB,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,EAAE;AACzF,MAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,WAAW,GAAG,aAAa,GAAG,cAAc,CAAC;AAClG,KAAK,MAAM;AACX,MAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,WAAW,GAAG,aAAa,GAAG,cAAc,CAAC;AAClG,KAAK;AACL,IAAI,OAAO,UAAU,CAAC,aAAa,CAAC,CAAC;AACrC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,OAAO,KAAK;AACnC,IAAI,MAAM,SAAS,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACzC,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,UAAU,KAAK;AACxC,IAAI,QAAQ,CAAC,UAAU,GAAG,UAAU,IAAI,IAAI,GAAG,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;AACtE,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,UAAU,IAAI,IAAI,GAAG,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACxG,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,WAAW,KAAK;AAC1C,IAAI,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AACvC,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AACrB,MAAM,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK;AACzB,IAAI,IAAI,CAACC,wBAAkB,EAAE,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,CAACC,iBAAW,EAAE,GAAG,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,YAAY;AACjC,IAAI,MAAMC,YAAQ,EAAE,CAAC;AACrB,IAAI,IAAI,CAACC,kBAAY,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1F,GAAG,CAAC;AACJ,EAAE,MAAM,wBAAwB,GAAG,CAAC,KAAK,KAAK;AAC9C,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,IAAI,IAAI,cAAc,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ;AACjD,MAAM,OAAO;AACb,IAAI,SAAS,EAAE,CAAC;AAChB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;AAClJ,MAAM,MAAM,kBAAkB,GAAG,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,MAAM,CAAC;AAC7E,MAAM,UAAU,GAAG,CAAC,kBAAkB,GAAG,OAAO,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;AAC9E,KAAK,MAAM;AACX,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;AAClJ,MAAM,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;AACzE,MAAM,UAAU,GAAG,CAAC,OAAO,GAAG,gBAAgB,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,CAAC;AAC5E,KAAK;AACL,IAAI,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,GAAG;AAC1C,MAAM,OAAO;AACb,IAAI,OAAO,WAAW,CAAC,UAAU,CAAC,CAAC;AACnC,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAG,CAAC,KAAK,KAAK;AAC5C,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE;AAC7J,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,OAAO,KAAK,KAAK;AACxC,IAAI,MAAM,SAAS,GAAG,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACtD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAMD,YAAQ,EAAE,CAAC;AACvB,MAAM,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,MAAM,SAAS,GAAG,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACtD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,QAAQ,KAAK;AAC3C,IAAI,IAAI,cAAc,CAAC,KAAK,IAAI,QAAQ,CAAC,QAAQ;AACjD,MAAM,OAAO;AACb,IAAI,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC5C,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,sBAAsB;AAC1B,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}