{"version": 3, "file": "img-empty.mjs", "sources": ["../../../../../../packages/components/empty/src/img-empty.vue"], "sourcesContent": ["<template>\n  <svg\n    viewBox=\"0 0 79 86\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n  >\n    <defs>\n      <linearGradient\n        :id=\"`linearGradient-1-${id}`\"\n        x1=\"38.8503086%\"\n        y1=\"0%\"\n        x2=\"61.1496914%\"\n        y2=\"100%\"\n      >\n        <stop\n          :stop-color=\"`var(${ns.cssVarBlockName('fill-color-1')})`\"\n          offset=\"0%\"\n        />\n        <stop\n          :stop-color=\"`var(${ns.cssVarBlockName('fill-color-4')})`\"\n          offset=\"100%\"\n        />\n      </linearGradient>\n      <linearGradient\n        :id=\"`linearGradient-2-${id}`\"\n        x1=\"0%\"\n        y1=\"9.5%\"\n        x2=\"100%\"\n        y2=\"90.5%\"\n      >\n        <stop\n          :stop-color=\"`var(${ns.cssVarBlockName('fill-color-1')})`\"\n          offset=\"0%\"\n        />\n        <stop\n          :stop-color=\"`var(${ns.cssVarBlockName('fill-color-6')})`\"\n          offset=\"100%\"\n        />\n      </linearGradient>\n      <rect :id=\"`path-3-${id}`\" x=\"0\" y=\"0\" width=\"17\" height=\"36\" />\n    </defs>\n    <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n      <g transform=\"translate(-1268.000000, -535.000000)\">\n        <g transform=\"translate(1268.000000, 535.000000)\">\n          <path\n            d=\"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z\"\n            :fill=\"`var(${ns.cssVarBlockName('fill-color-3')})`\"\n          />\n          <polygon\n            :fill=\"`var(${ns.cssVarBlockName('fill-color-7')})`\"\n            transform=\"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) \"\n            points=\"13 58 53 58 42 45 2 45\"\n          />\n          <g\n            transform=\"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)\"\n          >\n            <polygon\n              :fill=\"`var(${ns.cssVarBlockName('fill-color-7')})`\"\n              transform=\"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) \"\n              points=\"2.84078316e-14 3 18 3 23 7 5 7\"\n            />\n            <polygon\n              :fill=\"`var(${ns.cssVarBlockName('fill-color-5')})`\"\n              points=\"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43\"\n            />\n            <rect\n              :fill=\"`url(#linearGradient-1-${id})`\"\n              transform=\"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) \"\n              x=\"38\"\n              y=\"7\"\n              width=\"17\"\n              height=\"36\"\n            />\n            <polygon\n              :fill=\"`var(${ns.cssVarBlockName('fill-color-2')})`\"\n              transform=\"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) \"\n              points=\"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12\"\n            />\n          </g>\n          <rect\n            :fill=\"`url(#linearGradient-2-${id})`\"\n            x=\"13\"\n            y=\"45\"\n            width=\"40\"\n            height=\"36\"\n          />\n          <g transform=\"translate(53.000000, 45.000000)\">\n            <use\n              :fill=\"`var(${ns.cssVarBlockName('fill-color-8')})`\"\n              transform=\"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) \"\n              :xlink:href=\"`#path-3-${id}`\"\n            />\n            <polygon\n              :fill=\"`var(${ns.cssVarBlockName('fill-color-9')})`\"\n              :mask=\"`url(#mask-4-${id})`\"\n              transform=\"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) \"\n              points=\"7 0 24 0 20 18 7 16.5\"\n            />\n          </g>\n          <polygon\n            :fill=\"`var(${ns.cssVarBlockName('fill-color-2')})`\"\n            transform=\"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) \"\n            points=\"62 45 79 45 70 58 53 58\"\n          />\n        </g>\n      </g>\n    </g>\n  </svg>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useId, useNamespace } from '@element-plus/hooks'\n\ndefineOptions({\n  name: 'ImgEmpty',\n})\n\nconst ns = useNamespace('empty')\nconst id = useId()\n</script>\n"], "names": [], "mappings": ";;;;;mCAkHc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAA,MAAM,KAAK,KAAM,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}