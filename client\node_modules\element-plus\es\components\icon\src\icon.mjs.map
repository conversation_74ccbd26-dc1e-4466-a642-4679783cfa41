{"version": 3, "file": "icon.mjs", "sources": ["../../../../../../packages/components/icon/src/icon.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Icon from './icon.vue'\n\nexport const iconProps = buildProps({\n  /**\n   * @description SVG icon size, size x size\n   */\n  size: {\n    type: definePropType<number | string>([Number, String]),\n  },\n  /**\n   * @description SVG tag's fill attribute\n   */\n  color: {\n    type: String,\n  },\n} as const)\nexport type IconProps = ExtractPropTypes<typeof iconProps>\nexport type IconInstance = InstanceType<typeof Icon> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,CAAC;;;;"}